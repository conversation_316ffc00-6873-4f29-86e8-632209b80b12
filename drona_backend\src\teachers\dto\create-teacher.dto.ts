import { IsString, <PERSON><PERSON><PERSON>Empt<PERSON>, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateTeacherDto {
  @ApiProperty({
    description: 'Teacher full name',
    example: '<PERSON>',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Teacher email address',
    example: '<EMAIL>',
    required: true,
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiPropertyOptional({
    description: 'Teacher phone number',
    example: '+****************',
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Department the teacher belongs to',
    example: 'Computer Science',
  })
  @IsString()
  @IsOptional()
  department?: string;

  @ApiPropertyOptional({
    description: 'Teacher designation/title',
    example: 'Associate Professor',
  })
  @IsString()
  @IsOptional()
  designation?: string;

  @ApiPropertyOptional({
    description: 'URL to teacher profile image',
    example: 'https://example.com/images/teacher-profile.jpg',
  })
  @IsString()
  @IsOptional()
  profileImageUrl?: string;
}
