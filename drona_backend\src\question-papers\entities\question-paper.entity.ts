import { ApiProperty } from '@nestjs/swagger';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Question } from '../../questions/entities/question.entity';

@Entity()
export class QuestionPaper {
  @ApiProperty({
    description: 'The unique identifier of the question paper',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The title of the question paper',
    example: 'Mathematics Final Exam',
  })
  @Column()
  title: string;

  @ApiProperty({
    description: 'The description of the question paper',
    example: 'Final examination for Mathematics course',
    required: false,
  })
  @Column({ nullable: true })
  description: string;

  @ApiProperty({
    description: 'The maximum number of questions allowed in this paper',
    example: 10,
    required: false,
  })
  @Column({ default: 10 })
  maxQuestions: number;

  @ApiProperty({
    description: 'The user who created this question paper',
    type: () => User,
  })
  @ManyToOne(() => User, (user) => user.questionPapers)
  generatedBy: User;

  @ApiProperty({
    description: 'The questions in this question paper',
    type: [Question],
  })
  @OneToMany(() => Question, (question) => question.questionPaper)
  questions: Question[];

  @ApiProperty({
    description: 'The date when the question paper was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'The date when the question paper was last updated',
    example: '2023-01-01T00:00:00.000Z',
  })
  @UpdateDateColumn()
  updatedAt: Date;
}
