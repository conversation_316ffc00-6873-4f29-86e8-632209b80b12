import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from '../schema/user.schema';
import { College } from '../schema/college.schema';
import { CreateTeacherDto } from './dto/create-teacher.dto';
import { UpdateTeacherDto } from './dto/update-teacher.dto';

@Injectable()
export class TeachersService {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @InjectModel(College.name) private collegeModel: Model<College>,
  ) {}

  async addTeacherToCollege(
    collegeId: string,
    createTeacherDto: CreateTeacherDto,
  ): Promise<User> {
    // Check if college exists
    const college = await this.collegeModel.findById(collegeId).exec();
    if (!college) {
      throw new NotFoundException(`College with ID ${collegeId} not found`);
    }

    // Check if email already exists
    const existingTeacher = await this.userModel
      .findOne({ email: createTeacherDto.email })
      .exec();
    if (existingTeacher) {
      throw new BadRequestException(
        `User with email ${createTeacherDto.email} already exists`,
      );
    }

    // Create new teacher with proper field mapping
    const { name, ...otherFields } = createTeacherDto;

    // Split name into first and last name if possible
    const nameParts = name.trim().split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

    const teacher = new this.userModel({
      ...otherFields,
      displayName: name,
      firstName: firstName,
      lastName: lastName,
      role: 'teacher',
      collegeId: collegeId,
    });

    const savedTeacher = await teacher.save();

    // Add teacher email to college.teachers array if not already present
    if (!college.teachers.includes(createTeacherDto.email)) {
      college.teachers.push(createTeacherDto.email);
      await college.save();
    }

    // Populate college information
    await savedTeacher.populate('collegeId');

    return savedTeacher;
  }

  async findAllTeachersInCollege(collegeId: string): Promise<User[]> {
    // Check if college exists
    const college = await this.collegeModel.findById(collegeId).exec();
    if (!college) {
      throw new NotFoundException(`College with ID ${collegeId} not found`);
    }

    return this.userModel
      .find({ collegeId, role: 'teacher' })
      .populate('collegeId')
      .exec();
  }

  async findOne(id: string): Promise<User> {
    const teacher = await this.userModel
      .findOne({ _id: id, role: 'teacher' })
      .populate('collegeId')
      .exec();
    if (!teacher) {
      throw new NotFoundException(`Teacher with ID ${id} not found`);
    }
    return teacher;
  }

  async update(id: string, updateTeacherDto: UpdateTeacherDto): Promise<User> {
    const teacher = await this.userModel
      .findOne({ _id: id, role: 'teacher' })
      .exec();
    if (!teacher) {
      throw new NotFoundException(`Teacher with ID ${id} not found`);
    }

    // If email is being updated, check if it already exists and update college.teachers array
    if (updateTeacherDto.email && updateTeacherDto.email !== teacher.email) {
      const existingTeacher = await this.userModel
        .findOne({ email: updateTeacherDto.email })
        .exec();
      if (existingTeacher) {
        throw new BadRequestException(
          `User with email ${updateTeacherDto.email} already exists`,
        );
      }

      // Update college.teachers array - remove old email and add new email
      if (teacher.collegeId) {
        const college = await this.collegeModel
          .findById(teacher.collegeId)
          .exec();
        if (college) {
          // Remove old email
          college.teachers = college.teachers.filter(
            (email) => email !== teacher.email,
          );
          // Add new email if not already present
          if (!college.teachers.includes(updateTeacherDto.email)) {
            college.teachers.push(updateTeacherDto.email);
          }
          await college.save();
        }
      }
    }

    // Handle name field mapping for updates
    let updateData: any = { ...updateTeacherDto };
    if (updateTeacherDto.name) {
      const { name, ...otherFields } = updateTeacherDto;

      // Split name into first and last name if possible
      const nameParts = name.trim().split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';

      updateData = {
        ...otherFields,
        displayName: name,
        firstName: firstName,
        lastName: lastName,
      };
    }

    const updatedTeacher = await this.userModel
      .findByIdAndUpdate(id, updateData, { new: true })
      .populate('collegeId')
      .exec();

    if (!updatedTeacher) {
      throw new NotFoundException(`Teacher with ID ${id} not found`);
    }

    return updatedTeacher;
  }

  async remove(id: string): Promise<User> {
    const teacher = await this.userModel
      .findOne({ _id: id, role: 'teacher' })
      .populate('collegeId')
      .exec();
    if (!teacher) {
      throw new NotFoundException(`Teacher with ID ${id} not found`);
    }

    // Remove teacher email from college.teachers array
    if (teacher.collegeId) {
      const college = await this.collegeModel
        .findById(teacher.collegeId)
        .exec();
      if (college && college.teachers.includes(teacher.email)) {
        college.teachers = college.teachers.filter(
          (email) => email !== teacher.email,
        );
        await college.save();
      }
    }

    const deletedTeacher = await this.userModel
      .findByIdAndDelete(id)
      .populate('collegeId')
      .exec();
    if (!deletedTeacher) {
      throw new NotFoundException(`Teacher with ID ${id} not found`);
    }

    return deletedTeacher;
  }
}
