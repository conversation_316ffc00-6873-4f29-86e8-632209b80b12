"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const questions_service_1 = require("./questions.service");
const create_question_dto_1 = require("./dto/create-question.dto");
const update_question_dto_1 = require("./dto/update-question.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const filter_questions_dto_1 = require("./dto/filter-questions.dto");
const swagger_1 = require("@nestjs/swagger");
const question_schema_1 = require("../schema/question.schema");
const bulk_review_dto_1 = require("./dto/bulk-review.dto");
const bulk_upload_pdf_dto_1 = require("./dto/bulk-upload-pdf.dto");
const review_question_dto_1 = require("./dto/review-question.dto");
let QuestionsController = class QuestionsController {
    constructor(questionsService) {
        this.questionsService = questionsService;
    }
    create(createQuestionDto, images, req) {
        return this.questionsService.create(createQuestionDto, req.user, images);
    }
    findAll(filters, page, limit) {
        const pageNum = parseInt(page || '1', 10);
        const limitNum = Math.min(parseInt(limit || '20', 10), 100);
        return this.questionsService.findAll({
            ...filters,
            page: pageNum,
            limit: limitNum,
        });
    }
    findDuplicates(subjectId, limit) {
        const limitNum = parseInt(limit || '50', 10);
        return this.questionsService.findDuplicates({ subjectId, limit: limitNum });
    }
    removeQuestion(id) {
        return this.questionsService.removeQuestion(id);
    }
    findPendingReviews(subjectId, page, limit) {
        const pageNum = parseInt(page || '1', 10);
        const limitNum = Math.min(parseInt(limit || '20', 10), 100);
        return this.questionsService.findPendingReviews({
            subjectId,
            page: pageNum,
            limit: limitNum,
        });
    }
    findOne(id) {
        return this.questionsService.findOne(id);
    }
    update(id, updateQuestionDto, images) {
        return this.questionsService.update(id, updateQuestionDto, images);
    }
    remove(id) {
        return this.questionsService.remove(id);
    }
    async reviewQuestion(id, reviewDto, req) {
        return this.questionsService.reviewQuestion(id, reviewDto, req.user);
    }
    async bulkReviewQuestions(bulkReviewDto, req) {
        return this.questionsService.bulkReview(bulkReviewDto.questionIds, {
            status: bulkReviewDto.status,
            notes: bulkReviewDto.notes,
        }, req.user);
    }
    async bulkUploadPdf(file, uploadDto, req) {
        if (!file) {
            throw new Error('No file provided');
        }
        if (file.mimetype !== 'application/pdf') {
            throw new Error('Only PDF files are allowed');
        }
        return this.questionsService.bulkUploadFromPdf(file, uploadDto, req.user);
    }
    async getDebugCounts() {
        return this.questionsService.getDebugCounts();
    }
};
exports.QuestionsController = QuestionsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('images', 5, {
        limits: {
            fileSize: 100 * 1024 * 1024,
        },
        fileFilter: (req, file, callback) => {
            if (!file.mimetype.match(/^image\/(jpeg|jpg|png|webp)$/)) {
                return callback(new Error('Only image files are allowed'), false);
            }
            callback(null, true);
        },
    })),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new question with optional images',
        description: 'Creates a new question with large image support and duplicate content checking (super admin only). The system checks for duplicate questions by comparing normalized content (ignoring whitespaces, newlines, and case). Upload up to 5 images (up to 100MB each) along with question data. Images are automatically compressed to 2MB and stored in the database.',
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                content: { type: 'string', example: 'What is the capital of France?' },
                options: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['Paris', 'London', 'Berlin', 'Madrid'],
                },
                answer: { type: 'string', example: 'Paris' },
                subjectId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                topicId: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                difficulty: {
                    type: 'string',
                    enum: ['easy', 'medium', 'hard'],
                    example: 'medium',
                },
                type: {
                    type: 'string',
                    enum: ['multiple-choice', 'true-false', 'descriptive'],
                    example: 'multiple-choice',
                },
                tags: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['geography', 'capitals'],
                },
                images: {
                    type: 'array',
                    items: { type: 'string', format: 'binary' },
                    description: 'Image files (max 5, JPEG/PNG/WebP, up to 100MB each, compressed to 2MB and stored in database)',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Question created successfully',
        type: question_schema_1.Question,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - Invalid input data, image files, or duplicate question content',
        schema: {
            oneOf: [
                {
                    example: {
                        statusCode: 400,
                        message: 'A question with similar content already exists in the database (ID: 60d21b4667d0d8992e610c85). Please modify the question content to make it unique.',
                        error: 'Bad Request',
                    },
                },
                {
                    example: {
                        statusCode: 400,
                        message: 'Image compression failed: Unable to compress to 2MB target size',
                        error: 'Bad Request',
                    },
                },
            ],
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFiles)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_question_dto_1.CreateQuestionDto, Array, Object]),
    __metadata("design:returntype", void 0)
], QuestionsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all questions',
        description: 'Returns all questions with optional filtering and pagination (super admin only)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number (default: 1)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: 'Items per page (default: 20, max: 100)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns questions with pagination',
        schema: {
            type: 'object',
            properties: {
                questions: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            _id: { type: 'string' },
                            content: { type: 'string' },
                            options: {
                                type: 'array',
                                items: { type: 'string' },
                                example: ['Paris', 'London', 'Berlin', 'Madrid']
                            },
                            answer: { type: 'string' },
                            difficulty: { type: 'string' },
                            type: { type: 'string' },
                            reviewStatus: { type: 'string' },
                            status: { type: 'string' },
                            subject: {
                                type: 'object',
                                properties: {
                                    _id: { type: 'string' },
                                    name: { type: 'string' },
                                },
                            },
                            createdAt: { type: 'string', format: 'date-time' },
                        },
                    },
                },
                pagination: {
                    type: 'object',
                    properties: {
                        currentPage: { type: 'number' },
                        totalPages: { type: 'number' },
                        totalItems: { type: 'number' },
                        itemsPerPage: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [filter_questions_dto_1.FilterQuestionsDto, String, String]),
    __metadata("design:returntype", void 0)
], QuestionsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('duplicates'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Find duplicate questions',
        description: 'Returns a list of potential duplicate questions with optimized performance (super admin only)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'subjectId',
        required: false,
        description: 'Filter duplicates by subject ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: 'Limit number of duplicate groups (default: 50)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns potential duplicate questions',
        schema: {
            type: 'object',
            properties: {
                duplicateGroups: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            originalQuestion: {
                                type: 'object',
                                properties: {
                                    _id: { type: 'string' },
                                    content: { type: 'string' },
                                    subject: {
                                        type: 'object',
                                        properties: { name: { type: 'string' } },
                                    },
                                    college: {
                                        type: 'object',
                                        properties: { name: { type: 'string' } },
                                    },
                                },
                            },
                            duplicates: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        _id: { type: 'string' },
                                        content: { type: 'string' },
                                        similarity: { type: 'string' },
                                        subject: {
                                            type: 'object',
                                            properties: { name: { type: 'string' } },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                totalGroups: { type: 'number' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    __param(0, (0, common_1.Query)('subjectId')),
    __param(1, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", void 0)
], QuestionsController.prototype, "findDuplicates", null);
__decorate([
    (0, common_1.Delete)('delete/:id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete a question',
        description: 'Deletes a question by ID (super admin only)',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Question ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Question deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], QuestionsController.prototype, "removeQuestion", null);
__decorate([
    (0, common_1.Get)('pending-reviews'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get pending reviews',
        description: 'Get all questions pending review by super admin across all colleges with pagination and optimized response',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'subjectId',
        required: false,
        description: 'Filter by subject ID',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        required: false,
        description: 'Page number (default: 1)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        description: 'Items per page (default: 20, max: 100)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns pending questions with pagination',
        schema: {
            type: 'object',
            properties: {
                questions: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            _id: { type: 'string' },
                            content: { type: 'string' },
                            difficulty: { type: 'string' },
                            type: { type: 'string' },
                            subject: {
                                type: 'object',
                                properties: {
                                    _id: { type: 'string' },
                                    name: { type: 'string' },
                                },
                            },
                            createdAt: { type: 'string', format: 'date-time' },
                        },
                    },
                },
                pagination: {
                    type: 'object',
                    properties: {
                        currentPage: { type: 'number' },
                        totalPages: { type: 'number' },
                        totalItems: { type: 'number' },
                        itemsPerPage: { type: 'number' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    __param(0, (0, common_1.Query)('subjectId')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], QuestionsController.prototype, "findPendingReviews", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get a question by ID',
        description: 'Returns a question by ID (super admin only)',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Question ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns the question',
        type: question_schema_1.Question,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], QuestionsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('images', 5, {
        limits: {
            fileSize: 100 * 1024 * 1024,
        },
        fileFilter: (req, file, callback) => {
            if (!file.mimetype.match(/^image\/(jpeg|jpg|png|webp)$/)) {
                return callback(new Error('Only image files are allowed'), false);
            }
            callback(null, true);
        },
    })),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update a question with optional new images',
        description: 'Updates a question by ID with large image support and duplicate content checking (super admin only). The system checks for duplicate questions by comparing normalized content (ignoring whitespaces, newlines, and case) excluding the current question being updated. Images up to 100MB are supported, automatically compressed to 2MB and stored in database.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Question ID' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                content: { type: 'string', example: 'What is the capital of France?' },
                options: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['Paris', 'London', 'Berlin', 'Madrid'],
                },
                answer: { type: 'string', example: 'Paris' },
                subjectId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                topicId: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                difficulty: {
                    type: 'string',
                    enum: ['easy', 'medium', 'hard'],
                    example: 'medium',
                },
                type: {
                    type: 'string',
                    enum: ['multiple-choice', 'true-false', 'descriptive'],
                    example: 'multiple-choice',
                },
                tags: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['geography', 'capitals'],
                },
                images: {
                    type: 'array',
                    items: { type: 'string', format: 'binary' },
                    description: 'New image files to add (max 5, JPEG/PNG/WebP, up to 100MB each, compressed to 2MB and stored in database)',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Question updated successfully',
        type: question_schema_1.Question,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - Invalid input data, image files, or duplicate question content',
        schema: {
            oneOf: [
                {
                    example: {
                        statusCode: 400,
                        message: 'A question with similar content already exists in the database (ID: 60d21b4667d0d8992e610c85). Please modify the question content to make it unique.',
                        error: 'Bad Request',
                    },
                },
                {
                    example: {
                        statusCode: 400,
                        message: 'Image compression failed: Unable to compress to 2MB target size',
                        error: 'Bad Request',
                    },
                },
            ],
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFiles)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_question_dto_1.UpdateQuestionDto, Array]),
    __metadata("design:returntype", void 0)
], QuestionsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete a question',
        description: 'Deletes a question by ID (super admin only)',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Question ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Question deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], QuestionsController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':id/review'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Review a question',
        description: 'Review and approve/reject a question (super admin only)',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Question ID' }),
    (0, swagger_1.ApiBody)({
        type: review_question_dto_1.ReviewQuestionDto,
        description: 'Review data containing status and optional notes',
        examples: {
            approve: {
                summary: 'Approve a question',
                description: 'Example of approving a question with notes',
                value: {
                    status: 'approved',
                    notes: 'Question meets quality standards and is well-structured',
                },
            },
            reject: {
                summary: 'Reject a question',
                description: 'Example of rejecting a question with feedback',
                value: {
                    status: 'rejected',
                    notes: 'Question contains grammatical errors and unclear options',
                },
            },
            approveWithoutNotes: {
                summary: 'Approve without notes',
                description: 'Example of approving a question without additional notes',
                value: {
                    status: 'approved',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Question reviewed successfully',
        schema: {
            example: {
                message: 'Question reviewed successfully',
                status: 'approved',
                reviewNotes: 'Question meets quality standards',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, review_question_dto_1.ReviewQuestionDto, Object]),
    __metadata("design:returntype", Promise)
], QuestionsController.prototype, "reviewQuestion", null);
__decorate([
    (0, common_1.Patch)('bulk-review'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Bulk review multiple questions',
        description: 'Review multiple questions at once. Only superAdmin can perform this action.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Questions successfully reviewed',
        schema: {
            example: {
                message: 'Successfully reviewed 5 questions',
                reviewedCount: 5,
                status: 'approved',
                details: {
                    matchedCount: 5,
                    modifiedCount: 5,
                    upsertedCount: 0,
                    deletedCount: 0,
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad Request - Invalid input or already reviewed questions',
        schema: {
            example: {
                statusCode: 400,
                message: 'Some questions are already reviewed: 60d21b4667d0d8992e610c85',
                error: 'Bad Request',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Not Found - Questions not found',
        schema: {
            example: {
                statusCode: 404,
                message: 'Some questions were not found: 60d21b4667d0d8992e610c85',
                error: 'Not Found',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - User does not have superAdmin role',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bulk_review_dto_1.BulkReviewDto, Object]),
    __metadata("design:returntype", Promise)
], QuestionsController.prototype, "bulkReviewQuestions", null);
__decorate([
    (0, common_1.Post)('bulk-upload-pdf'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({
        summary: 'Bulk upload questions from PDF',
        description: 'Upload a PDF file and extract questions using Mistral AI OCR. Questions will be marked as in-review status.',
    }),
    (0, swagger_1.ApiBody)({
        description: 'PDF file and subject ID for bulk question upload',
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                    description: 'PDF file containing questions',
                },
                subjectId: {
                    type: 'string',
                    description: 'Subject ID for the questions',
                    example: '60d21b4667d0d8992e610c85',
                },
            },
            required: ['file', 'subjectId'],
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'PDF processed successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string' },
                questionsAdded: { type: 'number' },
                questionsFailed: { type: 'number' },
                questions: {
                    type: 'array',
                    items: { type: 'object' },
                },
                errors: {
                    type: 'array',
                    items: { type: 'string' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - Invalid file or missing parameters',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, bulk_upload_pdf_dto_1.BulkUploadPdfDto, Object]),
    __metadata("design:returntype", Promise)
], QuestionsController.prototype, "bulkUploadPdf", null);
__decorate([
    (0, common_1.Get)('debug/counts'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Debug endpoint to check question counts and database status',
        description: 'Returns debug information about questions in the database',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Debug information retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], QuestionsController.prototype, "getDebugCounts", null);
exports.QuestionsController = QuestionsController = __decorate([
    (0, swagger_1.ApiTags)('Questions'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('questions'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [questions_service_1.QuestionsService])
], QuestionsController);
//# sourceMappingURL=questions.controller.js.map