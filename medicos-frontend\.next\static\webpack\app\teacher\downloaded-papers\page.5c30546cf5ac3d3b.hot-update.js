"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/downloaded-papers/page",{

/***/ "(app-pages-browser)/./src/app/teacher/downloaded-papers/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/teacher/downloaded-papers/page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Breadcrumb */ \"(app-pages-browser)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Download,FileText,Hash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Download,FileText,Hash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Download,FileText,Hash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Download,FileText,Hash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Download,FileText,Hash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DownloadedPapersPage = ()=>{\n    _s();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [downloadingIds, setDownloadingIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Fetch question papers\n    const { data: questionPapers = [], isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)({\n        queryKey: [\n            'question-papers'\n        ],\n        queryFn: _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_4__.getQuestionPapers,\n        staleTime: 5 * 60 * 1000\n    });\n    // Calculate pagination\n    const totalPages = Math.ceil(questionPapers.length / pageSize);\n    const startIndex = (currentPage - 1) * pageSize;\n    const paginatedPapers = questionPapers.slice(startIndex, startIndex + pageSize);\n    // Handle download\n    const handleDownload = async (paper)=>{\n        try {\n            setDownloadingIds((prev)=>new Set(prev).add(paper._id));\n            // First, fetch the complete question paper details to ensure we have all data\n            console.log('Fetching complete question paper details for:', paper._id);\n            const fullQuestionPaper = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_4__.getQuestionPaper)(paper._id);\n            console.log('Full question paper data:', fullQuestionPaper);\n            // Check if the question paper has questions\n            if (!fullQuestionPaper.questions || fullQuestionPaper.questions.length === 0) {\n                throw new Error('This question paper does not contain any questions. It may have been created incorrectly.');\n            }\n            console.log(\"Question paper has \".concat(fullQuestionPaper.questions.length, \" questions. Proceeding with download...\"));\n            // Download with answers included\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_4__.downloadQuestionPaper)(paper._id, 'pdf', true);\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(paper.title.replace(/\\s+/g, '_'), \"_with_answers_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            console.log('Download completed successfully');\n        } catch (error) {\n            console.error('Download failed:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Failed to download the question paper. Please try again.';\n            alert(errorMessage);\n        } finally{\n            setDownloadingIds((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(paper._id);\n                return newSet;\n            });\n        }\n    };\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold text-black\",\n                            children: \"Downloaded Papers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            items: [\n                                {\n                                    label: 'Home',\n                                    href: '/teacher'\n                                },\n                                {\n                                    label: '...',\n                                    href: '#'\n                                },\n                                {\n                                    label: 'Downloaded Papers'\n                                }\n                            ],\n                            className: \"text-sm mt-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"Question Papers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: [\n                                                    questionPapers.length,\n                                                    \" total papers available for download\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>refetch(),\n                                            disabled: isLoading,\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isLoading ? 'Refreshing...' : 'Refresh'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Loading question papers...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 mb-4\",\n                                            children: \"Failed to load question papers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>refetch(),\n                                            variant: \"outline\",\n                                            children: \"Try Again\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, undefined) : questionPapers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 mb-2\",\n                                            children: \"No question papers found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Generate your first question paper to see it here\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"min-w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-200 bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Paper Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Subject\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 161,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Details\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-center font-medium text-gray-700\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 165,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: paginatedPapers.map((paper)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-100 hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center flex-shrink-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                    lineNumber: 175,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 174,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"font-medium text-gray-900 line-clamp-2\",\n                                                                                        children: paper.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 178,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                                                        children: [\n                                                                                            \"ID: \",\n                                                                                            paper._id.slice(-8)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 181,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 177,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                        children: paper.subjectId.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 190,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1 text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 199,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            paper.totalMarks,\n                                                                                            \" marks\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 200,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 198,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1 text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 203,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            paper.duration,\n                                                                                            \" min\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 204,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 202,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 196,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1 text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 212,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: formatDate(paper.createdAt)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 213,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(paper.status.toLowerCase() === 'active' ? 'bg-emerald-100 text-emerald-800' : 'bg-gray-100 text-gray-800'),\n                                                                        children: paper.status.charAt(0).toUpperCase() + paper.status.slice(1)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 219,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                            onClick: ()=>handleDownload(paper),\n                                                                            disabled: downloadingIds.has(paper._id),\n                                                                            className: \"bg-[#2563EB] hover:bg-[#2563EB]/90 text-white flex items-center gap-2 px-4 py-2\",\n                                                                            size: \"sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Download_FileText_Hash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    className: \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                    lineNumber: 237,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                downloadingIds.has(paper._id) ? 'Downloading...' : 'Download'\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                            lineNumber: 231,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, paper._id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-t border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_5__.Pagination, {\n                                            currentPage: currentPage,\n                                            totalPages: totalPages,\n                                            onPageChange: setCurrentPage,\n                                            pageSize: pageSize,\n                                            totalItems: questionPapers.length,\n                                            onPageSizeChange: setPageSize\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n            lineNumber: 91,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DownloadedPapersPage, \"rtLVZSNtDe4q9Z0F1wq6kyiIxTg=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery\n    ];\n});\n_c = DownloadedPapersPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadedPapersPage);\nvar _c;\n$RefreshReg$(_c, \"DownloadedPapersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/teacher/downloaded-papers/page.tsx\n"));

/***/ })

});