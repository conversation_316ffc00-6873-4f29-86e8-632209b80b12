"use client"

import { useState } from "react"
import type { FormData, SubjectConfig } from "../question-paper-wizard"
import { CustomDifficultyConfig } from "@/lib/api/questionPapers"
import { StepNavigation } from "../ui/step-navigation"
import { InfoMessage } from "../ui/info-message"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Minus, Plus } from "lucide-react"

type MultiSubjectConfigStepProps = {
  formData: FormData
  updateFormData: (data: Partial<FormData>) => void
  onNext: () => void
  onSkip: () => void
  onBack: () => void
  backDisabled: boolean
}

export function MultiSubjectConfigStep({ 
  formData, 
  updateFormData, 
  onNext, 
  onSkip, 
  onBack, 
  backDisabled 
}: MultiSubjectConfigStepProps) {
  const [activeTab, setActiveTab] = useState(formData.subjects[0] || "")

  // Initialize subject configs if they don't exist
  const initializeSubjectConfig = (subject: string): SubjectConfig => {
    if (formData.subjectConfigs[subject]) {
      return formData.subjectConfigs[subject]
    }
    
    return {
      subject,
      difficultyMode: "auto",
      difficultyLevels: {
        easyPercentage: 30,
        mediumPercentage: 50,
        hardPercentage: 20,
      },
      numberOfQuestions: 10,
      totalMarks: 40,
      topicId: undefined,
    }
  }

  // Update a specific subject's configuration
  const updateSubjectConfig = (subject: string, updates: Partial<SubjectConfig>) => {
    const currentConfig = formData.subjectConfigs[subject] || initializeSubjectConfig(subject)
    const updatedConfig = { ...currentConfig, ...updates }
    
    updateFormData({
      subjectConfigs: {
        ...formData.subjectConfigs,
        [subject]: updatedConfig
      }
    })
  }

  // Adjust difficulty percentage for a subject
  const adjustDifficulty = (subject: string, level: keyof CustomDifficultyConfig, amount: number) => {
    const config = formData.subjectConfigs[subject] || initializeSubjectConfig(subject)
    const newValue = Math.max(0, Math.min(100, config.difficultyLevels[level] + amount))
    
    updateSubjectConfig(subject, {
      difficultyLevels: {
        ...config.difficultyLevels,
        [level]: newValue,
      }
    })
  }

  // Calculate totals across all subjects
  const calculateTotals = () => {
    let totalQuestions = 0
    let totalMarks = 0
    
    formData.subjects.forEach(subject => {
      const config = formData.subjectConfigs[subject] || initializeSubjectConfig(subject)
      totalQuestions += config.numberOfQuestions
      totalMarks += config.totalMarks
    })
    
    return { totalQuestions, totalMarks }
  }

  const { totalQuestions, totalMarks } = calculateTotals()

  // Validation
  const isValid = formData.subjects.every(subject => {
    const config = formData.subjectConfigs[subject] || initializeSubjectConfig(subject)
    const difficultySum = config.difficultyLevels.easyPercentage + 
                         config.difficultyLevels.mediumPercentage + 
                         config.difficultyLevels.hardPercentage
    return difficultySum === 100 && config.numberOfQuestions > 0 && config.totalMarks > 0
  })

  // Initialize configs for all subjects if not already done
  if (formData.subjects.length > 0 && Object.keys(formData.subjectConfigs).length === 0) {
    const initialConfigs: Record<string, SubjectConfig> = {}
    formData.subjects.forEach(subject => {
      initialConfigs[subject] = initializeSubjectConfig(subject)
    })
    updateFormData({ subjectConfigs: initialConfigs })
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Configure Each Subject</h2>
        <p className="text-gray-500">
          Set difficulty levels, number of questions, and marks for each selected subject
        </p>
      </div>

      {/* Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Paper Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{formData.subjects.length}</div>
              <div className="text-sm text-gray-500">Subjects</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{totalQuestions}</div>
              <div className="text-sm text-gray-500">Total Questions</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{totalMarks}</div>
              <div className="text-sm text-gray-500">Total Marks</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{formData.duration}</div>
              <div className="text-sm text-gray-500">Duration (min)</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Subject Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-4">
          {formData.subjects.map((subject) => (
            <TabsTrigger key={subject} value={subject} className="text-sm">
              {subject}
              {formData.subjectConfigs[subject] && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {formData.subjectConfigs[subject].numberOfQuestions}Q
                </Badge>
              )}
            </TabsTrigger>
          ))}
        </TabsList>

        {formData.subjects.map((subject) => {
          const config = formData.subjectConfigs[subject] || initializeSubjectConfig(subject)
          const difficultySum = config.difficultyLevels.easyPercentage + 
                               config.difficultyLevels.mediumPercentage + 
                               config.difficultyLevels.hardPercentage

          return (
            <TabsContent key={subject} value={subject} className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{subject} Configuration</span>
                    <Badge variant={difficultySum === 100 ? "default" : "destructive"}>
                      {difficultySum === 100 ? "Valid" : `${difficultySum}% (Need 100%)`}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Basic Configuration */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`questions-${subject}`}>Number of Questions</Label>
                      <Input
                        id={`questions-${subject}`}
                        type="number"
                        min="1"
                        max="200"
                        value={config.numberOfQuestions}
                        onChange={(e) => updateSubjectConfig(subject, { 
                          numberOfQuestions: parseInt(e.target.value) || 1 
                        })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`marks-${subject}`}>Total Marks</Label>
                      <Input
                        id={`marks-${subject}`}
                        type="number"
                        min="1"
                        value={config.totalMarks}
                        onChange={(e) => updateSubjectConfig(subject, { 
                          totalMarks: parseInt(e.target.value) || 1 
                        })}
                      />
                    </div>
                  </div>

                  {/* Difficulty Configuration */}
                  <div className="space-y-4">
                    <Label>Difficulty Distribution</Label>
                    <div className="grid grid-cols-3 gap-4">
                      {/* Easy */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Easy</Label>
                        <div className="flex items-center rounded-sm border border-gray-200 p-1">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => adjustDifficulty(subject, "easyPercentage", -5)}
                            className="h-8 w-8 rounded-sm border-gray-200"
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <span className="flex-1 text-center font-medium">
                            {config.difficultyLevels.easyPercentage}%
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => adjustDifficulty(subject, "easyPercentage", 5)}
                            className="h-8 w-8 rounded-sm border-gray-200"
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Medium */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Medium</Label>
                        <div className="flex items-center rounded-sm border border-gray-200 p-1">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => adjustDifficulty(subject, "mediumPercentage", -5)}
                            className="h-8 w-8 rounded-sm border-gray-200"
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <span className="flex-1 text-center font-medium">
                            {config.difficultyLevels.mediumPercentage}%
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => adjustDifficulty(subject, "mediumPercentage", 5)}
                            className="h-8 w-8 rounded-sm border-gray-200"
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Hard */}
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Hard</Label>
                        <div className="flex items-center rounded-sm border border-gray-200 p-1">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => adjustDifficulty(subject, "hardPercentage", -5)}
                            className="h-8 w-8 rounded-sm border-gray-200"
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <span className="flex-1 text-center font-medium">
                            {config.difficultyLevels.hardPercentage}%
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => adjustDifficulty(subject, "hardPercentage", 5)}
                            className="h-8 w-8 rounded-sm border-gray-200"
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )
        })}
      </Tabs>

      <StepNavigation 
        onNext={onNext} 
        onSkip={onSkip} 
        onBack={onBack}
        backDisabled={backDisabled}
        nextDisabled={!isValid} 
      />

      {!isValid && (
        <InfoMessage 
          message="Please ensure all subjects have valid configurations (difficulty percentages must sum to 100% and questions/marks must be greater than 0)." 
        />
      )}
    </div>
  )
}
