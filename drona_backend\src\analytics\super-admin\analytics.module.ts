import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';
import {
  UserActivity,
  UserActivitySchema,
} from '../../schema/user-activity.schema';
import { User, UserSchema } from '../../schema/user.schema';
import {
  QuestionPaper,
  QuestionPaperSchema,
} from '../../schema/question-paper.schema';
import { Question, QuestionSchema } from '../../schema/question.schema';
import { College, CollegeSchema } from '../../schema/college.schema';
import { Download, DownloadSchema } from '../../schema/download.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserActivity.name, schema: UserActivitySchema },
      { name: User.name, schema: UserSchema },
      { name: QuestionPaper.name, schema: QuestionPaperSchema },
      { name: Question.name, schema: QuestionSchema },
      { name: College.name, schema: CollegeSchema },
      { name: Download.name, schema: DownloadSchema },
    ]),
  ],
  controllers: [AnalyticsController],
  providers: [AnalyticsService],
})
export class AnalyticsModule {}
