import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { SubjectsService } from './subjects.service';
import { CreateSubjectDto } from './dto/create-subject.dto';
import { UpdateSubjectDto } from './dto/update-subject.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

/**
 * Subject management endpoints
 *
 * These endpoints allow super admins to create, manage, and delete subjects.
 * Subjects are used to categorize questions and question papers.
 */
@ApiTags('Subjects')
@ApiBearerAuth('JWT-auth')
@Controller('subjects')
@UseGuards(JwtAuthGuard, RolesGuard)
export class SubjectsController {
  private readonly logger = new Logger(SubjectsController.name);

  constructor(private readonly subjectsService: SubjectsService) {}

  /**
   * Create a new subject
   * @param createSubjectDto Subject data
   * @returns The created subject
   */
  @Post()
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Create a new subject',
    description: 'Creates a new subject (super admin only)',
  })
  @ApiCreatedResponse({
    description: 'Subject created successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        name: { type: 'string', example: 'Mathematics' },
        description: {
          type: 'string',
          example: 'Study of numbers, quantities, and shapes',
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Bad request - Invalid input data' })
  @ApiConflictResponse({
    description: 'Conflict - Subject with this name already exists',
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  create(@Body() createSubjectDto: CreateSubjectDto) {
    return this.subjectsService.create(createSubjectDto);
  }

  /**
   * Get all subjects
   * @returns List of all subjects
   */
  @Get()
  @ApiOperation({
    summary: 'Get all subjects',
    description: 'Returns all subjects',
  })
  @ApiOkResponse({
    description: 'Returns list of subjects',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          name: { type: 'string', example: 'Mathematics' },
          description: {
            type: 'string',
            example: 'Study of numbers, quantities, and shapes',
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  findAll() {
    return this.subjectsService.findAll();
  }

  /**
   * Get all subjects with their topics
   * @returns List of subjects with nested topics
   */
  @Get('with-topics')
  @ApiOperation({
    summary: 'Get all subjects with topics',
    description: 'Returns all subjects with their associated topics nested',
  })
  @ApiOkResponse({
    description: 'Returns list of subjects with topics',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          name: { type: 'string', example: 'Mathematics' },
          description: {
            type: 'string',
            example: 'Study of numbers, quantities, and shapes',
          },
          topics: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                name: { type: 'string', example: 'Calculus' },
                description: {
                  type: 'string',
                  example: 'Branch of mathematics dealing with limits',
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  findAllWithTopics() {
    return this.subjectsService.findAllWithTopics();
  }

  /**
   * Get a specific subject by ID
   * @param id Subject ID
   * @returns The subject
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get subject by ID',
    description: 'Returns a specific subject by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Subject ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'Returns the subject',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        name: { type: 'string', example: 'Mathematics' },
        description: {
          type: 'string',
          example: 'Study of numbers, quantities, and shapes',
        },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Subject not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  findOne(@Param('id') id: string) {
    return this.subjectsService.findOne(id);
  }

  /**
   * Update a subject
   * @param id Subject ID
   * @param updateSubjectDto Updated subject data
   * @returns The updated subject
   */
  @Patch(':id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Update subject',
    description: 'Updates a subject (super admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'Subject ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'Subject updated successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        name: { type: 'string', example: 'Mathematics' },
        description: {
          type: 'string',
          example: 'Study of numbers, quantities, and shapes',
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Bad request - Invalid input data' })
  @ApiConflictResponse({
    description: 'Conflict - Subject with this name already exists',
  })
  @ApiNotFoundResponse({ description: 'Subject not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  update(@Param('id') id: string, @Body() updateSubjectDto: UpdateSubjectDto) {
    return this.subjectsService.update(id, updateSubjectDto);
  }

  /**
   * Delete a subject
   * @param id Subject ID
   */
  @Delete(':id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Delete subject',
    description:
      'Deletes a subject (super admin only). Cannot delete if subject has associated topics.',
  })
  @ApiParam({
    name: 'id',
    description: 'Subject ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({ description: 'Subject deleted successfully' })
  @ApiConflictResponse({
    description: 'Conflict - Cannot delete subject with associated topics',
  })
  @ApiNotFoundResponse({ description: 'Subject not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  remove(@Param('id') id: string) {
    return this.subjectsService.remove(id);
  }
}
