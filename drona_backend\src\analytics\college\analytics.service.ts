// analytics.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  UserActivity,
  UserActivityDocument,
} from '../../schema/user-activity.schema';
import { User, UserDocument } from '../../schema/user.schema';
import {
  QuestionPaper,
  QuestionPaperDocument,
} from '../../schema/question-paper.schema';
import { Question, QuestionDocument } from '../../schema/question.schema';
import { Subject, SubjectDocument } from '../../schema/subject.schema';
import { Download, DownloadDocument } from '../../schema/download.schema';
import { AnalyticsCacheService } from '../../common/services';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);

  constructor(
    @InjectModel(UserActivity.name)
    private userActivityModel: Model<UserActivityDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(QuestionPaper.name)
    private questionPaperModel: Model<QuestionPaperDocument>,
    @InjectModel(Subject.name) private subjectModel: Model<SubjectDocument>,
    @InjectModel(Question.name)
    private readonly questionModel: Model<QuestionDocument>,
    @InjectModel(Download.name) private downloadModel: Model<DownloadDocument>,
    private readonly cacheService: AnalyticsCacheService,
  ) {}

  async getCollegeSummary(collegeId: string) {
    const cacheKey = `college_summary_${collegeId}`;

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log(
        `Computing college summary for collegeId: ${collegeId} (cache miss)`,
      );

      try {
        // Validate collegeId
        if (!collegeId || !Types.ObjectId.isValid(collegeId)) {
          throw new Error('Invalid collegeId');
        }

        // Convert string ID to ObjectId
        const collegeObjectId = new Types.ObjectId(collegeId);

        // Run all queries in parallel for better performance
        const [totalTeachers, activityCounts, totalDownloads] =
          await Promise.all([
            // Get total teachers count
            this.userModel.countDocuments({
              collegeId: collegeObjectId,
              role: 'teacher',
              status: 'active',
            }),

            // Get activity counts using aggregation (excluding downloads)
            this.userActivityModel
              .aggregate([
                {
                  $match: {
                    collegeId: collegeObjectId,
                  },
                },
                {
                  $group: {
                    _id: '$activityType',
                    count: { $sum: 1 },
                  },
                },
              ])
              .exec(),

            // Get downloads count from downloads collection
            this.downloadModel.countDocuments({
              collegeId: collegeObjectId,
            }),
          ]);

        // Process activity counts
        const activityMap = activityCounts.reduce((acc, curr) => {
          acc[curr._id] = curr.count;
          return acc;
        }, {});

        return {
          totalTeachers,
          totalPapersGenerated: activityMap['paper_generation'] || 0,
          totalDownloads,
        };
      } catch (error) {
        this.logger.error(
          `Error getting college summary: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    });
  }

  async getTeacherActivityLogs(
    collegeId: string,
    limit = 100,
    page = 1,
    filters: {
      teacherId?: string;
      startDate?: Date;
      endDate?: Date;
    } = {},
  ) {
    // Create a more specific cache key that includes filters
    const filterKey = JSON.stringify(filters);
    const cacheKey = `teacher_downloads_${collegeId}_${page}_${limit}_${Buffer.from(filterKey).toString('base64')}`;

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log(
        `Computing teacher download history for collegeId: ${collegeId}, page: ${page}, limit: ${limit}, filters: ${filterKey} (cache miss)`,
      );

      try {
        // Validate collegeId
        if (!collegeId || !Types.ObjectId.isValid(collegeId)) {
          throw new Error('Invalid collegeId');
        }

        // Convert string ID to ObjectId
        const collegeObjectId = new Types.ObjectId(collegeId);

        // Build the query for downloads
        const downloadQuery: any = { collegeId: collegeObjectId };

        // Add teacher ID filter
        if (filters.teacherId) {
          if (!Types.ObjectId.isValid(filters.teacherId)) {
            throw new Error('Invalid teacherId');
          }
          downloadQuery.userId = new Types.ObjectId(filters.teacherId);
        }

        // Add date range filters
        if (filters.startDate || filters.endDate) {
          downloadQuery.downloadDate = {};
          if (filters.startDate) {
            downloadQuery.downloadDate.$gte = filters.startDate;
          }
          if (filters.endDate) {
            downloadQuery.downloadDate.$lte = filters.endDate;
          }
        }

        // Get teachers with their download history grouped by subject
        const teacherDownloads = await this.downloadModel
          .aggregate([
            { $match: downloadQuery },
            {
              $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: '_id',
                as: 'teacher',
              },
            },
            { $unwind: '$teacher' },
            {
              $lookup: {
                from: 'questionpapers',
                localField: 'paperId',
                foreignField: '_id',
                as: 'questionPaper',
              },
            },
            { $unwind: '$questionPaper' },
            {
              $lookup: {
                from: 'subjects',
                localField: 'questionPaper.subjectId',
                foreignField: '_id',
                as: 'subject',
              },
            },
            { $unwind: '$subject' },
            {
              $group: {
                _id: {
                  teacherId: '$teacher._id',
                  teacherName: '$teacher.displayName',
                  teacherEmail: '$teacher.email',
                  subjectId: '$subject._id',
                  subjectName: '$subject.name',
                },
                downloadCount: { $sum: 1 },
                lastDownload: { $max: '$downloadDate' },
              },
            },
            {
              $group: {
                _id: {
                  teacherId: '$_id.teacherId',
                  teacherName: '$_id.teacherName',
                  teacherEmail: '$_id.teacherEmail',
                },
                subjectWiseDownloads: {
                  $push: {
                    subjectId: '$_id.subjectId',
                    subjectName: '$_id.subjectName',
                    downloadCount: '$downloadCount',
                    lastDownload: '$lastDownload',
                  },
                },
                totalDownloads: { $sum: '$downloadCount' },
              },
            },
            {
              $project: {
                _id: 0,
                teacherId: '$_id.teacherId',
                teacherName: '$_id.teacherName',
                teacherEmail: '$_id.teacherEmail',
                totalDownloads: 1,
                subjectWiseDownloads: {
                  $sortArray: {
                    input: '$subjectWiseDownloads',
                    sortBy: { subjectName: 1 },
                  },
                },
              },
            },
            { $sort: { teacherName: 1 } },
            { $skip: (page - 1) * limit },
            { $limit: limit },
          ])
          .exec();

        // Get total count for pagination
        const totalCountResult = await this.downloadModel
          .aggregate([
            { $match: downloadQuery },
            {
              $lookup: {
                from: 'users',
                localField: 'userId',
                foreignField: '_id',
                as: 'teacher',
              },
            },
            { $unwind: '$teacher' },
            {
              $group: {
                _id: '$teacher._id',
              },
            },
            {
              $count: 'total',
            },
          ])
          .exec();

        const totalCount =
          totalCountResult.length > 0 ? totalCountResult[0].total : 0;

        return {
          teachers: teacherDownloads,
          pagination: {
            total: totalCount,
            page,
            limit,
            pages: Math.ceil(totalCount / limit),
          },
          filters: filters,
        };
      } catch (error) {
        this.logger.error(
          `Error getting teacher download history: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    });
  }

  async getQuestionPaperDownloadStats(
    collegeId: string,
    filters: {
      startDate?: Date;
      endDate?: Date;
    } = {},
  ) {
    const filterKey = JSON.stringify(filters);
    const cacheKey = `question_paper_stats_${collegeId}_${Buffer.from(filterKey).toString('base64')}`;

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log(
        `Computing question paper stats for collegeId: ${collegeId}, filters: ${filterKey} (cache miss)`,
      );

      try {
        // Validate collegeId
        if (!collegeId || !Types.ObjectId.isValid(collegeId)) {
          throw new Error('Invalid collegeId');
        }

        // Convert string ID to ObjectId
        const collegeObjectId = new Types.ObjectId(collegeId);

        // Build date filter for question papers generated
        const paperGeneratedQuery: any = { collegeId: collegeObjectId };
        if (filters.startDate || filters.endDate) {
          paperGeneratedQuery.createdAt = {};
          if (filters.startDate) {
            paperGeneratedQuery.createdAt.$gte = filters.startDate;
          }
          if (filters.endDate) {
            paperGeneratedQuery.createdAt.$lte = filters.endDate;
          }
        }

        // Build date filter for downloads
        const downloadQuery: any = { collegeId: collegeObjectId };
        if (filters.startDate || filters.endDate) {
          downloadQuery.downloadDate = {};
          if (filters.startDate) {
            downloadQuery.downloadDate.$gte = filters.startDate;
          }
          if (filters.endDate) {
            downloadQuery.downloadDate.$lte = filters.endDate;
          }
        }

        // Get question papers generated per subject and date
        const papersGenerated = await this.questionPaperModel
          .aggregate([
            { $match: paperGeneratedQuery },
            {
              $group: {
                _id: {
                  subjectId: '$subjectId',
                  year: { $year: '$createdAt' },
                  month: { $month: '$createdAt' },
                  day: { $dayOfMonth: '$createdAt' },
                },
                count: { $sum: 1 },
              },
            },
            {
              $lookup: {
                from: 'subjects',
                localField: '_id.subjectId',
                foreignField: '_id',
                as: 'subject',
              },
            },
            { $unwind: '$subject' },
            {
              $project: {
                subjectId: '$_id.subjectId',
                subjectName: '$subject.name',
                date: {
                  $dateFromParts: {
                    year: '$_id.year',
                    month: '$_id.month',
                    day: '$_id.day',
                  },
                },
                generated: '$count',
                _id: 0,
              },
            },
          ])
          .exec();

        // Get question papers downloaded per subject and date
        const papersDownloaded = await this.downloadModel
          .aggregate([
            { $match: downloadQuery },
            {
              $lookup: {
                from: 'questionpapers',
                localField: 'paperId',
                foreignField: '_id',
                as: 'questionPaper',
              },
            },
            { $unwind: '$questionPaper' },
            {
              $group: {
                _id: {
                  subjectId: '$questionPaper.subjectId',
                  year: { $year: '$downloadDate' },
                  month: { $month: '$downloadDate' },
                  day: { $dayOfMonth: '$downloadDate' },
                },
                count: { $sum: 1 },
              },
            },
            {
              $lookup: {
                from: 'subjects',
                localField: '_id.subjectId',
                foreignField: '_id',
                as: 'subject',
              },
            },
            { $unwind: '$subject' },
            {
              $project: {
                subjectId: '$_id.subjectId',
                subjectName: '$subject.name',
                date: {
                  $dateFromParts: {
                    year: '$_id.year',
                    month: '$_id.month',
                    day: '$_id.day',
                  },
                },
                downloaded: '$count',
                _id: 0,
              },
            },
          ])
          .exec();

        // Create a comprehensive map with all dates in range
        const dateMap = new Map();

        // Generate all dates in the range if start and end dates are provided
        if (filters.startDate && filters.endDate) {
          const currentDate = new Date(filters.startDate);
          const endDate = new Date(filters.endDate);

          while (currentDate <= endDate) {
            const dateKey = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD format
            dateMap.set(dateKey, {
              date: new Date(currentDate),
              subjects: new Map(),
            });
            currentDate.setDate(currentDate.getDate() + 1);
          }
        }

        // Process generated papers data
        papersGenerated.forEach((item) => {
          const dateKey = item.date.toISOString().split('T')[0];
          const subjectKey = item.subjectId.toString();

          if (!dateMap.has(dateKey)) {
            dateMap.set(dateKey, {
              date: item.date,
              subjects: new Map(),
            });
          }

          if (!dateMap.get(dateKey).subjects.has(subjectKey)) {
            dateMap.get(dateKey).subjects.set(subjectKey, {
              subjectId: item.subjectId,
              subjectName: item.subjectName,
              generated: 0,
              downloaded: 0,
            });
          }

          dateMap.get(dateKey).subjects.get(subjectKey).generated =
            item.generated;
        });

        // Process downloaded papers data
        papersDownloaded.forEach((item) => {
          const dateKey = item.date.toISOString().split('T')[0];
          const subjectKey = item.subjectId.toString();

          if (!dateMap.has(dateKey)) {
            dateMap.set(dateKey, {
              date: item.date,
              subjects: new Map(),
            });
          }

          if (!dateMap.get(dateKey).subjects.has(subjectKey)) {
            dateMap.get(dateKey).subjects.set(subjectKey, {
              subjectId: item.subjectId,
              subjectName: item.subjectName,
              generated: 0,
              downloaded: 0,
            });
          }

          dateMap.get(dateKey).subjects.get(subjectKey).downloaded =
            item.downloaded;
        });

        // Convert to final format
        const dailyData = Array.from(dateMap.entries())
          .map(([dateKey, dayData]) => ({
            date: dayData.date,
            subjects: Array.from(dayData.subjects.values()).sort(
              (a: any, b: any) => a.subjectName.localeCompare(b.subjectName),
            ),
          }))
          .sort((a, b) => a.date.getTime() - b.date.getTime());

        return {
          data: dailyData,
          totalDays: dailyData.length,
          dateRange: {
            startDate: filters.startDate || null,
            endDate: filters.endDate || null,
          },
        };
      } catch (error) {
        this.logger.error(
          `Error getting question paper stats: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    });
  }

  async getSubjectWiseAnalytics(collegeId: string) {
    const cacheKey = `subject_wise_analytics_${collegeId}`;

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log(
        `Computing subject-wise analytics for collegeId: ${collegeId} (cache miss)`,
      );

      try {
        // Validate collegeId
        if (!collegeId || !Types.ObjectId.isValid(collegeId)) {
          throw new Error('Invalid collegeId');
        }

        // Convert string ID to ObjectId
        const collegeObjectId = new Types.ObjectId(collegeId);

        // Get question papers generated per subject
        const papersBySubject = await this.questionPaperModel
          .aggregate([
            { $match: { collegeId: collegeObjectId } },
            {
              $group: {
                _id: '$subjectId',
                questionPapersGenerated: { $sum: 1 },
              },
            },
            {
              $lookup: {
                from: 'subjects',
                localField: '_id',
                foreignField: '_id',
                as: 'subject',
              },
            },
            { $unwind: '$subject' },
            {
              $project: {
                subjectId: '$_id',
                subjectName: '$subject.name',
                questionPapersGenerated: 1,
                _id: 0,
              },
            },
          ])
          .exec();

        // Get questions generated per subject
        const questionsBySubject = await this.questionModel
          .aggregate([
            { $match: { collegeId: collegeObjectId, status: 'active' } },
            {
              $group: {
                _id: '$subjectId',
                questionsGenerated: { $sum: 1 },
              },
            },
            {
              $lookup: {
                from: 'subjects',
                localField: '_id',
                foreignField: '_id',
                as: 'subject',
              },
            },
            { $unwind: '$subject' },
            {
              $project: {
                subjectId: '$_id',
                subjectName: '$subject.name',
                questionsGenerated: 1,
                _id: 0,
              },
            },
          ])
          .exec();

        // Get question papers downloaded per subject
        const downloadsBySubject = await this.downloadModel
          .aggregate([
            { $match: { collegeId: collegeObjectId } },
            {
              $lookup: {
                from: 'questionpapers',
                localField: 'paperId',
                foreignField: '_id',
                as: 'questionPaper',
              },
            },
            { $unwind: '$questionPaper' },
            {
              $group: {
                _id: '$questionPaper.subjectId',
                questionPapersDownloaded: { $sum: 1 },
              },
            },
            {
              $lookup: {
                from: 'subjects',
                localField: '_id',
                foreignField: '_id',
                as: 'subject',
              },
            },
            { $unwind: '$subject' },
            {
              $project: {
                subjectId: '$_id',
                subjectName: '$subject.name',
                questionPapersDownloaded: 1,
                _id: 0,
              },
            },
          ])
          .exec();

        // Combine all data by subject
        const subjectMap = new Map();

        // Initialize with papers data
        papersBySubject.forEach((item) => {
          subjectMap.set(item.subjectId.toString(), {
            subjectId: item.subjectId,
            subjectName: item.subjectName,
            questionPapersGenerated: item.questionPapersGenerated,
            questionsGenerated: 0,
            questionPapersDownloaded: 0,
          });
        });

        // Add questions data
        questionsBySubject.forEach((item) => {
          const key = item.subjectId.toString();
          if (subjectMap.has(key)) {
            subjectMap.get(key).questionsGenerated = item.questionsGenerated;
          } else {
            subjectMap.set(key, {
              subjectId: item.subjectId,
              subjectName: item.subjectName,
              questionPapersGenerated: 0,
              questionsGenerated: item.questionsGenerated,
              questionPapersDownloaded: 0,
            });
          }
        });

        // Add downloads data
        downloadsBySubject.forEach((item) => {
          const key = item.subjectId.toString();
          if (subjectMap.has(key)) {
            subjectMap.get(key).questionPapersDownloaded =
              item.questionPapersDownloaded;
          } else {
            subjectMap.set(key, {
              subjectId: item.subjectId,
              subjectName: item.subjectName,
              questionPapersGenerated: 0,
              questionsGenerated: 0,
              questionPapersDownloaded: item.questionPapersDownloaded,
            });
          }
        });

        // Convert map to array and sort by subject name
        const result = Array.from(subjectMap.values()).sort((a, b) =>
          a.subjectName.localeCompare(b.subjectName),
        );

        return result;
      } catch (error) {
        this.logger.error(
          `Error getting subject-wise analytics: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    });
  }
}
