// auth/strategies/jwt.strategy.ts
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument } from '../../schema/user.schema';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  async validate(payload: any) {
    // Retrieve user and populate collegeId
    const user = await this.userModel
      .findById(payload.sub)
      .populate('collegeId')
      .exec();

    // Extract collegeId from user object if it exists
    let collegeId = payload.collegeId;

    // If user has a collegeId in the database but not in the token, use the one from the database
    if (!collegeId && user && user.collegeId) {
      collegeId =
        typeof user.collegeId === 'string'
          ? user.collegeId
          : user.collegeId.toString();
    }

    return {
      _id: payload.sub,
      userId: payload.sub, // Keep for backward compatibility
      email: payload.email,
      role: payload.role,
      collegeId: collegeId,
      user,
    };
  }
}
