"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/question-bank/page",{

/***/ "(app-pages-browser)/./src/utils/imageUtils.ts":
/*!*********************************!*\
  !*** ./src/utils/imageUtils.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureDataUrl: () => (/* binding */ ensureDataUrl),\n/* harmony export */   extractImagesFromText: () => (/* binding */ extractImagesFromText),\n/* harmony export */   isBase64Image: () => (/* binding */ isBase64Image),\n/* harmony export */   validateBase64ImageSrc: () => (/* binding */ validateBase64ImageSrc)\n/* harmony export */ });\n/**\n * Utility functions for handling images, including base64 detection and conversion\n */ /**\n * Checks if a string is a base64 encoded image\n */ function isBase64Image(str) {\n    if (!str || typeof str !== 'string') return false;\n    // Check for data URL format\n    const dataUrlPattern = /^data:image\\/(png|jpg|jpeg|gif|webp|svg\\+xml);base64,/i;\n    if (dataUrlPattern.test(str)) return true;\n    // Check for raw base64 (without data URL prefix)\n    // Base64 strings are typically long and contain only valid base64 characters\n    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;\n    return str.length > 100 && base64Pattern.test(str);\n}\n/**\n * Converts a base64 string to a data URL if it's not already one\n * Also handles cases where the data URL prefix is duplicated\n */ function ensureDataUrl(base64String) {\n    if (!base64String) return '';\n    // Handle duplicated data URL prefixes (e.g., \"data:image/jpeg;base64,data:image/jpeg;base64,...\")\n    const duplicatedPrefixPattern = /^(data:image\\/[^;]+;base64,)(data:image\\/[^;]+;base64,)/;\n    if (duplicatedPrefixPattern.test(base64String)) {\n        // Remove the first occurrence of the duplicated prefix\n        base64String = base64String.replace(duplicatedPrefixPattern, '$2');\n    }\n    // If it's already a data URL, return as is\n    if (base64String.startsWith('data:image/')) {\n        return base64String;\n    }\n    // If it's raw base64, add the data URL prefix\n    // Default to PNG if we can't determine the format\n    return \"data:image/png;base64,\".concat(base64String);\n}\n/**\n * Extracts and processes images from text content\n * Returns an object with cleaned text and extracted images\n */ function extractImagesFromText(text) {\n    if (!text) return {\n        cleanText: text,\n        images: []\n    };\n    const images = [];\n    let cleanText = text;\n    // First, look for markdown-style image syntax: ![alt](data:image/type;base64,...)\n    const markdownImagePattern = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n    const markdownMatches = [\n        ...text.matchAll(markdownImagePattern)\n    ];\n    if (markdownMatches.length > 0) {\n        markdownMatches.forEach((match, index)=>{\n            const fullMatch = match[0];\n            const altText = match[1] || \"Image \".concat(images.length + 1);\n            let imageSrc = match[2];\n            // Handle cases where the image src might have duplicated prefixes or other issues\n            // Clean up the image source before validation\n            imageSrc = imageSrc.trim();\n            // Check if the src contains base64 data (either with data URL or raw base64)\n            const containsBase64 = imageSrc.includes('base64,') || isBase64Image(imageSrc);\n            if (containsBase64) {\n                const imageId = \"extracted-image-markdown-\".concat(index);\n                const validatedSrc = ensureDataUrl(imageSrc);\n                images.push({\n                    id: imageId,\n                    src: validatedSrc,\n                    alt: altText\n                });\n                // Remove the markdown image syntax from the text\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        });\n    }\n    // Second, look for complete data URLs (these take priority over raw base64)\n    const dataUrlPattern = /data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*/g;\n    const dataUrlMatches = cleanText.match(dataUrlPattern); // Use cleanText to avoid already processed markdown images\n    if (dataUrlMatches) {\n        dataUrlMatches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = \"extracted-image-dataurl-\".concat(index);\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: \"Extracted image \".concat(images.length + 1)\n                });\n                // Remove the complete data URL from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Finally, look for raw base64 strings (only if they're not part of already processed content)\n    const rawBase64Pattern = /\\b[A-Za-z0-9+/]{200,}={0,2}\\b/g;\n    const rawBase64Matches = cleanText.match(rawBase64Pattern); // Use cleanText to avoid already processed content\n    if (rawBase64Matches) {\n        rawBase64Matches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = \"extracted-image-raw-\".concat(index);\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: \"Extracted image \".concat(images.length + 1)\n                });\n                // Remove the raw base64 string from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Clean up any extra whitespace left after removing base64 strings\n    cleanText = cleanText.replace(/\\s+/g, ' ').trim();\n    return {\n        cleanText,\n        images\n    };\n}\n/**\n * Validates and sanitizes base64 image source\n */ function validateBase64ImageSrc(src) {\n    if (!src || !isBase64Image(src)) return null;\n    try {\n        const dataUrl = ensureDataUrl(src);\n        // Additional validation could be added here\n        return dataUrl;\n    } catch (error) {\n        console.warn('Invalid base64 image source:', error);\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/imageUtils.ts\n"));

/***/ })

});