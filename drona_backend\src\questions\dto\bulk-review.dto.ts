import {
  Is<PERSON>rray,
  IsEnum,
  IsOptional,
  IsString,
  ArrayMinSize,
  IsMongoId,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class BulkReviewDto {
  @ApiProperty({
    description: 'Array of question IDs to review',
    example: ['60d21b4667d0d8992e610c85', '60d21b4967d0d8992e610c86'],
    type: [String],
    minItems: 1,
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'At least one question ID must be provided' })
  @IsMongoId({
    each: true,
    message: 'Each question ID must be a valid MongoDB ID',
  })
  questionIds: string[];

  @ApiProperty({
    description: 'Review status (approved or rejected)',
    example: 'approved',
    enum: ['approved', 'rejected'],
  })
  @IsEnum(['approved', 'rejected'], {
    message: 'Status must be either approved or rejected',
  })
  status: 'approved' | 'rejected';

  @ApiProperty({
    description: 'Optional notes for the review',
    example: 'These questions meet our quality standards',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
