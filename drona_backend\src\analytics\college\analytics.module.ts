import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AnalyticsController } from './analytics.controller';
import { AnalyticsService } from './analytics.service';
import { User, UserSchema } from '../../schema/user.schema';
import {
  QuestionPaper,
  QuestionPaperSchema,
} from '../../schema/question-paper.schema';
import { Subject, SubjectSchema } from '../../schema/subject.schema';
import { Question, QuestionSchema } from '../../schema/question.schema';
import {
  UserActivity,
  UserActivitySchema,
} from '../../schema/user-activity.schema';
import { Download, DownloadSchema } from '../../schema/download.schema';
import { AnalyticsCacheService } from '../../common/services';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: QuestionPaper.name, schema: QuestionPaperSchema },
      { name: Subject.name, schema: SubjectSchema },
      { name: Question.name, schema: QuestionSchema },
      { name: UserActivity.name, schema: UserActivitySchema },
      { name: Download.name, schema: DownloadSchema },
    ]),
  ],
  controllers: [AnalyticsController],
  providers: [AnalyticsService, AnalyticsCacheService],
})
export class AnalyticsModule {}
