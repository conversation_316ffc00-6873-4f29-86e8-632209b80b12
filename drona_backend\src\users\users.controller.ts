import { Controller, Get, Post, Body, Param, UseGuards } from '@nestjs/common';
import { UsersService } from './users.service';
import { User, UserDocument } from '../schema/user.schema';
import { AssignRoleDto } from './dto/assign-role.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/guards/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';

@ApiTags('Users')
@ApiBearerAuth('JWT-auth')
@Controller('users')
@UseGuards(JwtAuthGuard, RolesGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('me')
  @ApiOperation({
    summary: 'Get current user',
    description: 'Returns the currently authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the current user',
    type: User,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  async getCurrentUser(@CurrentUser() user: any): Promise<any> {
    // Use the _id from the JWT token (with fallback to userId for backward compatibility)
    const userId = user._id || user.userId;
    const userDoc = await this.usersService.findOne(userId);

    // If the user has a collegeId in the token but not in the database, update the database
    if (user.collegeId && !userDoc.collegeId) {
      // Update the user's collegeId in the database
      await this.usersService.updateCollegeId(userId, user.collegeId);
      // Retrieve the updated user
      const updatedUser = await this.usersService.findOne(userId);

      // Transform the response to rename collegeId to college
      const userResponse = this.transformUserResponse(updatedUser);
      return userResponse;
    }

    // Transform the response to rename collegeId to college
    const userResponse = this.transformUserResponse(userDoc);
    return userResponse;
  }

  /**
   * Transform user document to rename collegeId to college in the response
   * @param userDoc User document from database
   * @returns Transformed user object
   */
  private transformUserResponse(userDoc: UserDocument): any {
    const userObj = userDoc.toObject();

    // Rename collegeId to college
    if (userObj.collegeId) {
      userObj.college = userObj.collegeId;
      delete userObj.collegeId;
    }

    return userObj;
  }

  @Get()
  @Roles('superAdmin')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get all users',
    description: 'Returns all users (super admin only)',
  })
  @ApiResponse({ status: 200, description: 'Returns all users', type: [User] })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async findAll(): Promise<UserDocument[]> {
    return this.usersService.findAll();
  }

  @Post(':id/assign-role')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Assign role to user',
    description: 'Assigns a role to a user (super admin only)',
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Role assigned successfully',
    type: User,
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  async assignRole(
    @Param('id') id: string,
    @Body() assignRoleDto: AssignRoleDto,
  ): Promise<UserDocument> {
    return this.usersService.assignRole(id, assignRoleDto);
  }

  @Get('colleges/:collegeId/users')
  @Roles('superAdmin', 'collegeAdmin')
  @ApiOperation({
    summary: 'Get users by college',
    description: 'Returns all users associated with a college',
  })
  @ApiParam({ name: 'collegeId', description: 'College ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns users associated with the college',
    type: [User],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'College not found' })
  async findByCollege(
    @Param('collegeId') collegeId: string,
  ): Promise<UserDocument[]> {
    return this.usersService.findByCollege(collegeId);
  }
}
