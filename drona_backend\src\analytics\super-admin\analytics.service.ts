import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  UserActivity,
  UserActivityDocument,
} from '../../schema/user-activity.schema';
import { User, UserDocument } from '../../schema/user.schema';
import {
  QuestionPaper,
  QuestionPaperDocument,
} from '../../schema/question-paper.schema';
import { Question, QuestionDocument } from '../../schema/question.schema';
import { College, CollegeDocument } from '../../schema/college.schema';
import { Download, DownloadDocument } from '../../schema/download.schema';
import { AnalyticsCacheService } from '../../common/services';

@Injectable()
export class AnalyticsService {
  private readonly logger = new Logger(AnalyticsService.name);
  private readonly CACHE_TTL = 3600000; // 1 hour in milliseconds

  constructor(
    @InjectModel(UserActivity.name)
    private userActivityModel: Model<UserActivityDocument>,
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(QuestionPaper.name)
    private questionPaperModel: Model<QuestionPaperDocument>,
    @InjectModel(Question.name) private questionModel: Model<QuestionDocument>,
    @InjectModel(College.name) private collegeModel: Model<CollegeDocument>,
    @InjectModel(Download.name) private downloadModel: Model<DownloadDocument>,
    private readonly cacheService: AnalyticsCacheService,
  ) {}

  async getPlatformSummary() {
    const cacheKey = 'platform_summary';

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log('Computing platform summary (cache miss)');

      // Get activity in the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      // Run all queries in parallel for better performance
      const [
        totalColleges,
        totalTeachers,
        totalQuestions,
        totalPapers,
        totalDownloads,
        recentActivity,
      ] = await Promise.all([
        // Get total counts
        this.collegeModel.countDocuments({ status: 'active' }),
        this.userModel.countDocuments({ role: 'teacher' }),
        this.questionModel.countDocuments({ status: 'active' }),
        this.questionPaperModel.countDocuments({ status: 'published' }),
        this.downloadModel.countDocuments(),

        // Get recent activity counts using a single aggregation
        this.userActivityModel
          .aggregate([
            {
              $match: {
                timestamp: { $gte: thirtyDaysAgo },
              },
            },
            {
              $group: {
                _id: '$activityType',
                count: { $sum: 1 },
              },
            },
          ])
          .exec(),
      ]);

      // Process the activity results
      const activityMap = recentActivity.reduce((acc, curr) => {
        acc[curr._id] = curr.count;
        return acc;
      }, {});

      // Get recent downloads from downloads collection
      const recentDownloads = await this.downloadModel.countDocuments({
        downloadDate: { $gte: thirtyDaysAgo },
      });

      return {
        totalColleges,
        totalTeachers,
        totalQuestions,
        totalPapers,
        totalDownloads,
        recentActivity: {
          logins: activityMap['login'] || 0,
          paperGenerations: activityMap['paper_generation'] || 0,
          questionCreations: activityMap['question_creation'] || 0,
          downloads: recentDownloads,
        },
      };
    });
  }

  async getTopColleges() {
    const cacheKey = 'top_colleges';

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log('Computing top colleges (cache miss)');

      // Run both aggregations in parallel
      const [topCollegesByDownloads, topCollegesByTeachers] = await Promise.all(
        [
          // Get colleges with the most downloads - optimized pipeline
          this.downloadModel
            .aggregate([
              // Stage 1: Group by collegeId and count downloads
              {
                $group: {
                  _id: '$collegeId',
                  downloadCount: { $sum: 1 },
                },
              },
              // Stage 2: Sort by download count descending
              { $sort: { downloadCount: -1 } },
              // Stage 3: Limit to top 10
              { $limit: 10 },
              // Stage 4: Lookup college details
              {
                $lookup: {
                  from: 'colleges',
                  localField: '_id',
                  foreignField: '_id',
                  as: 'college',
                },
              },
              // Stage 5: Unwind the college array
              { $unwind: '$college' },
              // Stage 6: Project only needed fields
              {
                $project: {
                  _id: 1,
                  name: '$college.name',
                  downloadCount: 1,
                },
              },
            ])
            .exec(),

          // Get colleges with the most teachers - optimized pipeline
          this.userModel
            .aggregate([
              // Stage 1: Match only teacher roles (use index)
              { $match: { role: 'teacher' } },
              // Stage 2: Group by collegeId and count teachers
              {
                $group: {
                  _id: '$collegeId',
                  teacherCount: { $sum: 1 },
                },
              },
              // Stage 3: Sort by teacher count descending
              { $sort: { teacherCount: -1 } },
              // Stage 4: Limit to top 10
              { $limit: 10 },
              // Stage 5: Lookup college details
              {
                $lookup: {
                  from: 'colleges',
                  localField: '_id',
                  foreignField: '_id',
                  as: 'college',
                },
              },
              // Stage 6: Unwind the college array
              { $unwind: '$college' },
              // Stage 7: Project only needed fields
              {
                $project: {
                  _id: 1,
                  name: '$college.name',
                  teacherCount: 1,
                },
              },
            ])
            .exec(),
        ],
      );

      return {
        byDownloads: topCollegesByDownloads,
        byTeachers: topCollegesByTeachers,
      };
    });
  }

  async getQuestionUsage() {
    const cacheKey = 'question_usage';

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log('Computing question usage statistics (cache miss)');

      // Run all aggregations in parallel for better performance
      const [usedQuestions, unusedQuestions, questionStats] = await Promise.all(
        [
          // Get questions that have been used in papers (each question should only be counted once)
          this.questionPaperModel
            .aggregate([
              // Stage 1: Unwind sections and questions to get individual question references
              { $unwind: '$sections' },
              { $unwind: '$sections.questions' },
              // Stage 2: Group by questionId to count papers and find first usage
              {
                $group: {
                  _id: '$sections.questions.questionId',
                  paperCount: { $sum: 1 },
                  firstUsedIn: { $min: '$createdAt' },
                },
              },
              // Stage 3: Sort by most recently used first
              { $sort: { firstUsedIn: -1 } },
              // Stage 4: Limit to 20 questions for performance
              { $limit: 20 },
              // Stage 5: Lookup question details
              {
                $lookup: {
                  from: 'questions',
                  localField: '_id',
                  foreignField: '_id',
                  as: 'question',
                },
              },
              // Stage 6: Unwind the question array
              { $unwind: '$question' },
              // Stage 7: Project only needed fields
              {
                $project: {
                  _id: 1,
                  content: '$question.content',
                  subjectId: '$question.subjectId',
                  topicId: '$question.topicId',
                  difficulty: '$question.difficulty',
                  paperCount: 1,
                  firstUsedIn: 1,
                },
              },
            ])
            .exec(),

          // Get questions that have never been used in papers - optimized query
          this.questionModel
            .aggregate([
              // Stage 1: Match only active questions (use index)
              { $match: { status: 'active' } },
              // Stage 2: Lookup in question papers to find usage
              {
                $lookup: {
                  from: 'questionpapers',
                  let: { questionId: '$_id' },
                  pipeline: [
                    { $match: { status: 'published' } }, // Only check published papers
                    { $unwind: '$sections' },
                    { $unwind: '$sections.questions' },
                    {
                      $match: {
                        $expr: {
                          $eq: [
                            '$sections.questions.questionId',
                            '$$questionId',
                          ],
                        },
                      },
                    },
                    { $limit: 1 }, // We only need to know if there's at least one match
                  ],
                  as: 'usedInPapers',
                },
              },
              // Stage 3: Match questions with no usage
              {
                $match: {
                  usedInPapers: { $size: 0 },
                },
              },
              // Stage 4: Project only needed fields
              {
                $project: {
                  _id: 1,
                  content: 1,
                  subjectId: 1,
                  topicId: 1,
                  difficulty: 1,
                  type: 1,
                },
              },
              // Stage 5: Limit to 20 questions for performance
              { $limit: 20 },
            ])
            .exec(),

          // Get question statistics by difficulty and type in a single aggregation
          this.questionModel
            .aggregate([
              // Stage 1: Match only active questions (use index)
              { $match: { status: 'active' } },
              // Stage 2: Facet to run multiple aggregations in parallel
              {
                $facet: {
                  // Get counts by difficulty
                  byDifficulty: [
                    {
                      $group: {
                        _id: '$difficulty',
                        count: { $sum: 1 },
                      },
                    },
                  ],
                  // Get counts by type
                  byType: [
                    {
                      $group: {
                        _id: '$type',
                        count: { $sum: 1 },
                      },
                    },
                  ],
                },
              },
            ])
            .exec(),
        ],
      );

      // Extract the results from the facet
      const { byDifficulty, byType } = questionStats[0];

      return {
        usedQuestions,
        unusedQuestions,
        byDifficulty,
        byType,
      };
    });
  }

  async getQuestionStats() {
    const cacheKey = 'question_stats';

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log('Computing question statistics (cache miss)');

      // Use a single aggregation with facets to get all stats in one query
      const result = await this.questionModel
        .aggregate([
          // Stage 1: Match only active questions (use index)
          { $match: { status: 'active' } },

          // Stage 2: Use facet to run multiple aggregations in parallel
          {
            $facet: {
              // Count total questions
              totalCount: [{ $count: 'count' }],

              // Group by subject
              bySubject: [
                {
                  $group: {
                    _id: '$subjectId',
                    count: { $sum: 1 },
                  },
                },
                {
                  $lookup: {
                    from: 'subjects',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'subject',
                  },
                },
                { $unwind: '$subject' },
                {
                  $project: {
                    _id: 1,
                    subjectName: '$subject.name',
                    count: 1,
                  },
                },
                { $sort: { count: -1 } },
              ],

              // Group by topic
              byTopic: [
                {
                  $group: {
                    _id: '$topicId',
                    count: { $sum: 1 },
                  },
                },
                {
                  $lookup: {
                    from: 'topics',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'topic',
                  },
                },
                { $unwind: '$topic' },
                {
                  $project: {
                    _id: 1,
                    topicName: '$topic.name',
                    count: 1,
                  },
                },
                { $sort: { count: -1 } },
              ],

              // Group by difficulty
              byDifficulty: [
                {
                  $group: {
                    _id: '$difficulty',
                    count: { $sum: 1 },
                  },
                },
                { $sort: { count: -1 } },
              ],

              // Group by type
              byType: [
                {
                  $group: {
                    _id: '$type',
                    count: { $sum: 1 },
                  },
                },
                { $sort: { count: -1 } },
              ],
            },
          },
        ])
        .exec();

      // Extract results from the facet
      const facetResult = result[0];
      const totalQuestions =
        facetResult.totalCount.length > 0 ? facetResult.totalCount[0].count : 0;

      return {
        totalQuestions,
        bySubject: facetResult.bySubject,
        byTopic: facetResult.byTopic,
        byDifficulty: facetResult.byDifficulty,
        byType: facetResult.byType,
      };
    });
  }

  async getCollegeAnalytics(
    filters: {
      startDate?: Date;
      endDate?: Date;
      limit?: number;
      sortBy?: string;
    } = {},
  ) {
    const cacheKey = `college_analytics_${JSON.stringify(filters)}`;

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log('Computing college analytics (cache miss)');

      try {
        // Set defaults
        const limit = filters.limit || 50;
        const sortBy = filters.sortBy || 'totalActivity';
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        // Build date filters
        const dateFilter: any = {};
        if (filters.startDate || filters.endDate) {
          if (filters.startDate) dateFilter.$gte = filters.startDate;
          if (filters.endDate) dateFilter.$lte = filters.endDate;
        }

        // Get all colleges with comprehensive analytics
        const collegeAnalytics = await this.collegeModel
          .aggregate([
            // Stage 1: Match active colleges
            { $match: { status: 'active' } },

            // Stage 2: Lookup teachers
            {
              $lookup: {
                from: 'users',
                let: { collegeId: '$_id' },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$collegeId', '$$collegeId'] },
                      role: 'teacher',
                    },
                  },
                ],
                as: 'teachers',
              },
            },

            // Stage 3: Lookup questions
            {
              $lookup: {
                from: 'questions',
                let: { collegeId: '$_id' },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$collegeId', '$$collegeId'] },
                      status: 'active',
                    },
                  },
                ],
                as: 'questions',
              },
            },

            // Stage 4: Lookup question papers
            {
              $lookup: {
                from: 'questionpapers',
                let: { collegeId: '$_id' },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$collegeId', '$$collegeId'] },
                    },
                  },
                ],
                as: 'questionPapers',
              },
            },

            // Stage 5: Lookup downloads
            {
              $lookup: {
                from: 'downloads',
                let: { collegeId: '$_id' },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$collegeId', '$$collegeId'] },
                    },
                  },
                ],
                as: 'downloads',
              },
            },

            // Stage 6: Lookup recent activity
            {
              $lookup: {
                from: 'useractivities',
                let: { collegeId: '$_id' },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$collegeId', '$$collegeId'] },
                      timestamp: { $gte: thirtyDaysAgo },
                    },
                  },
                ],
                as: 'recentActivities',
              },
            },

            // Stage 7: Project and calculate metrics
            {
              $project: {
                collegeId: '$_id',
                collegeName: '$name',
                status: '$status',
                metrics: {
                  teacherCount: { $size: '$teachers' },
                  activeTeachers: {
                    $size: {
                      $filter: {
                        input: '$teachers',
                        cond: { $eq: ['$$this.status', 'active'] },
                      },
                    },
                  },
                  questionCount: { $size: '$questions' },
                  approvedQuestions: {
                    $size: {
                      $filter: {
                        input: '$questions',
                        cond: { $eq: ['$$this.reviewStatus', 'approved'] },
                      },
                    },
                  },
                  pendingQuestions: {
                    $size: {
                      $filter: {
                        input: '$questions',
                        cond: { $eq: ['$$this.reviewStatus', 'pending'] },
                      },
                    },
                  },
                  rejectedQuestions: {
                    $size: {
                      $filter: {
                        input: '$questions',
                        cond: { $eq: ['$$this.reviewStatus', 'rejected'] },
                      },
                    },
                  },
                  paperCount: { $size: '$questionPapers' },
                  downloadCount: { $size: '$downloads' },
                  totalActivity: {
                    $add: [
                      { $size: '$questions' },
                      { $size: '$questionPapers' },
                      { $size: '$downloads' },
                    ],
                  },
                },
                recentActivity: {
                  last30Days: {
                    questionsCreated: {
                      $size: {
                        $filter: {
                          input: '$recentActivities',
                          cond: {
                            $eq: ['$$this.activityType', 'question_creation'],
                          },
                        },
                      },
                    },
                    papersGenerated: {
                      $size: {
                        $filter: {
                          input: '$recentActivities',
                          cond: {
                            $eq: ['$$this.activityType', 'paper_generation'],
                          },
                        },
                      },
                    },
                    downloads: {
                      $size: {
                        $filter: {
                          input: '$downloads',
                          cond: {
                            $gte: ['$$this.downloadDate', thirtyDaysAgo],
                          },
                        },
                      },
                    },
                    activeTeachers: {
                      $size: {
                        $setUnion: [
                          {
                            $map: {
                              input: '$recentActivities',
                              as: 'activity',
                              in: '$$activity.userId',
                            },
                          },
                        ],
                      },
                    },
                  },
                },
                // Store raw data for subject breakdown
                questions: '$questions',
                questionPapers: '$questionPapers',
                downloads: '$downloads',
              },
            },
          ])
          .exec();

        // Get subject breakdown for each college
        for (const college of collegeAnalytics) {
          college.subjectBreakdown = await this.getCollegeSubjectBreakdown(
            college.collegeId,
          );
        }

        // Sort colleges based on sortBy criteria
        collegeAnalytics.sort((a, b) => {
          let aValue, bValue;
          switch (sortBy) {
            case 'questionCount':
              aValue = a.metrics.questionCount;
              bValue = b.metrics.questionCount;
              break;
            case 'downloadCount':
              aValue = a.metrics.downloadCount;
              bValue = b.metrics.downloadCount;
              break;
            case 'teacherCount':
              aValue = a.metrics.teacherCount;
              bValue = b.metrics.teacherCount;
              break;
            case 'paperCount':
              aValue = a.metrics.paperCount;
              bValue = b.metrics.paperCount;
              break;
            default: // totalActivity
              aValue = a.metrics.totalActivity;
              bValue = b.metrics.totalActivity;
          }
          return bValue - aValue; // Descending order
        });

        // Limit results
        const limitedColleges = collegeAnalytics.slice(0, limit);

        // Remove raw data from response
        limitedColleges.forEach((college) => {
          delete college.questions;
          delete college.questionPapers;
          delete college.downloads;
        });

        // Calculate summary
        const summary = {
          totalColleges: collegeAnalytics.length,
          totalTeachers: collegeAnalytics.reduce(
            (sum, c) => sum + c.metrics.teacherCount,
            0,
          ),
          totalQuestions: collegeAnalytics.reduce(
            (sum, c) => sum + c.metrics.questionCount,
            0,
          ),
          totalPapers: collegeAnalytics.reduce(
            (sum, c) => sum + c.metrics.paperCount,
            0,
          ),
          totalDownloads: collegeAnalytics.reduce(
            (sum, c) => sum + c.metrics.downloadCount,
            0,
          ),
        };

        return {
          summary,
          colleges: limitedColleges,
          filters: {
            startDate: filters.startDate || null,
            endDate: filters.endDate || null,
            limit,
            sortBy,
          },
        };
      } catch (error) {
        this.logger.error(
          `Error getting college analytics: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    });
  }

  private async getCollegeSubjectBreakdown(collegeId: any) {
    try {
      const subjectBreakdown = await this.questionModel
        .aggregate([
          { $match: { collegeId: collegeId, status: 'active' } },
          {
            $lookup: {
              from: 'subjects',
              localField: 'subjectId',
              foreignField: '_id',
              as: 'subject',
            },
          },
          { $unwind: '$subject' },
          {
            $group: {
              _id: {
                subjectId: '$subject._id',
                subjectName: '$subject.name',
              },
              questionCount: { $sum: 1 },
            },
          },
          {
            $lookup: {
              from: 'questionpapers',
              let: { subjectId: '$_id.subjectId' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ['$subjectId', '$$subjectId'] },
                        { $eq: ['$collegeId', collegeId] },
                      ],
                    },
                  },
                },
              ],
              as: 'papers',
            },
          },
          {
            $lookup: {
              from: 'downloads',
              let: { subjectId: '$_id.subjectId' },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: ['$collegeId', collegeId] },
                  },
                },
                {
                  $lookup: {
                    from: 'questionpapers',
                    localField: 'paperId',
                    foreignField: '_id',
                    as: 'paper',
                  },
                },
                { $unwind: '$paper' },
                {
                  $match: {
                    $expr: { $eq: ['$paper.subjectId', '$$subjectId'] },
                  },
                },
              ],
              as: 'downloads',
            },
          },
          {
            $project: {
              _id: 0,
              subjectId: '$_id.subjectId',
              subjectName: '$_id.subjectName',
              questionCount: 1,
              paperCount: { $size: '$papers' },
              downloadCount: { $size: '$downloads' },
            },
          },
          { $sort: { subjectName: 1 } },
        ])
        .exec();

      return subjectBreakdown;
    } catch (error) {
      this.logger.error(
        `Error getting subject breakdown for college ${collegeId}: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  async getUsageTrends(
    filters: {
      year?: number;
      startDate?: Date;
      endDate?: Date;
    } = {},
  ) {
    const cacheKey = `usage_trends_${JSON.stringify(filters)}`;

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log('Computing usage trends (cache miss)');

      try {
        // Determine date range
        let startDate: Date, endDate: Date;

        if (filters.startDate && filters.endDate) {
          // Custom date range
          startDate = filters.startDate;
          endDate = filters.endDate;
        } else if (filters.year) {
          // Specific year
          startDate = new Date(filters.year, 0, 1); // January 1st
          endDate = new Date(filters.year, 11, 31, 23, 59, 59); // December 31st
        } else {
          // Default to current year
          const currentYear = new Date().getFullYear();
          startDate = new Date(currentYear, 0, 1);
          endDate = new Date(currentYear, 11, 31, 23, 59, 59);
        }

        // Get monthly usage data using aggregation
        const [questionsData, papersData] = await Promise.all([
          // Questions created per month
          this.questionModel
            .aggregate([
              {
                $match: {
                  createdAt: { $gte: startDate, $lte: endDate },
                  status: 'active',
                },
              },
              {
                $group: {
                  _id: {
                    year: { $year: '$createdAt' },
                    month: { $month: '$createdAt' },
                  },
                  count: { $sum: 1 },
                },
              },
              { $sort: { '_id.year': 1, '_id.month': 1 } },
            ])
            .exec(),

          // Papers generated per month
          this.questionPaperModel
            .aggregate([
              {
                $match: {
                  createdAt: { $gte: startDate, $lte: endDate },
                  status: 'published',
                },
              },
              {
                $group: {
                  _id: {
                    year: { $year: '$createdAt' },
                    month: { $month: '$createdAt' },
                  },
                  count: { $sum: 1 },
                },
              },
              { $sort: { '_id.year': 1, '_id.month': 1 } },
            ])
            .exec(),
        ]);

        // Create a map for easy lookup
        const questionsMap = new Map();
        questionsData.forEach((item) => {
          const key = `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`;
          questionsMap.set(key, item.count);
        });

        const papersMap = new Map();
        papersData.forEach((item) => {
          const key = `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`;
          papersMap.set(key, item.count);
        });

        // Generate monthly data for the entire range
        const monthlyData: any[] = [];
        const currentDate = new Date(startDate);

        while (currentDate <= endDate) {
          const year = currentDate.getFullYear();
          const month = currentDate.getMonth() + 1;
          const monthKey = `${year}-${month.toString().padStart(2, '0')}`;

          const questionsCreated = questionsMap.get(monthKey) || 0;
          const papersGenerated = papersMap.get(monthKey) || 0;

          monthlyData.push({
            month: monthKey,
            monthName: currentDate.toLocaleDateString('en-US', {
              month: 'long',
              year: 'numeric',
            }),
            questionsCreated,
            papersGenerated,
            totalUsage: questionsCreated + papersGenerated,
          });

          // Move to next month
          currentDate.setMonth(currentDate.getMonth() + 1);
        }

        // Calculate summary statistics
        const totalQuestionsCreated = monthlyData.reduce(
          (sum, month) => sum + month.questionsCreated,
          0,
        );
        const totalPapersGenerated = monthlyData.reduce(
          (sum, month) => sum + month.papersGenerated,
          0,
        );
        const totalMonths = monthlyData.length;

        const summary = {
          totalMonths,
          totalQuestionsCreated,
          totalPapersGenerated,
          averageMonthlyQuestions:
            totalMonths > 0
              ? Math.round(totalQuestionsCreated / totalMonths)
              : 0,
          averageMonthlyPapers:
            totalMonths > 0
              ? Math.round(totalPapersGenerated / totalMonths)
              : 0,
        };

        return {
          data: monthlyData,
          summary,
          dateRange: {
            startDate,
            endDate,
          },
        };
      } catch (error) {
        this.logger.error(
          `Error getting usage trends: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    });
  }

  async getCollegeGrowth(
    filters: {
      year?: number;
      startDate?: Date;
      endDate?: Date;
      view?: string;
    } = {},
  ) {
    const cacheKey = `college_growth_${JSON.stringify(filters)}`;

    return this.cacheService.getOrCompute(cacheKey, async () => {
      this.logger.log('Computing college growth trends (cache miss)');

      try {
        // Determine date range
        let startDate: Date, endDate: Date;

        if (filters.startDate && filters.endDate) {
          // Custom date range
          startDate = filters.startDate;
          endDate = filters.endDate;
        } else if (filters.year) {
          // Specific year
          startDate = new Date(filters.year, 0, 1); // January 1st
          endDate = new Date(filters.year, 11, 31, 23, 59, 59); // December 31st
        } else {
          // Default to current year
          const currentYear = new Date().getFullYear();
          startDate = new Date(currentYear, 0, 1);
          endDate = new Date(currentYear, 11, 31, 23, 59, 59);
        }

        // Get colleges created per month
        const collegeGrowthData = await this.collegeModel
          .aggregate([
            {
              $match: {
                createdAt: { $gte: startDate, $lte: endDate },
              },
            },
            {
              $group: {
                _id: {
                  year: { $year: '$createdAt' },
                  month: { $month: '$createdAt' },
                },
                collegesAdded: { $sum: 1 },
              },
            },
            { $sort: { '_id.year': 1, '_id.month': 1 } },
          ])
          .exec();

        // Create a map for easy lookup
        const growthMap = new Map();
        collegeGrowthData.forEach((item) => {
          const key = `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`;
          growthMap.set(key, item.collegesAdded);
        });

        // Get total colleges before the start date for cumulative calculation
        const collegesBeforeStart = await this.collegeModel.countDocuments({
          createdAt: { $lt: startDate },
        });

        // Generate monthly data for the entire range
        const monthlyData: any[] = [];
        const currentDate = new Date(startDate);
        let cumulativeColleges = collegesBeforeStart;

        // Default targets (can be made configurable)
        const monthlyTarget = 6;
        const yearlyTarget = monthlyTarget * 12;

        while (currentDate <= endDate) {
          const year = currentDate.getFullYear();
          const month = currentDate.getMonth() + 1;
          const monthKey = `${year}-${month.toString().padStart(2, '0')}`;

          const collegesAdded = growthMap.get(monthKey) || 0;
          cumulativeColleges += collegesAdded;

          const targetAchievement =
            monthlyTarget > 0 ? (collegesAdded / monthlyTarget) * 100 : 0;

          // Generate mock data for different views
          const revenue = this.generateRevenueData(collegesAdded, filters.view);
          const salesMetrics = this.generateSalesMetrics(
            collegesAdded,
            filters.view,
          );

          monthlyData.push({
            month: monthKey,
            monthName: currentDate.toLocaleDateString('en-US', {
              month: 'long',
              year: 'numeric',
            }),
            collegesAdded,
            cumulativeColleges,
            monthlyTarget,
            targetAchievement: Math.round(targetAchievement * 100) / 100,
            revenue,
            salesMetrics,
          });

          // Move to next month
          currentDate.setMonth(currentDate.getMonth() + 1);
        }

        // Calculate summary statistics
        const totalCollegesAdded = monthlyData.reduce(
          (sum, month) => sum + month.collegesAdded,
          0,
        );
        const totalMonths = monthlyData.length;
        const averageMonthlyGrowth =
          totalMonths > 0 ? Math.round(totalCollegesAdded / totalMonths) : 0;
        const totalTargetAchievement =
          totalMonths > 0
            ? monthlyData.reduce(
                (sum, month) => sum + month.targetAchievement,
                0,
              ) / totalMonths
            : 0;
        const totalRevenue = monthlyData.reduce(
          (sum, month) => sum + month.revenue,
          0,
        );

        const summary = {
          totalMonths,
          totalCollegesAdded,
          averageMonthlyGrowth,
          totalTargetAchievement:
            Math.round(totalTargetAchievement * 100) / 100,
          totalRevenue,
        };

        const targets = {
          monthlyTarget,
          yearlyTarget,
          currentProgress: totalTargetAchievement,
        };

        return {
          data: monthlyData,
          summary,
          targets,
          dateRange: {
            startDate,
            endDate,
          },
        };
      } catch (error) {
        this.logger.error(
          `Error getting college growth trends: ${error.message}`,
          error.stack,
        );
        throw error;
      }
    });
  }

  private generateRevenueData(collegesAdded: number, view?: string): number {
    // Mock revenue calculation based on colleges added
    // In a real implementation, this would come from actual revenue data
    const baseRevenue = 5000; // Base revenue per college

    switch (view) {
      case 'revenue':
        return collegesAdded * baseRevenue * 1.2; // Premium pricing
      case 'sales':
        return collegesAdded * baseRevenue * 0.8; // Discounted pricing
      default: // overview
        return collegesAdded * baseRevenue;
    }
  }

  private generateSalesMetrics(collegesAdded: number, view?: string): any {
    // Mock sales metrics
    // In a real implementation, this would come from actual sales data
    const baseLeads = collegesAdded * 2.4; // 2.4 leads per conversion on average
    const conversionRate = view === 'sales' ? 45 : 41.67; // Higher rate for sales view

    return {
      conversions: collegesAdded,
      leads: Math.round(baseLeads),
      conversionRate: Math.round(conversionRate * 100) / 100,
    };
  }
}
