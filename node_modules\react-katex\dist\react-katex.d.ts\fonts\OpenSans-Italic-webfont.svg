<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="open_sansitalic" horiz-adv-x="1128" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="532" />
<glyph unicode="&#xfb01;" horiz-adv-x="1165" d="M0 0zM-104 -492q-69 0 -125 21v141q64 -22 112 -22q76 0 117 62t66 177l227 1082h-193l13 67l206 66l23 100q46 200 127.5 282.5t241.5 82.5q40 0 98 -11.5t90 -25.5l-43 -129q-76 29 -137 29q-87 0 -133.5 -48.5t-75.5 -177.5l-25 -108h238l-25 -127h-237l-232 -1098 q-39 -189 -120 -276t-213 -87zM870 0h-168l234 1096h168zM983 1376q0 56 32 91.5t83 35.5q88 0 88 -90q0 -55 -33.5 -93t-77.5 -38q-40 0 -66 24.5t-26 69.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1165" d="M0 0zM-104 -492q-69 0 -125 21v141q64 -22 112 -22q76 0 117 62t66 177l227 1082h-193l13 67l206 66l23 100q46 200 127.5 282.5t241.5 82.5q40 0 98 -11.5t90 -25.5l-43 -129q-76 29 -137 29q-87 0 -133.5 -48.5t-75.5 -177.5l-25 -108h238l-25 -127h-237l-232 -1098 q-39 -189 -120 -276t-213 -87zM868 0h-168l332 1556h168z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1815" d="M545 -492q-71 0 -125 21v141q61 -22 115 -22q68 0 111 57.5t69 181.5l227 1082h-481l-232 -1098q-39 -189 -120 -276t-213 -87q-69 0 -125 21v141q64 -22 112 -22q70 0 114 58t69 181l227 1082h-193l13 67l206 66l23 100q46 200 127.5 282.5t241.5 82.5q40 0 98 -11.5 t90 -25.5l-43 -129q-76 29 -137 29q-87 0 -133.5 -48.5t-75.5 -177.5l-25 -108h482l24 108q45 197 126 280t243 83q41 0 97.5 -11t92.5 -26l-45 -129q-76 29 -137 29q-89 0 -135 -51t-74 -175l-24 -108h239l-26 -127h-238l-231 -1098q-43 -195 -123.5 -279t-210.5 -84z M1522 0h-168l233 1096h168zM1634 1376q0 54 32 90.5t83 36.5q88 0 88 -90q0 -55 -33.5 -93t-77.5 -38q-38 0 -65 24.5t-27 69.5z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1815" d="M1520 0h-168l331 1556h168zM545 -492q-71 0 -125 21v141q61 -22 115 -22q68 0 111 57.5t69 181.5l227 1082h-481l-232 -1098q-39 -189 -120 -276t-213 -87q-69 0 -125 21v141q64 -22 112 -22q70 0 114 58t69 181l227 1082h-193l13 67l206 66l23 100q46 200 127.5 282.5 t241.5 82.5q40 0 98 -11.5t90 -25.5l-43 -129q-76 29 -137 29q-87 0 -133.5 -48.5t-75.5 -177.5l-25 -108h482l24 108q45 197 126 280t243 83q41 0 97.5 -11t92.5 -26l-45 -129q-76 29 -137 29q-89 0 -135 -51t-74 -175l-24 -108h239l-26 -127h-238l-231 -1098 q-43 -195 -123.5 -279t-210.5 -84z" />
<glyph horiz-adv-x="2048" />
<glyph horiz-adv-x="2048" />
<glyph unicode="&#xd;" horiz-adv-x="1044" />
<glyph unicode=" "  horiz-adv-x="532" />
<glyph unicode="&#x09;" horiz-adv-x="532" />
<glyph unicode="&#xa0;" horiz-adv-x="532" />
<glyph unicode="!" horiz-adv-x="530" d="M555 1462l-274 -1059h-109q49 307 176 1059h207zM43 78q0 76 39.5 120t107.5 44q45 0 73 -27.5t28 -81.5q0 -68 -39 -115t-105 -47q-49 0 -76.5 28t-27.5 79z" />
<glyph unicode="&#x22;" horiz-adv-x="791" d="M485 1462l-153 -528h-107l72 528h188zM834 1462l-154 -528h-107l72 528h189z" />
<glyph unicode="#" horiz-adv-x="1323" d="M1036 899l-98 -340h285l-13 -129h-309l-125 -430h-139l129 430h-303l-127 -430h-133l121 430h-261l13 129h284l101 340h-277l13 127h301l123 436h139l-125 -436h305l127 436h133l-125 -436h264l-12 -127h-291zM500 559h303l96 340h-303z" />
<glyph unicode="$" d="M942 492q0 -162 -112.5 -263t-309.5 -123l-49 -225h-133l49 223q-195 14 -315 72v154q82 -41 175.5 -63.5t166.5 -22.5l98 452q-139 49 -201.5 123.5t-62.5 188.5q0 159 108 255t299 113l39 176h133l-39 -178q159 -12 283 -76l-63 -135q-121 63 -248 72l-94 -440 q149 -55 212.5 -125t63.5 -178zM549 250q107 13 170 75t63 154q0 54 -33 96t-114 74zM627 1235q-108 -11 -167 -69t-59 -156q0 -53 34.5 -97.5t107.5 -70.5z" />
<glyph unicode="%" horiz-adv-x="1624" d="M1468 1462l-1085 -1462h-152l1086 1462h151zM1284 772q-67 0 -119.5 -64.5t-84 -188.5t-31.5 -240q0 -173 106 -173q65 0 117 65t86.5 198.5t34.5 236.5q0 166 -109 166zM1532 621q0 -107 -31.5 -236t-82 -217.5t-121 -138t-156.5 -49.5q-110 0 -171 74.5t-61 219.5 q0 166 50.5 318.5t136.5 228.5t200 76q116 0 176 -71.5t60 -204.5zM543 1358q-66 0 -119 -63t-85 -187.5t-32 -243.5q0 -172 107 -172q52 0 94 39.5t73.5 114t50.5 175t19 171.5q0 166 -108 166zM791 1206q0 -108 -32 -237.5t-82.5 -217.5t-120.5 -137t-157 -49 q-109 0 -170 75t-61 220q0 166 50.5 318.5t136.5 228.5t200 76q116 0 176 -72t60 -205z" />
<glyph unicode="&#x26;" horiz-adv-x="1372" d="M737 1348q-105 0 -163 -60t-58 -170q0 -120 82 -235q139 71 191 110t83 85t31 104q0 77 -42.5 121.5t-123.5 44.5zM471 119q87 0 168 33t195 124l-306 433q-128 -67 -184 -116t-85.5 -107.5t-29.5 -131.5q0 -106 66.5 -170.5t175.5 -64.5zM66 342q0 148 90 257.5 t303 211.5q-103 165 -103 309q0 164 106 264.5t281 100.5q149 0 236.5 -79t87.5 -212q0 -78 -32.5 -137t-87.5 -108t-127.5 -90t-153.5 -83l278 -389q127 110 199 295h168q-101 -236 -283 -412l203 -270h-201l-117 166q-120 -100 -230 -143t-247 -43q-168 0 -269 96 t-101 266z" />
<glyph unicode="'" horiz-adv-x="444" d="M485 1462l-153 -528h-107l72 528h188z" />
<glyph unicode="(" horiz-adv-x="584" d="M82 272q0 339 120 627t384 563h157q-246 -270 -371.5 -570t-125.5 -618q0 -339 114 -598h-131q-147 266 -147 596z" />
<glyph unicode=")" horiz-adv-x="584" d="M500 864q0 -341 -121.5 -629.5t-382.5 -558.5h-156q496 551 496 1188q0 341 -113 598h131q146 -269 146 -598z" />
<glyph unicode="*" horiz-adv-x="1130" d="M854 1528l-123 -383l422 18l-8 -157l-393 47l180 -383l-166 -52l-113 406l-258 -344l-116 121l309 284l-373 109l55 154l371 -185l41 400z" />
<glyph unicode="+" d="M516 651h-389v142h389v391h141v-391h390v-142h-390v-387h-141v387z" />
<glyph unicode="," horiz-adv-x="492" d="M291 238l8 -23q-113 -235 -270 -479h-129q126 286 204 502h187z" />
<glyph unicode="-" horiz-adv-x="639" d="M55 469l35 158h479l-34 -158h-480z" />
<glyph unicode="." horiz-adv-x="518" d="M43 74q0 77 40.5 122.5t111.5 45.5q43 0 69.5 -26t26.5 -79q0 -71 -40 -118.5t-108 -47.5q-46 0 -73 26t-27 77z" />
<glyph unicode="/" horiz-adv-x="717" d="M893 1462l-813 -1462h-174l813 1462h174z" />
<glyph unicode="0" d="M1092 1014q0 -295 -78.5 -539t-214 -369.5t-314.5 -125.5q-176 0 -270 127.5t-94 369.5q0 270 82 514.5t216.5 369t307.5 124.5q365 0 365 -471zM717 1341q-113 0 -209 -115.5t-155.5 -323t-59.5 -423.5q0 -172 50 -264t161 -92q115 0 209 114t150.5 328t56.5 453 q0 323 -203 323z" />
<glyph unicode="1" d="M588 0h-172l196 913q59 261 88 359q-50 -53 -139 -111l-178 -110l-80 127l449 284h149z" />
<glyph unicode="2" d="M911 0h-899l31 147l465 420q102 93 176.5 163.5t123 133t72 124t23.5 136.5q0 99 -60 157t-163 58q-77 0 -150.5 -28.5t-162.5 -96.5l-82 115q191 154 413 154q176 0 278.5 -88.5t102.5 -243.5q0 -111 -39.5 -204t-131 -197t-294.5 -281l-352 -307v-8h678z" />
<glyph unicode="3" d="M1073 1159q0 -156 -101 -264.5t-280 -140.5v-9q124 -23 195 -106.5t71 -208.5q0 -133 -62 -234.5t-181 -158.5t-283 -57q-210 0 -385 79v164q94 -49 199 -75.5t190 -26.5q162 0 252 79.5t90 217.5q0 131 -79 198.5t-220 67.5h-131l31 143h139q165 0 274 87t109 227 q0 92 -58 146t-157 54q-80 0 -157 -27t-175 -93l-80 118q195 144 424 144q179 0 277 -87t98 -237z" />
<glyph unicode="4" d="M1067 334h-233l-72 -334h-164l74 334h-656l29 158l834 978h196l-207 -983h232zM705 487q46 220 78 373t116 445h-8q-17 -29 -66.5 -96.5t-72.5 -96.5l-533 -625h486z" />
<glyph unicode="5" d="M598 893q182 0 289.5 -104.5t107.5 -282.5q0 -161 -73 -283t-204 -182.5t-308 -60.5q-193 0 -330 79v164q164 -102 334 -102q191 0 298 96t107 268q0 126 -73.5 199.5t-204.5 73.5q-48 0 -97 -6.5t-139 -30.5l-74 57l197 684h668l-33 -153h-522l-127 -439q87 23 184 23z " />
<glyph unicode="6" d="M133 424q0 209 60.5 415t163.5 351.5t246 219t327 73.5q111 0 184 -23l-35 -145q-68 22 -170 22q-212 0 -356.5 -149t-212.5 -443h8q59 79 146.5 126t193.5 47q154 0 244 -98.5t90 -270.5q0 -161 -66.5 -294.5t-180.5 -204t-261 -70.5q-182 0 -281.5 115t-99.5 329z M532 121q94 0 167.5 54t114 149t40.5 208q0 248 -221 248q-66 0 -128 -28.5t-110 -76t-72 -104.5t-24 -155q0 -137 60.5 -216t172.5 -79z" />
<glyph unicode="7" d="M174 0l768 1313h-719l31 149h891l-27 -139l-764 -1323h-180z" />
<glyph unicode="8" d="M741 1485q174 0 271.5 -86.5t97.5 -235.5q0 -129 -78 -225t-266 -176q127 -78 180 -165t53 -202q0 -122 -60 -217.5t-172.5 -146.5t-264.5 -51q-190 0 -298 98.5t-108 267.5q0 148 95 256t296 184q-95 69 -135.5 144.5t-40.5 171.5q0 111 54.5 198.5t153.5 136t222 48.5z M608 700q-185 -60 -261.5 -143.5t-76.5 -202.5q0 -107 69 -170t181 -63q139 0 222 74t83 196q0 99 -52 174t-165 135zM729 1341q-114 0 -182 -65.5t-68 -175.5q0 -82 39 -144t127 -116q161 60 228 131.5t67 173.5q0 90 -57.5 143t-153.5 53z" />
<glyph unicode="9" d="M1067 1034q0 -194 -56 -406.5t-147.5 -360t-221.5 -217.5t-302 -70q-136 0 -242 34v158q134 -47 246 -47q202 0 327 141t189 441h-10q-51 -75 -132.5 -118.5t-180.5 -43.5q-169 0 -261 98.5t-92 288.5q0 153 64.5 280.5t180 199t259.5 71.5q180 0 279.5 -114.5 t99.5 -334.5zM682 1341q-150 0 -241 -113t-91 -290q0 -124 54.5 -190t162.5 -66q76 0 140 28.5t108.5 81.5t65 114t20.5 151q0 131 -59 207.5t-160 76.5z" />
<glyph unicode=":" horiz-adv-x="518" d="M43 74q0 77 40.5 122.5t111.5 45.5q43 0 69.5 -26t26.5 -79q0 -71 -40 -118.5t-108 -47.5q-46 0 -73 26t-27 77zM203 956q0 77 40 122.5t111 45.5q97 0 97 -104q0 -73 -41.5 -119.5t-106.5 -46.5q-46 0 -73 26.5t-27 75.5z" />
<glyph unicode=";" horiz-adv-x="518" d="M291 238l8 -23q-113 -235 -270 -479h-129q126 286 204 502h187zM203 956q0 77 40 122.5t111 45.5q97 0 97 -104q0 -73 -41.5 -119.5t-106.5 -46.5q-46 0 -73 26.5t-27 75.5z" />
<glyph unicode="&#x3c;" d="M1040 242l-919 422v98l919 479v-149l-747 -371l747 -328v-151z" />
<glyph unicode="=" d="M127 858v139h920v-139h-920zM127 444v142h920v-142h-920z" />
<glyph unicode="&#x3e;" d="M121 393l745 328l-745 371v149l919 -479v-98l-919 -422v151z" />
<glyph unicode="?" horiz-adv-x="874" d="M260 403q22 130 72.5 213.5t165.5 174.5q128 100 168 144t63 94t23 112q0 93 -51.5 143.5t-147.5 50.5q-81 0 -155 -25.5t-140 -56.5l-61 129q92 51 192 76t182 25q167 0 259 -84t92 -238q0 -123 -65.5 -226.5t-225.5 -223.5q-125 -91 -169 -147.5t-67 -160.5h-135z M158 74q0 77 40 122.5t111 45.5q44 0 70.5 -26t26.5 -79q0 -73 -41.5 -119.5t-106.5 -46.5q-46 0 -73 26t-27 77z" />
<glyph unicode="@" horiz-adv-x="1735" d="M1679 852q0 -179 -55 -324t-155 -227t-222 -82q-197 0 -213 184h-8q-111 -184 -291 -184q-115 0 -180.5 75.5t-65.5 209.5q0 157 68 284t188.5 199t260.5 72q65 0 127.5 -12t150.5 -48q-64 -242 -98 -368t-31 -172q0 -117 102 -117q78 0 141.5 67t100.5 183.5t37 243.5 q0 239 -128 367t-370 128q-228 0 -406.5 -107t-277 -295.5t-98.5 -416.5q0 -270 143.5 -418.5t409.5 -148.5q197 0 420 86v-127q-219 -90 -443 -90q-314 0 -494.5 184.5t-180.5 505.5q0 261 126.5 485.5t343.5 347.5t486 123q191 0 329 -75.5t210.5 -213.5t72.5 -319z M985 938q-94 0 -174 -55t-126 -153t-46 -212q0 -93 33 -134.5t98 -41.5q187 0 272 315l70 258q-63 23 -127 23z" />
<glyph unicode="A" horiz-adv-x="1137" d="M813 465h-496l-245 -465h-189l799 1462h174l184 -1462h-170zM795 621l-35 299q-24 179 -29 350q-37 -88 -80.5 -175t-249.5 -474h394z" />
<glyph unicode="B" horiz-adv-x="1225" d="M770 1462q432 0 432 -336q0 -141 -87 -238t-245 -126v-10q115 -32 176.5 -110.5t61.5 -188.5q0 -212 -152 -332.5t-407 -120.5h-463l309 1462h375zM434 836h248q156 0 249 73t93 199q0 104 -66.5 155.5t-209.5 51.5h-211zM287 145h266q181 0 278 80.5t97 227.5 q0 116 -74.5 177.5t-214.5 61.5h-236z" />
<glyph unicode="C" horiz-adv-x="1198" d="M942 1331q-174 0 -311.5 -97t-218 -284.5t-80.5 -408.5q0 -187 97.5 -298.5t268.5 -111.5q139 0 322 57v-149q-86 -31 -164 -45t-188 -14q-242 0 -380 149.5t-138 407.5q0 261 105.5 485.5t283.5 342.5t403 118q197 0 348 -80l-69 -141q-138 69 -279 69z" />
<glyph unicode="D" horiz-adv-x="1364" d="M1300 877q0 -261 -105 -461t-300 -308t-457 -108h-352l309 1462h342q276 0 419.5 -149.5t143.5 -435.5zM449 147q202 0 355 91.5t234.5 258.5t81.5 382t-103 325.5t-302 110.5h-178l-250 -1168h162z" />
<glyph unicode="E" horiz-adv-x="1047" d="M821 0h-735l309 1462h735l-32 -153h-566l-98 -469h527l-29 -152h-529l-114 -536h565z" />
<glyph unicode="F" horiz-adv-x="967" d="M258 0h-172l309 1462h735l-30 -153h-568l-110 -533h528l-32 -153h-529z" />
<glyph unicode="G" horiz-adv-x="1386" d="M821 766h459l-154 -711q-216 -75 -419 -75q-264 0 -410.5 144.5t-146.5 403.5q0 269 101.5 489.5t281.5 343t399 122.5q117 0 219.5 -20t206.5 -64l-66 -152q-77 34 -165.5 59t-194.5 25q-169 0 -307.5 -101.5t-215.5 -283.5t-77 -407q0 -190 102.5 -299t286.5 -109 q154 0 260 39l96 444h-289z" />
<glyph unicode="H" horiz-adv-x="1389" d="M1085 0h-167l143 688h-660l-145 -688h-170l309 1462h170l-131 -622h660l133 622h168z" />
<glyph unicode="I" horiz-adv-x="559" d="M86 0l311 1462h168l-311 -1462h-168z" />
<glyph unicode="J" horiz-adv-x="547" d="M-166 -385q-105 0 -153 25l6 147q69 -20 145 -20q100 0 165.5 62.5t90.5 182.5l307 1450h170l-309 -1468q-79 -379 -422 -379z" />
<glyph unicode="K" horiz-adv-x="1141" d="M1034 0h-186q-72 181 -146.5 359.5t-146.5 361.5l-174 -131l-125 -590h-170l309 1462h170l-151 -710l700 710h209l-639 -637z" />
<glyph unicode="L" horiz-adv-x="971" d="M86 0l309 1462h170l-276 -1308h565l-33 -154h-735z" />
<glyph unicode="M" horiz-adv-x="1714" d="M676 0l-166 1296h-8q-7 -72 -28.5 -197.5t-37.5 -199.5l-190 -899h-162l309 1462h244l149 -1204h9l659 1204h266l-303 -1462h-174q126 590 193 905.5t94 392.5h-6l-717 -1298h-131z" />
<glyph unicode="N" horiz-adv-x="1438" d="M1139 0h-181l-460 1223h-6q-32 -221 -74 -418l-172 -805h-162l309 1462h180l459 -1220h6q30 224 72 405l174 815h164z" />
<glyph unicode="O" horiz-adv-x="1475" d="M1411 907q0 -269 -88 -481.5t-252 -329t-379 -116.5q-256 0 -399 149.5t-143 419.5q0 264 96 482t263.5 336t377.5 118q244 0 384 -154t140 -424zM874 1333q-157 0 -281 -101t-192.5 -281t-68.5 -398q0 -199 98 -310.5t266 -111.5q152 0 272.5 97.5t190.5 279.5t70 403 q0 199 -94 310.5t-261 111.5z" />
<glyph unicode="P" horiz-adv-x="1159" d="M1159 1085q0 -248 -164 -379t-481 -131h-135l-123 -575h-170l309 1462h330q214 0 324 -94.5t110 -282.5zM410 721h133q216 0 328 91t112 267q0 125 -69.5 180.5t-213.5 55.5h-163z" />
<glyph unicode="Q" horiz-adv-x="1475" d="M874 1333q-157 0 -281 -101t-192.5 -281t-68.5 -398q0 -199 98 -310.5t266 -111.5q158 0 279 100t187.5 280.5t66.5 399.5q0 199 -94 310.5t-261 111.5zM725 -18l-17 -2h-16q-256 0 -399 149.5t-143 419.5q0 264 96 482t263.5 336t377.5 118q244 0 384 -154t140 -424 q0 -333 -139 -576t-375 -321l274 -358h-219z" />
<glyph unicode="R" horiz-adv-x="1165" d="M385 608l-129 -608h-170l309 1462h320q446 0 446 -366q0 -348 -368 -449l239 -647h-186l-209 608h-252zM532 1313q-102 -494 -116 -559h168q193 0 297 85t104 244q0 121 -67.5 175.5t-219.5 54.5h-166z" />
<glyph unicode="S" horiz-adv-x="1028" d="M913 424q0 -211 -140.5 -327.5t-395.5 -116.5q-106 0 -186.5 14.5t-151.5 48.5v170q162 -84 340 -84q162 0 257 75.5t95 207.5q0 78 -52.5 137.5t-195.5 140.5q-151 85 -209.5 170t-58.5 201q0 187 132 304.5t347 117.5q99 0 184.5 -19t180.5 -65l-66 -150 q-66 38 -148 60t-151 22q-134 0 -215.5 -69.5t-81.5 -188.5q0 -54 17 -92.5t54 -72.5t142 -95q147 -88 198.5 -138t78 -110.5t26.5 -140.5z" />
<glyph unicode="T" horiz-adv-x="1020" d="M487 0h-172l277 1311h-406l33 151h985l-30 -151h-408z" />
<glyph unicode="U" horiz-adv-x="1384" d="M1407 1462l-205 -966q-55 -263 -197.5 -389.5t-388.5 -126.5q-230 0 -341 104t-111 299q0 81 24 201l189 878h170l-191 -891q-22 -106 -22 -188q0 -117 73 -184.5t218 -67.5q172 0 267.5 87.5t139.5 289.5l205 954h170z" />
<glyph unicode="V" horiz-adv-x="1122" d="M479 197q61 144 162 338l479 927h191l-781 -1462h-180l-162 1462h170l97 -930q20 -196 20 -335h4z" />
<glyph unicode="W" horiz-adv-x="1745" d="M1257 0h-170l-49 965q-8 136 -8 282h-6q-25 -72 -61 -154.5t-504 -1092.5h-174l-62 1462h170l31 -901l2 -88q0 -98 -10 -258h6q89 243 156 383l405 864h178l43 -860q9 -153 9 -304l-1 -83h9q75 224 131 354l387 893h182z" />
<glyph unicode="X" horiz-adv-x="1063" d="M956 0h-180l-213 641l-481 -641h-186l596 776l-263 686h172l203 -563l443 563h186l-555 -694z" />
<glyph unicode="Y" horiz-adv-x="1030" d="M537 715l489 747h193l-627 -921l-113 -541h-172l119 549l-238 913h170z" />
<glyph unicode="Z" horiz-adv-x="1087" d="M885 0h-901l28 137l924 1170h-655l32 155h858l-26 -139l-924 -1169h697z" />
<glyph unicode="[" horiz-adv-x="586" d="M371 -324h-387l381 1786h387l-31 -141h-227l-318 -1503h227z" />
<glyph unicode="\" horiz-adv-x="717" d="M375 1462l217 -1462h-154l-217 1462h154z" />
<glyph unicode="]" horiz-adv-x="586" d="M-119 -182h225l320 1503h-227l30 141h389l-380 -1786h-388z" />
<glyph unicode="^" horiz-adv-x="1059" d="M53 553l598 920h109l266 -920h-145l-201 747l-467 -747h-160z" />
<glyph unicode="_" horiz-adv-x="807" d="M623 -324h-811l30 140h811z" />
<glyph unicode="`" horiz-adv-x="1135" d="M903 1241h-104q-61 61 -128.5 154t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="a" horiz-adv-x="1157" d="M639 1116q92 0 164 -49.5t112 -142.5h11l67 172h127l-233 -1096h-133l26 209h-8q-179 -229 -377 -229q-139 0 -218 99t-79 271q0 208 71 386t196 279t274 101zM449 119q97 0 193 92.5t156 241t60 297.5q0 103 -56 164t-147 61q-104 0 -193.5 -86t-140.5 -233t-51 -310 q0 -114 47 -170.5t132 -56.5z" />
<glyph unicode="b" horiz-adv-x="1182" d="M545 -20q-97 0 -170.5 51t-110.5 139h-10l-70 -170h-125l330 1556h168q-51 -242 -78.5 -370.5t-75.5 -300.5h9q93 118 183.5 173.5t186.5 55.5q141 0 220 -99t79 -272q0 -209 -68.5 -386.5t-191 -277t-276.5 -99.5zM731 975q-96 0 -195.5 -95t-158 -239t-58.5 -295 q0 -110 55.5 -168.5t160.5 -58.5q99 0 184.5 81t137.5 230.5t52 317.5q0 227 -178 227z" />
<glyph unicode="c" horiz-adv-x="922" d="M506 -20q-194 0 -301 107t-107 302q0 200 74 369t204.5 263.5t293.5 94.5q137 0 268 -51l-47 -141q-120 51 -219 51q-112 0 -204.5 -76.5t-145 -213t-52.5 -296.5q0 -128 66.5 -199t183.5 -71q72 0 136 20t126 47v-143q-124 -63 -276 -63z" />
<glyph unicode="d" horiz-adv-x="1182" d="M639 1114q194 0 281 -190h10q17 155 45 274l78 358h166l-330 -1556h-139l22 209h-8q-101 -125 -189 -177t-182 -52q-139 0 -217 98t-78 272q0 214 72 392t194.5 275t274.5 97zM449 119q94 0 194 93.5t158.5 239t58.5 296.5q0 111 -54 169t-157 58q-101 0 -187.5 -82.5 t-139 -232t-52.5 -314.5q0 -227 179 -227z" />
<glyph unicode="e" horiz-adv-x="1010" d="M492 -20q-184 0 -289 109t-105 302q0 188 74.5 360.5t197.5 268.5t271 96q153 0 230 -66.5t77 -185.5q0 -180 -166 -282.5t-475 -102.5h-33l-4 -80q0 -131 61.5 -204.5t190.5 -73.5q63 0 129.5 18t165.5 66v-146q-94 -44 -166 -61.5t-159 -17.5zM631 973 q-103 0 -193.5 -94t-138.5 -261h12q228 0 349.5 59.5t121.5 172.5q0 53 -36.5 88t-114.5 35z" />
<glyph unicode="f" horiz-adv-x="641" d="M-104 -492q-69 0 -125 21v141q64 -22 112 -22q76 0 117 62t66 177l227 1082h-193l13 67l206 66l23 100q46 200 127.5 282.5t241.5 82.5q40 0 98 -11.5t90 -25.5l-43 -129q-76 29 -137 29q-87 0 -133.5 -48.5t-75.5 -177.5l-25 -108h238l-25 -127h-237l-232 -1098 q-39 -189 -120 -276t-213 -87z" />
<glyph unicode="g" horiz-adv-x="1026" d="M1100 1096l-25 -107l-211 -24q41 -62 41 -158q0 -191 -116.5 -304.5t-311.5 -113.5q-55 0 -84 8q-139 -53 -139 -131q0 -41 33 -54.5t96 -21.5l117 -14q181 -22 262.5 -88t81.5 -194q0 -184 -146 -285t-411 -101q-194 0 -304 73.5t-110 207.5q0 105 72 182t233 131 q-78 41 -78 121q0 69 51 118.5t142 92.5q-63 32 -103 94.5t-40 145.5q0 194 119.5 318t305.5 124q78 0 154 -20h371zM35 -195q0 -77 65 -122t193 -45q182 0 284.5 63.5t102.5 179.5q0 62 -54 98t-184 50l-159 16q-120 -25 -184 -88t-64 -152zM313 680q0 -85 45 -129.5 t125 -44.5q79 0 138 42t90.5 115.5t31.5 159.5q0 82 -44 125t-126 43q-78 0 -136.5 -40.5t-91 -113t-32.5 -157.5z" />
<glyph unicode="h" horiz-adv-x="1182" d="M729 0l148 692q18 94 18 135q0 148 -147 148q-89 0 -173.5 -59t-149 -171.5t-97.5 -271.5l-101 -473h-168l330 1556h168q-18 -82 -34.5 -159t-34 -156.5t-38 -166.5t-47.5 -189h11q94 123 185.5 176t191.5 53q131 0 202.5 -72t71.5 -204q0 -62 -23 -166 q-39 -193 -145 -672h-168z" />
<glyph unicode="i" horiz-adv-x="520" d="M227 0h-168l234 1096h168zM340 1376q0 56 32 91.5t83 35.5q88 0 88 -90q0 -55 -33.5 -93t-77.5 -38q-40 0 -66 24.5t-26 69.5z" />
<glyph unicode="j" horiz-adv-x="520" d="M-135 -492q-69 0 -123 21v141q61 -22 119 -22q125 0 168 205l264 1243h166l-266 -1258q-36 -171 -114.5 -250.5t-213.5 -79.5zM340 1376q0 56 32 91.5t83 35.5q86 0 86 -90q0 -55 -33.5 -93t-77.5 -38q-38 0 -64 24.5t-26 69.5z" />
<glyph unicode="k" horiz-adv-x="999" d="M330 559l526 537h201l-469 -467l295 -629h-187l-235 524l-152 -123l-82 -401h-170l330 1556h170l-129 -602t-102 -395h4z" />
<glyph unicode="l" horiz-adv-x="520" d="M225 0h-168l332 1556h168z" />
<glyph unicode="m" horiz-adv-x="1786" d="M696 0l148 692q18 94 18 135q0 70 -31 109t-106 39q-84 0 -163.5 -60t-140 -171.5t-93.5 -268.5l-101 -475h-168l234 1096h139l-22 -203h10q87 119 173.5 171t178.5 52q113 0 174 -65t72 -181h8q86 125 183 185.5t196 60.5q127 0 196.5 -68t69.5 -198q0 -68 -22 -178 l-144 -672h-170l148 692q20 104 20 146q0 62 -34.5 99.5t-108.5 37.5q-81 0 -160 -58t-138.5 -164.5t-90.5 -252.5l-107 -500h-168z" />
<glyph unicode="n" horiz-adv-x="1182" d="M729 0l148 692q20 104 20 144q0 63 -35.5 101t-113.5 38q-89 0 -173.5 -60t-149 -171t-97.5 -269l-101 -475h-168l234 1096h139l-22 -203h10q96 122 185.5 172.5t185.5 50.5q127 0 200.5 -69.5t73.5 -194.5q0 -79 -23 -180l-143 -672h-170z" />
<glyph unicode="o" horiz-adv-x="1149" d="M643 1110q190 0 300 -112.5t110 -309.5q0 -188 -72 -355t-195 -258t-278 -91q-192 0 -301 113t-109 309q0 190 73 357.5t197 257t275 89.5zM879 711q0 115 -62.5 186.5t-169.5 71.5q-109 0 -195.5 -74t-134 -205.5t-47.5 -292.5q0 -131 63.5 -202.5t182.5 -71.5 q104 0 187 73t129.5 207.5t46.5 307.5z" />
<glyph unicode="p" horiz-adv-x="1182" d="M545 -20q-97 0 -170 50t-113 140h-10l-4 -38q-3 -25 -10.5 -70t-114.5 -554h-166l336 1588h139l-26 -209h8q179 227 372 227q137 0 216 -97.5t79 -273.5q0 -212 -69 -389t-191 -275.5t-276 -98.5zM731 975q-96 0 -195.5 -95t-158 -239t-58.5 -295q0 -110 55.5 -168.5 t160.5 -58.5q99 0 184.5 81t137.5 230.5t52 317.5q0 227 -178 227z" />
<glyph unicode="q" horiz-adv-x="1182" d="M641 1116q94 0 165.5 -50.5t108.5 -141.5h13l67 172h125l-336 -1588h-166l101 480q9 45 57 221h-8q-95 -121 -185 -175t-186 -54q-140 0 -219.5 97.5t-79.5 272.5q0 212 72.5 392t196 277t274.5 97zM449 119q92 0 190 92t158.5 237t60.5 300q0 105 -54.5 166t-152.5 61 q-101 0 -189 -84.5t-140 -233t-52 -311.5q0 -227 179 -227z" />
<glyph unicode="r" horiz-adv-x="811" d="M752 1116q69 0 120 -14l-36 -150q-53 13 -105 13q-91 0 -170.5 -60t-139 -166.5t-87.5 -236.5l-107 -502h-168l234 1096h139l-22 -203h10q72 95 119 136.5t98.5 64t114.5 22.5z" />
<glyph unicode="s" horiz-adv-x="877" d="M735 311q0 -156 -110.5 -243.5t-311.5 -87.5q-169 0 -305 69v158q70 -42 151 -65t150 -23q126 0 190 50t64 128q0 57 -35 96t-151 107q-130 73 -184 143t-54 166q0 138 101 222.5t266 84.5q171 0 330 -74l-54 -137l-56 25q-101 43 -220 43q-93 0 -146 -43.5t-53 -112.5 q0 -56 35.5 -96t146.5 -103q107 -60 153.5 -103t69.5 -92.5t23 -111.5z" />
<glyph unicode="t" horiz-adv-x="664" d="M395 117q55 0 144 26v-129q-34 -14 -84 -24t-80 -10q-125 0 -191.5 59.5t-66.5 177.5q0 66 18 150l127 602h-172l14 73l185 78l125 228h98l-55 -252h274l-26 -127h-273l-129 -604q-18 -87 -18 -132q0 -56 29 -86t81 -30z" />
<glyph unicode="u" horiz-adv-x="1182" d="M451 1096l-150 -695q-18 -89 -18 -139q0 -143 147 -143q88 0 173 60t150 172t99 270l100 475h166l-231 -1096h-139l22 203h-12q-98 -125 -187 -174t-184 -49q-128 0 -201 69.5t-73 198.5q0 62 22 172l146 676h170z" />
<glyph unicode="v" horiz-adv-x="946" d="M223 0l-125 1096h168l64 -613q24 -258 24 -362h6q127 275 179 371l325 604h178l-591 -1096h-228z" />
<glyph unicode="w" horiz-adv-x="1468" d="M831 0l-32 602q-4 94 -4 172v156h-9l-50 -118l-83 -189l-291 -623h-202l-43 1096h164l18 -594v-88q0 -147 -8 -269h6q47 124 137 322l295 629h182l37 -594q6 -168 6 -262v-53l-2 -42h6q28 86 83 218.5t323 732.5h178l-506 -1096h-205z" />
<glyph unicode="x" horiz-adv-x="979" d="M467 434l-346 -434h-195l475 565l-239 531h170l174 -412l330 412h194l-455 -539l252 -557h-168z" />
<glyph unicode="y" horiz-adv-x="946" d="M98 1096h168l74 -545q10 -69 19.5 -203.5t9.5 -216.5h6q35 87 87 200t77 156l325 609h178l-696 -1282q-93 -172 -184 -239t-219 -67q-72 0 -140 21v135q63 -18 131 -18q82 0 140.5 50.5t113.5 149.5l76 136z" />
<glyph unicode="z" horiz-adv-x="909" d="M690 0h-719l23 117l694 854h-479l27 125h657l-29 -140l-680 -831h531z" />
<glyph unicode="{" horiz-adv-x="715" d="M442 -324q-141 0 -216.5 52.5t-75.5 171.5q0 52 20 141q33 146 51.5 227.5t14.5 102.5q0 143 -209 143l32 143q118 0 189.5 43.5t93.5 147.5l68 326q34 160 117.5 224t254.5 64h33l-31 -141q-105 0 -151 -36.5t-66 -123.5l-71 -321q-28 -123 -91 -184t-167 -78v-5 q151 -41 151 -213q0 -59 -18 -131l-47 -211q-15 -58 -15 -98q0 -53 36.5 -77.5t119.5 -24.5v-142h-23z" />
<glyph unicode="|" d="M541 1556h139v-2052h-139v2052z" />
<glyph unicode="}" horiz-adv-x="715" d="M285 1462q144 0 220.5 -52.5t76.5 -170.5q0 -48 -21 -141l-49 -219q-16 -68 -16 -111q0 -143 209 -143l-33 -144q-119 0 -190 -43t-93 -147l-67 -326q-36 -164 -119 -226.5t-264 -62.5h-13v142q115 0 167 36t71 123l72 322q25 117 88 179.5t170 80.5v6q-150 42 -150 211 q0 59 18 131l50 213q14 65 14 99q0 53 -40.5 77.5t-139.5 24.5l28 141h11z" />
<glyph unicode="~" d="M348 713q-54 0 -118.5 -34.5t-114.5 -86.5v151q98 109 243 109q69 0 127 -14.5t144 -51.5q64 -27 112.5 -41t98.5 -14q55 0 119.5 33t115.5 88v-150q-100 -110 -244 -110q-72 0 -135 16.5t-135 48.5q-75 32 -120 44t-93 12z" />
<glyph unicode="&#xa1;" horiz-adv-x="530" d="M260 684h109l-176 -1057h-207zM250 950q0 76 40.5 122t110.5 46q44 0 70.5 -26t26.5 -80q0 -71 -40.5 -117.5t-105.5 -46.5q-48 0 -75 25.5t-27 76.5z" />
<glyph unicode="&#xa2;" d="M582 -20h-125l43 215q-132 34 -203.5 137.5t-71.5 257.5q0 185 63.5 344t178.5 258.5t260 120.5l35 170h123l-37 -168q119 -9 217 -49l-47 -142q-109 52 -219 52q-112 0 -204.5 -76.5t-145 -213t-52.5 -296.5q0 -125 66 -198t184 -73q72 0 136 20t126 48v-143 q-123 -62 -286 -66z" />
<glyph unicode="&#xa3;" d="M842 1481q184 0 336 -86l-66 -133q-146 79 -278 79q-213 0 -263 -237l-69 -326h370l-26 -127h-371l-47 -219q-22 -98 -66 -166.5t-124 -111.5h725l-33 -154h-953l27 141q205 46 258 289l47 221h-200l26 127h201l76 350q75 353 430 353z" />
<glyph unicode="&#xa4;" d="M229 723q0 117 68 215l-129 129l92 92l127 -129q103 70 217 70t215 -70l129 129l92 -90l-129 -129q70 -104 70 -217q0 -119 -70 -217l127 -127l-90 -90l-129 127q-98 -68 -215 -68q-119 0 -217 70l-127 -127l-90 90l127 127q-68 96 -68 215zM358 723q0 -103 71.5 -174.5 t174.5 -71.5q104 0 177 71.5t73 174.5q0 104 -73 177t-177 73q-102 0 -174 -72.5t-72 -177.5z" />
<glyph unicode="&#xa5;" d="M582 715l491 747h187l-533 -770h231l-28 -133h-297l-33 -160h297l-29 -133h-295l-57 -266h-154l56 266h-291l29 133h290l33 160h-291l29 133h225l-202 770h163z" />
<glyph unicode="&#xa6;" d="M541 1556h139v-776h-139v776zM541 281h139v-777h-139v777z" />
<glyph unicode="&#xa7;" horiz-adv-x="995" d="M174 770q0 201 238 307q-119 70 -119 203q0 127 103.5 206t279.5 79q189 0 321 -68l-53 -123q-148 60 -266 60q-102 0 -162.5 -40.5t-60.5 -109.5q0 -49 38 -83.5t162 -90.5q100 -44 149 -83.5t75 -89.5t26 -114q0 -97 -61 -180t-172 -139q114 -71 114 -189 q0 -152 -114 -237.5t-318 -85.5q-176 0 -295 61v148q56 -34 136.5 -56t156.5 -22q133 0 204 44.5t71 129.5q0 48 -50.5 89t-152.5 87q-138 61 -194 130.5t-56 166.5zM514 1010q-82 -26 -135 -87.5t-53 -131.5t50.5 -117t198.5 -111q80 44 127.5 107t47.5 131 q0 60 -49.5 105.5t-186.5 103.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="1135" d="M457 1378q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM821 1378q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xa9;" horiz-adv-x="1704" d="M928 1059q-125 0 -191.5 -87t-66.5 -241q0 -169 65 -249.5t193 -80.5q82 0 211 43v-122q-66 -28 -113 -38t-104 -10q-192 0 -297 119.5t-105 339.5q0 208 110 330.5t300 122.5q130 0 248 -60l-60 -120q-106 53 -190 53zM139 731q0 200 100 375t275 276t377 101 q197 0 370 -97t277 -272t104 -383q0 -204 -100.5 -376.5t-273 -273.5t-377.5 -101q-207 0 -382 103.5t-272.5 276.5t-97.5 371zM244 731q0 -173 87 -323.5t237.5 -237t322.5 -86.5q174 0 323 87t236.5 235.5t87.5 324.5q0 174 -87 323t-235.5 236.5t-324.5 87.5 q-174 0 -323 -87t-236.5 -235.5t-87.5 -324.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="686" d="M498 1479q114 0 153 -103h6l37 90h86l-139 -665h-92l14 117h-4q-40 -56 -90 -93t-123 -37q-77 0 -126.5 60t-49.5 166q0 127 41.5 234.5t116.5 169t170 61.5zM381 891q61 0 112.5 49t86 137.5t34.5 167.5q0 62 -28.5 96.5t-85.5 34.5q-92 0 -154.5 -103t-62.5 -243 q0 -139 98 -139z" />
<glyph unicode="&#xab;" horiz-adv-x="958" d="M88 584l391 374l78 -81l-297 -328l172 -387l-113 -49l-231 442v29zM483 541l367 405l86 -69l-283 -365l158 -350l-113 -49l-215 397v31z" />
<glyph unicode="&#xac;" d="M1047 793v-529h-140v387h-780v142h920z" />
<glyph unicode="&#xad;" horiz-adv-x="639" d="M55 469zM55 469l35 158h479l-34 -158h-480z" />
<glyph unicode="&#xae;" horiz-adv-x="1704" d="M139 731q0 200 100 375t275 276t377 101q197 0 370 -97t277 -272t104 -383q0 -204 -100.5 -376.5t-273 -273.5t-377.5 -101q-207 0 -382 103.5t-272.5 276.5t-97.5 371zM244 731q0 -173 87 -323.5t237.5 -237t322.5 -86.5q174 0 323 87t236.5 235.5t87.5 324.5 q0 174 -87 323t-235.5 236.5t-324.5 87.5q-174 0 -323 -87t-236.5 -235.5t-87.5 -324.5zM1194 915q0 -78 -47.5 -141t-132.5 -98l227 -385h-149l-207 352h-113v-352h-127v880h229q163 0 241.5 -63t78.5 -193zM772 762h92q195 0 195 149q0 76 -47.5 107t-149.5 31h-90v-287z " />
<glyph unicode="&#xaf;" horiz-adv-x="782" d="M1012 1556h-785l33 132h787z" />
<glyph unicode="&#xb0;" horiz-adv-x="877" d="M215 1171q0 128 90.5 220t220.5 92q83 0 155.5 -41.5t114.5 -114t42 -156.5q0 -128 -90.5 -218.5t-221.5 -90.5t-221 90.5t-90 218.5zM328 1171q0 -80 58 -138t140 -58q83 0 140 58.5t57 137.5q0 82 -57.5 140.5t-139.5 58.5q-80 0 -139 -58.5t-59 -140.5z" />
<glyph unicode="&#xb1;" d="M516 643h-389v141h389v392h141v-392h390v-141h-390v-387h-141v387zM127 0v141h920v-141h-920z" />
<glyph unicode="&#xb2;" horiz-adv-x="717" d="M657 586h-561l23 106l264 228q115 100 158.5 149.5t63.5 93t20 90.5q0 53 -31 85t-90 32q-90 0 -195 -80l-59 90q125 101 274 101q109 0 171.5 -56.5t62.5 -150.5q0 -99 -52.5 -179.5t-197.5 -205.5l-221 -187h395z" />
<glyph unicode="&#xb3;" horiz-adv-x="717" d="M549 1036q69 -17 108 -68t39 -120q0 -132 -91 -205.5t-253 -73.5q-125 0 -233 56v127q125 -72 239 -72q205 0 205 170q0 137 -178 137h-90l22 107h95q97 0 155 41t58 112q0 60 -34.5 90.5t-93.5 30.5q-102 0 -196 -68l-55 93q109 88 268 88q114 0 178 -56t64 -151 q0 -180 -207 -234v-4z" />
<glyph unicode="&#xb4;" horiz-adv-x="1135" d="M532 1268q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xb5;" horiz-adv-x="1194" d="M295 266q0 -147 147 -147q89 0 172 59t148.5 171t99.5 269l105 478h163l-233 -1096h-139l24 205h-12q-93 -121 -183 -173t-188 -52q-112 0 -163 96h-9q-11 -78 -22.5 -148t-83.5 -420h-164l336 1588h168l-148 -695q-18 -92 -18 -135z" />
<glyph unicode="&#xb6;" horiz-adv-x="1341" d="M1206 -260h-114v1661h-213v-1661h-115v819q-62 -18 -146 -18q-216 0 -317.5 125t-101.5 376q0 260 109 387t341 127h557v-1816z" />
<glyph unicode="&#xb7;" horiz-adv-x="518" d="M170 587zM170 690q0 77 40.5 122.5t111.5 45.5q43 0 69.5 -26t26.5 -79q0 -71 -40 -118.5t-108 -47.5q-46 0 -73 26t-27 77z" />
<glyph unicode="&#xb8;" horiz-adv-x="420" d="M211 -276q0 -98 -81 -157t-214 -59q-41 0 -86 9v100q38 -6 68 -6q174 0 174 110q0 46 -39 67.5t-99 29.5l101 182h106l-61 -121q131 -38 131 -155z" />
<glyph unicode="&#xb9;" horiz-adv-x="717" d="M537 1462h118l-186 -876h-135l112 526q25 103 58 225q-25 -25 -50 -46.5t-145 -100.5l-51 90z" />
<glyph unicode="&#xba;" horiz-adv-x="688" d="M745 1219q0 -191 -88.5 -311t-240.5 -120q-113 0 -180.5 71t-67.5 196q0 117 42 215.5t117.5 153.5t174.5 55q117 0 180 -67t63 -193zM500 1376q-102 0 -160.5 -87.5t-58.5 -229.5q0 -85 38 -127.5t107 -42.5q94 0 152.5 88.5t58.5 232.5q0 166 -137 166z" />
<glyph unicode="&#xbb;" horiz-adv-x="958" d="M872 485l-393 -370l-78 82l297 323l-172 391l113 50l233 -447v-29zM477 528l-368 -401l-86 70l282 360l-158 354l113 50l217 -402v-31z" />
<glyph unicode="&#xbc;" horiz-adv-x="1518" d="M123 0zM1362 1462l-1086 -1462h-153l1085 1462h154zM483 1462h118l-186 -876h-135l112 526q25 103 58 225q-25 -25 -50 -46.5t-145 -100.5l-51 90zM1327 203h-129l-43 -202h-127l43 202h-365l23 101l481 579h133l-121 -563h127zM1095 320q69 322 90 395 q-20 -36 -110 -149l-205 -246h225z" />
<glyph unicode="&#xbd;" horiz-adv-x="1518" d="M66 0zM427 1462h118l-186 -876h-135l112 526q25 103 58 225q-25 -25 -50 -46.5t-145 -100.5l-51 90zM1305 1462l-1086 -1462h-153l1085 1462h154zM1343 1h-561l23 106l264 228q115 100 158.5 149.5t63.5 93t20 90.5q0 53 -31 85t-90 32q-90 0 -195 -80l-59 90 q125 101 274 101q109 0 171.5 -56.5t62.5 -150.5q0 -99 -52.5 -179.5t-197.5 -205.5l-221 -187h395z" />
<glyph unicode="&#xbe;" horiz-adv-x="1565" d="M87 0zM517 1036q69 -17 108 -68t39 -120q0 -132 -91 -205.5t-253 -73.5q-125 0 -233 56v127q125 -72 239 -72q205 0 205 170q0 137 -178 137h-90l22 107h95q97 0 155 41t58 112q0 60 -34.5 90.5t-93.5 30.5q-102 0 -196 -68l-55 93q109 88 268 88q114 0 178 -56t64 -151 q0 -180 -207 -234v-4zM1512 1462l-1086 -1462h-153l1085 1462h154zM1477 203h-129l-43 -202h-127l43 202h-365l23 101l481 579h133l-121 -563h127zM1245 320q69 322 90 395q-20 -36 -110 -149l-205 -246h225z" />
<glyph unicode="&#xbf;" horiz-adv-x="874" d="M657 680q-22 -130 -72 -212t-165 -175l-95 -75q-159 -127 -159 -275q0 -93 51.5 -144t147.5 -51q80 0 154 25.5t140 56.5l62 -129q-90 -48 -189 -74t-186 -26q-168 0 -259 83.5t-91 237.5q0 124 66 228t225 223q132 98 172.5 152.5t62.5 154.5h135zM760 1010 q0 -77 -40.5 -122.5t-111.5 -45.5q-43 0 -69.5 26t-26.5 78q0 71 40 118.5t107 47.5q47 0 74 -25.5t27 -76.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1137" d="M0 0zM813 465h-496l-245 -465h-189l799 1462h174l184 -1462h-170zM795 621l-35 299q-24 179 -29 350q-37 -88 -80.5 -175t-249.5 -474h394zM863 1579h-104q-61 61 -128.5 154t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xc1;" horiz-adv-x="1137" d="M0 0zM813 465h-496l-245 -465h-189l799 1462h174l184 -1462h-170zM795 621l-35 299q-24 179 -29 350q-37 -88 -80.5 -175t-249.5 -474h394zM679 1606q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xc2;" horiz-adv-x="1137" d="M0 0zM813 465h-496l-245 -465h-189l799 1462h174l184 -1462h-170zM795 621l-35 299q-24 179 -29 350q-37 -88 -80.5 -175t-249.5 -474h394zM1080 1579h-103q-57 48 -161 189q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27 z" />
<glyph unicode="&#xc3;" horiz-adv-x="1137" d="M0 0zM813 465h-496l-245 -465h-189l799 1462h174l184 -1462h-170zM795 621l-35 299q-24 179 -29 350q-37 -88 -80.5 -175t-249.5 -474h394zM934 1581q-40 0 -77.5 17.5t-73 39t-69 39t-65.5 17.5q-44 0 -69.5 -28.5t-47.5 -86.5h-100q58 258 231 258q44 0 83.5 -18 t75 -39.5t66.5 -39.5t58 -18q44 0 69.5 27t51.5 90h100q-66 -258 -233 -258z" />
<glyph unicode="&#xc4;" horiz-adv-x="1137" d="M0 0zM813 465h-496l-245 -465h-189l799 1462h174l184 -1462h-170zM795 621l-35 299q-24 179 -29 350q-37 -88 -80.5 -175t-249.5 -474h394zM523 1716q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM887 1716q0 46 28 79.5 t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xc5;" horiz-adv-x="1137" d="M0 0zM813 465h-496l-245 -465h-189l799 1462h174l184 -1462h-170zM795 621l-35 299q-24 179 -29 350q-37 -88 -80.5 -175t-249.5 -474h394zM991 1585q0 -99 -60 -157t-159 -58q-101 0 -160 57.5t-59 155.5q0 94 62 152.5t157 58.5q101 0 160 -57t59 -152zM887 1583 q0 53 -32 84t-83 31q-49 0 -82 -31t-33 -84q0 -54 29.5 -84.5t85.5 -30.5q51 0 83 30.5t32 84.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1673" d="M1448 0h-737l98 465h-438l-293 -465h-197l938 1462h938l-33 -153h-565l-100 -469h528l-28 -150h-529l-115 -538h566zM840 621l147 688h-84l-434 -688h371z" />
<glyph unicode="&#xc7;" horiz-adv-x="1198" d="M150 0zM942 1331q-174 0 -311.5 -97t-218 -284.5t-80.5 -408.5q0 -187 97.5 -298.5t268.5 -111.5q139 0 322 57v-149q-86 -31 -164 -45t-188 -14q-242 0 -380 149.5t-138 407.5q0 261 105.5 485.5t283.5 342.5t403 118q197 0 348 -80l-69 -141q-138 69 -279 69zM758 -276 q0 -98 -81 -157t-214 -59q-41 0 -86 9v100q38 -6 68 -6q174 0 174 110q0 46 -39 67.5t-99 29.5l101 182h106l-61 -121q131 -38 131 -155z" />
<glyph unicode="&#xc8;" horiz-adv-x="1047" d="M86 0zM821 0h-735l309 1462h735l-32 -153h-566l-98 -469h527l-29 -152h-529l-114 -536h565zM898 1579h-104q-61 61 -128.5 154t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xc9;" horiz-adv-x="1047" d="M86 0zM821 0h-735l309 1462h735l-32 -153h-566l-98 -469h527l-29 -152h-529l-114 -536h565zM657 1606q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xca;" horiz-adv-x="1047" d="M86 0zM821 0h-735l309 1462h735l-32 -153h-566l-98 -469h527l-29 -152h-529l-114 -536h565zM1084 1579h-103q-57 48 -161 189q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#xcb;" horiz-adv-x="1047" d="M86 0zM821 0h-735l309 1462h735l-32 -153h-566l-98 -469h527l-29 -152h-529l-114 -536h565zM523 1716q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM887 1716q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34 q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xcc;" horiz-adv-x="559" d="M86 0zM86 0l311 1462h168l-311 -1462h-168zM593 1579h-104q-61 61 -128.5 154t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xcd;" horiz-adv-x="559" d="M86 0zM86 0l311 1462h168l-311 -1462h-168zM412 1606q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xce;" horiz-adv-x="559" d="M86 0zM86 0l311 1462h168l-311 -1462h-168zM808 1579h-103q-57 48 -161 189q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#xcf;" horiz-adv-x="559" d="M86 0zM86 0l311 1462h168l-311 -1462h-168zM265 1716q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM629 1716q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xd0;" horiz-adv-x="1364" d="M1300 877q0 -261 -105 -461t-300 -308t-457 -108h-352l135 649h-149l32 150h150l141 663h342q276 0 419.5 -149.5t143.5 -435.5zM449 147q202 0 355 91.5t234.5 258.5t81.5 382t-103 325.5t-302 110.5h-178l-111 -516h330l-33 -150h-330l-106 -502h162z" />
<glyph unicode="&#xd1;" horiz-adv-x="1438" d="M84 0zM1139 0h-181l-460 1223h-6q-32 -221 -74 -418l-172 -805h-162l309 1462h180l459 -1220h6q30 224 72 405l174 815h164zM1102 1581q-40 0 -77.5 17.5t-73 39t-69 39t-65.5 17.5q-44 0 -69.5 -28.5t-47.5 -86.5h-100q58 258 231 258q44 0 83.5 -18t75 -39.5 t66.5 -39.5t58 -18q44 0 69.5 27t51.5 90h100q-66 -258 -233 -258z" />
<glyph unicode="&#xd2;" horiz-adv-x="1475" d="M150 0zM1411 907q0 -269 -88 -481.5t-252 -329t-379 -116.5q-256 0 -399 149.5t-143 419.5q0 264 96 482t263.5 336t377.5 118q244 0 384 -154t140 -424zM874 1333q-157 0 -281 -101t-192.5 -281t-68.5 -398q0 -199 98 -310.5t266 -111.5q152 0 272.5 97.5t190.5 279.5 t70 403q0 199 -94 310.5t-261 111.5zM1007 1579h-104q-61 61 -128.5 154t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xd3;" horiz-adv-x="1475" d="M150 0zM1411 907q0 -269 -88 -481.5t-252 -329t-379 -116.5q-256 0 -399 149.5t-143 419.5q0 264 96 482t263.5 336t377.5 118q244 0 384 -154t140 -424zM874 1333q-157 0 -281 -101t-192.5 -281t-68.5 -398q0 -199 98 -310.5t266 -111.5q152 0 272.5 97.5t190.5 279.5 t70 403q0 199 -94 310.5t-261 111.5zM821 1606q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xd4;" horiz-adv-x="1475" d="M150 0zM1411 907q0 -269 -88 -481.5t-252 -329t-379 -116.5q-256 0 -399 149.5t-143 419.5q0 264 96 482t263.5 336t377.5 118q244 0 384 -154t140 -424zM874 1333q-157 0 -281 -101t-192.5 -281t-68.5 -398q0 -199 98 -310.5t266 -111.5q152 0 272.5 97.5t190.5 279.5 t70 403q0 199 -94 310.5t-261 111.5zM1227 1579h-103q-57 48 -161 189q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#xd5;" horiz-adv-x="1475" d="M150 0zM1411 907q0 -269 -88 -481.5t-252 -329t-379 -116.5q-256 0 -399 149.5t-143 419.5q0 264 96 482t263.5 336t377.5 118q244 0 384 -154t140 -424zM874 1333q-157 0 -281 -101t-192.5 -281t-68.5 -398q0 -199 98 -310.5t266 -111.5q152 0 272.5 97.5t190.5 279.5 t70 403q0 199 -94 310.5t-261 111.5zM1067 1581q-40 0 -77.5 17.5t-73 39t-69 39t-65.5 17.5q-44 0 -69.5 -28.5t-47.5 -86.5h-100q58 258 231 258q44 0 83.5 -18t75 -39.5t66.5 -39.5t58 -18q44 0 69.5 27t51.5 90h100q-66 -258 -233 -258z" />
<glyph unicode="&#xd6;" horiz-adv-x="1475" d="M150 0zM1411 907q0 -269 -88 -481.5t-252 -329t-379 -116.5q-256 0 -399 149.5t-143 419.5q0 264 96 482t263.5 336t377.5 118q244 0 384 -154t140 -424zM874 1333q-157 0 -281 -101t-192.5 -281t-68.5 -398q0 -199 98 -310.5t266 -111.5q152 0 272.5 97.5t190.5 279.5 t70 403q0 199 -94 310.5t-261 111.5zM664 1716q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM1028 1716q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xd7;" d="M487 723l-319 321l98 99l320 -320l323 320l99 -96l-324 -324l322 -322l-97 -96l-323 320l-320 -318l-96 96z" />
<glyph unicode="&#xd8;" horiz-adv-x="1475" d="M1411 907q0 -269 -88 -481.5t-252 -329t-379 -116.5q-200 0 -332 96l-129 -160l-112 92l137 170q-106 136 -106 371q0 264 96 482t263.5 336t377.5 118q99 0 178.5 -27t151.5 -84l131 166l114 -92l-149 -184q48 -62 73 -156t25 -201zM874 1333q-157 0 -281 -101 t-192.5 -281t-68.5 -398q0 -135 41 -227l737 919q-90 88 -236 88zM1229 911q0 118 -33 205l-733 -911q91 -74 233 -74q152 0 272.5 97.5t190.5 279.5t70 403z" />
<glyph unicode="&#xd9;" horiz-adv-x="1384" d="M164 0zM1407 1462l-205 -966q-55 -263 -197.5 -389.5t-388.5 -126.5q-230 0 -341 104t-111 299q0 81 24 201l189 878h170l-191 -891q-22 -106 -22 -188q0 -117 73 -184.5t218 -67.5q172 0 267.5 87.5t139.5 289.5l205 954h170zM991 1579h-104q-61 61 -128.5 154 t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xda;" horiz-adv-x="1384" d="M164 0zM1407 1462l-205 -966q-55 -263 -197.5 -389.5t-388.5 -126.5q-230 0 -341 104t-111 299q0 81 24 201l189 878h170l-191 -891q-22 -106 -22 -188q0 -117 73 -184.5t218 -67.5q172 0 267.5 87.5t139.5 289.5l205 954h170zM823 1606q56 60 125.5 151.5t106.5 149.5 h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xdb;" horiz-adv-x="1384" d="M164 0zM1407 1462l-205 -966q-55 -263 -197.5 -389.5t-388.5 -126.5q-230 0 -341 104t-111 299q0 81 24 201l189 878h170l-191 -891q-22 -106 -22 -188q0 -117 73 -184.5t218 -67.5q172 0 267.5 87.5t139.5 289.5l205 954h170zM1217 1579h-103q-57 48 -161 189 q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#xdc;" horiz-adv-x="1384" d="M164 0zM1407 1462l-205 -966q-55 -263 -197.5 -389.5t-388.5 -126.5q-230 0 -341 104t-111 299q0 81 24 201l189 878h170l-191 -891q-22 -106 -22 -188q0 -117 73 -184.5t218 -67.5q172 0 267.5 87.5t139.5 289.5l205 954h170zM643 1716q0 46 28 79.5t74 33.5 q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM1007 1716q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xdd;" horiz-adv-x="1030" d="M188 0zM537 715l489 747h193l-627 -921l-113 -541h-172l119 549l-238 913h170zM616 1606q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xde;" horiz-adv-x="1159" d="M1106 829q0 -248 -164 -379t-483 -131h-133l-70 -319h-170l309 1462h170l-53 -256h160q213 0 323.5 -95t110.5 -282zM354 465h135q215 0 328 91t113 267q0 126 -70 181t-215 55h-166z" />
<glyph unicode="&#xdf;" horiz-adv-x="1182" d="M-133 -492q-69 0 -123 21v143q61 -22 111 -22q65 0 107 47.5t65 157.5l280 1314q43 200 156 299t307 99q162 0 252 -71t90 -196q0 -57 -21 -106.5t-61.5 -95t-178.5 -150.5q-110 -83 -110 -151q0 -56 95 -122q47 -34 101 -87.5t79.5 -110t25.5 -123.5 q0 -175 -108.5 -274.5t-292.5 -99.5q-175 0 -268 71v160q51 -41 118.5 -66.5t129.5 -25.5q113 0 181 58t68 159q0 40 -10.5 71t-33.5 59t-89 83q-88 69 -122.5 124t-34.5 115q0 53 18.5 96t49.5 78.5t124 104.5q80 56 111 87.5t48 65t17 70.5q0 64 -52.5 100.5t-141.5 36.5 q-119 0 -186 -62.5t-95 -190.5l-274 -1303q-40 -189 -121 -276t-211 -87z" />
<glyph unicode="&#xe0;" horiz-adv-x="1157" d="M98 0zM639 1116q92 0 164 -49.5t112 -142.5h11l67 172h127l-233 -1096h-133l26 209h-8q-179 -229 -377 -229q-139 0 -218 99t-79 271q0 208 71 386t196 279t274 101zM449 119q97 0 193 92.5t156 241t60 297.5q0 103 -56 164t-147 61q-104 0 -193.5 -86t-140.5 -233 t-51 -310q0 -114 47 -170.5t132 -56.5zM824 1241h-104q-61 61 -128.5 154t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xe1;" horiz-adv-x="1157" d="M98 0zM639 1116q92 0 164 -49.5t112 -142.5h11l67 172h127l-233 -1096h-133l26 209h-8q-179 -229 -377 -229q-139 0 -218 99t-79 271q0 208 71 386t196 279t274 101zM449 119q97 0 193 92.5t156 241t60 297.5q0 103 -56 164t-147 61q-104 0 -193.5 -86t-140.5 -233 t-51 -310q0 -114 47 -170.5t132 -56.5zM600 1268q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xe2;" horiz-adv-x="1157" d="M98 0zM639 1116q92 0 164 -49.5t112 -142.5h11l67 172h127l-233 -1096h-133l26 209h-8q-179 -229 -377 -229q-139 0 -218 99t-79 271q0 208 71 386t196 279t274 101zM449 119q97 0 193 92.5t156 241t60 297.5q0 103 -56 164t-147 61q-104 0 -193.5 -86t-140.5 -233 t-51 -310q0 -114 47 -170.5t132 -56.5zM1005 1241h-103q-57 48 -161 189q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#xe3;" horiz-adv-x="1157" d="M98 0zM639 1116q92 0 164 -49.5t112 -142.5h11l67 172h127l-233 -1096h-133l26 209h-8q-179 -229 -377 -229q-139 0 -218 99t-79 271q0 208 71 386t196 279t274 101zM449 119q97 0 193 92.5t156 241t60 297.5q0 103 -56 164t-147 61q-104 0 -193.5 -86t-140.5 -233 t-51 -310q0 -114 47 -170.5t132 -56.5zM856 1243q-40 0 -77.5 17.5t-73 39t-69 39t-65.5 17.5q-44 0 -69.5 -28.5t-47.5 -86.5h-100q58 258 231 258q44 0 83.5 -18t75 -39.5t66.5 -39.5t58 -18q44 0 69.5 27t51.5 90h100q-66 -258 -233 -258z" />
<glyph unicode="&#xe4;" horiz-adv-x="1157" d="M98 0zM639 1116q92 0 164 -49.5t112 -142.5h11l67 172h127l-233 -1096h-133l26 209h-8q-179 -229 -377 -229q-139 0 -218 99t-79 271q0 208 71 386t196 279t274 101zM449 119q97 0 193 92.5t156 241t60 297.5q0 103 -56 164t-147 61q-104 0 -193.5 -86t-140.5 -233 t-51 -310q0 -114 47 -170.5t132 -56.5zM454 1378q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM818 1378q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xe5;" horiz-adv-x="1157" d="M98 0zM639 1116q92 0 164 -49.5t112 -142.5h11l67 172h127l-233 -1096h-133l26 209h-8q-179 -229 -377 -229q-139 0 -218 99t-79 271q0 208 71 386t196 279t274 101zM449 119q97 0 193 92.5t156 241t60 297.5q0 103 -56 164t-147 61q-104 0 -193.5 -86t-140.5 -233 t-51 -310q0 -114 47 -170.5t132 -56.5zM951 1456q0 -99 -60 -157t-159 -58q-101 0 -160 57.5t-59 155.5q0 94 62 152.5t157 58.5q101 0 160 -57t59 -152zM847 1454q0 53 -32 84t-83 31q-49 0 -82 -31t-33 -84q0 -54 29.5 -84.5t85.5 -30.5q51 0 83 30.5t32 84.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1669" d="M1165 -20q-108 0 -189.5 39.5t-121.5 119.5l-31 -139h-114l26 209h-8q-109 -132 -191.5 -180.5t-177.5 -48.5q-122 0 -191 99t-69 269q0 206 70.5 385t191.5 281t263 102q82 0 145 -48.5t102 -143.5h11l67 172h109l-31 -146q123 166 332 166q119 0 192.5 -68t73.5 -184 q0 -182 -166.5 -283.5t-472.5 -101.5h-39l-4 -80q0 -131 62.5 -204.5t193.5 -73.5q55 0 116.5 16.5t178.5 67.5v-150q-164 -75 -328 -75zM412 119q95 0 188.5 91.5t153 240.5t59.5 299q0 103 -45.5 164t-122.5 61q-99 0 -187 -86.5t-138 -231.5t-50 -309q0 -114 37 -171.5 t105 -57.5zM1325 973q-117 0 -211 -94.5t-141 -260.5h14q226 0 348.5 58.5t122.5 169.5q0 61 -35 94t-98 33z" />
<glyph unicode="&#xe7;" horiz-adv-x="922" d="M98 0zM506 -20q-194 0 -301 107t-107 302q0 200 74 369t204.5 263.5t293.5 94.5q137 0 268 -51l-47 -141q-120 51 -219 51q-112 0 -204.5 -76.5t-145 -213t-52.5 -296.5q0 -128 66.5 -199t183.5 -71q72 0 136 20t126 47v-143q-124 -63 -276 -63zM592 -276q0 -98 -81 -157 t-214 -59q-41 0 -86 9v100q38 -6 68 -6q174 0 174 110q0 46 -39 67.5t-99 29.5l101 182h106l-61 -121q131 -38 131 -155z" />
<glyph unicode="&#xe8;" horiz-adv-x="1010" d="M98 0zM492 -20q-184 0 -289 109t-105 302q0 188 74.5 360.5t197.5 268.5t271 96q153 0 230 -66.5t77 -185.5q0 -180 -166 -282.5t-475 -102.5h-33l-4 -80q0 -131 61.5 -204.5t190.5 -73.5q63 0 129.5 18t165.5 66v-146q-94 -44 -166 -61.5t-159 -17.5zM631 973 q-103 0 -193.5 -94t-138.5 -261h12q228 0 349.5 59.5t121.5 172.5q0 53 -36.5 88t-114.5 35zM777 1241h-104q-61 61 -128.5 154t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xe9;" horiz-adv-x="1010" d="M98 0zM492 -20q-184 0 -289 109t-105 302q0 188 74.5 360.5t197.5 268.5t271 96q153 0 230 -66.5t77 -185.5q0 -180 -166 -282.5t-475 -102.5h-33l-4 -80q0 -131 61.5 -204.5t190.5 -73.5q63 0 129.5 18t165.5 66v-146q-94 -44 -166 -61.5t-159 -17.5zM631 973 q-103 0 -193.5 -94t-138.5 -261h12q228 0 349.5 59.5t121.5 172.5q0 53 -36.5 88t-114.5 35zM585 1268q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xea;" horiz-adv-x="1010" d="M98 0zM492 -20q-184 0 -289 109t-105 302q0 188 74.5 360.5t197.5 268.5t271 96q153 0 230 -66.5t77 -185.5q0 -180 -166 -282.5t-475 -102.5h-33l-4 -80q0 -131 61.5 -204.5t190.5 -73.5q63 0 129.5 18t165.5 66v-146q-94 -44 -166 -61.5t-159 -17.5zM631 973 q-103 0 -193.5 -94t-138.5 -261h12q228 0 349.5 59.5t121.5 172.5q0 53 -36.5 88t-114.5 35zM966 1241h-103q-57 48 -161 189q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#xeb;" horiz-adv-x="1010" d="M98 0zM492 -20q-184 0 -289 109t-105 302q0 188 74.5 360.5t197.5 268.5t271 96q153 0 230 -66.5t77 -185.5q0 -180 -166 -282.5t-475 -102.5h-33l-4 -80q0 -131 61.5 -204.5t190.5 -73.5q63 0 129.5 18t165.5 66v-146q-94 -44 -166 -61.5t-159 -17.5zM631 973 q-103 0 -193.5 -94t-138.5 -261h12q228 0 349.5 59.5t121.5 172.5q0 53 -36.5 88t-114.5 35zM413 1378q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM777 1378q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34 q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xec;" horiz-adv-x="520" d="M59 0zM227 0h-168l234 1096h168zM492 1241h-104q-61 61 -128.5 154t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xed;" horiz-adv-x="520" d="M59 0zM227 0h-168l234 1096h168zM324 1268q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xee;" horiz-adv-x="520" d="M59 0zM227 0h-168l234 1096h168zM708 1241h-103q-57 48 -161 189q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#xef;" horiz-adv-x="520" d="M59 0zM227 0h-168l234 1096h168zM161 1378q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM525 1378q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xf0;" horiz-adv-x="1165" d="M676 1343q-66 58 -146 113l95 117q118 -84 188 -154l260 146l64 -105l-240 -133q87 -115 126.5 -240.5t39.5 -269.5q0 -253 -71.5 -447t-203 -292t-311.5 -98q-182 0 -284.5 104t-102.5 289q0 160 67.5 298t187 217t267.5 79q105 0 181.5 -45.5t111.5 -124.5l6 2v17 q0 136 -36.5 240t-110.5 197l-270 -149l-56 108zM487 121q107 0 190 56t134 168t51 226q0 118 -65.5 187t-178.5 69q-109 0 -189 -57.5t-123.5 -161t-43.5 -231.5q0 -126 57.5 -191t167.5 -65z" />
<glyph unicode="&#xf1;" horiz-adv-x="1182" d="M59 0zM729 0l148 692q20 104 20 144q0 63 -35.5 101t-113.5 38q-89 0 -173.5 -60t-149 -171t-97.5 -269l-101 -475h-168l234 1096h139l-22 -203h10q96 122 185.5 172.5t185.5 50.5q127 0 200.5 -69.5t73.5 -194.5q0 -79 -23 -180l-143 -672h-170zM871 1243 q-40 0 -77.5 17.5t-73 39t-69 39t-65.5 17.5q-44 0 -69.5 -28.5t-47.5 -86.5h-100q58 258 231 258q44 0 83.5 -18t75 -39.5t66.5 -39.5t58 -18q44 0 69.5 27t51.5 90h100q-66 -258 -233 -258z" />
<glyph unicode="&#xf2;" horiz-adv-x="1149" d="M98 0zM643 1110q190 0 300 -112.5t110 -309.5q0 -188 -72 -355t-195 -258t-278 -91q-192 0 -301 113t-109 309q0 190 73 357.5t197 257t275 89.5zM879 711q0 115 -62.5 186.5t-169.5 71.5q-109 0 -195.5 -74t-134 -205.5t-47.5 -292.5q0 -131 63.5 -202.5t182.5 -71.5 q104 0 187 73t129.5 207.5t46.5 307.5zM798 1241h-104q-61 61 -128.5 154t-95.5 153v21h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xf3;" horiz-adv-x="1149" d="M98 0zM643 1110q190 0 300 -112.5t110 -309.5q0 -188 -72 -355t-195 -258t-278 -91q-192 0 -301 113t-109 309q0 190 73 357.5t197 257t275 89.5zM879 711q0 115 -62.5 186.5t-169.5 71.5q-109 0 -195.5 -74t-134 -205.5t-47.5 -292.5q0 -131 63.5 -202.5t182.5 -71.5 q104 0 187 73t129.5 207.5t46.5 307.5zM589 1268q56 60 125.5 151.5t106.5 149.5h190v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xf4;" horiz-adv-x="1149" d="M98 0zM643 1110q190 0 300 -112.5t110 -309.5q0 -188 -72 -355t-195 -258t-278 -91q-192 0 -301 113t-109 309q0 190 73 357.5t197 257t275 89.5zM879 711q0 115 -62.5 186.5t-169.5 71.5q-109 0 -195.5 -74t-134 -205.5t-47.5 -292.5q0 -131 63.5 -202.5t182.5 -71.5 q104 0 187 73t129.5 207.5t46.5 307.5zM997 1241h-103q-57 48 -161 189q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#xf5;" horiz-adv-x="1149" d="M98 0zM643 1110q190 0 300 -112.5t110 -309.5q0 -188 -72 -355t-195 -258t-278 -91q-192 0 -301 113t-109 309q0 190 73 357.5t197 257t275 89.5zM879 711q0 115 -62.5 186.5t-169.5 71.5q-109 0 -195.5 -74t-134 -205.5t-47.5 -292.5q0 -131 63.5 -202.5t182.5 -71.5 q104 0 187 73t129.5 207.5t46.5 307.5zM844 1243q-40 0 -77.5 17.5t-73 39t-69 39t-65.5 17.5q-44 0 -69.5 -28.5t-47.5 -86.5h-100q58 258 231 258q44 0 83.5 -18t75 -39.5t66.5 -39.5t58 -18q44 0 69.5 27t51.5 90h100q-66 -258 -233 -258z" />
<glyph unicode="&#xf6;" horiz-adv-x="1149" d="M98 0zM643 1110q190 0 300 -112.5t110 -309.5q0 -188 -72 -355t-195 -258t-278 -91q-192 0 -301 113t-109 309q0 190 73 357.5t197 257t275 89.5zM879 711q0 115 -62.5 186.5t-169.5 71.5q-109 0 -195.5 -74t-134 -205.5t-47.5 -292.5q0 -131 63.5 -202.5t182.5 -71.5 q104 0 187 73t129.5 207.5t46.5 307.5zM433 1378q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM797 1378q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xf7;" d="M127 651v142h920v-142h-920zM475 373q0 121 111 121q53 0 82.5 -30.5t29.5 -90.5q0 -58 -30 -89.5t-82 -31.5t-81.5 31t-29.5 90zM475 1071q0 121 111 121q53 0 82.5 -30.5t29.5 -90.5q0 -58 -30 -89.5t-82 -31.5t-81.5 31t-29.5 90z" />
<glyph unicode="&#xf8;" horiz-adv-x="1149" d="M1047 705q0 -189 -69.5 -360t-191.5 -266t-276 -95q-146 0 -246 65l-98 -125l-105 82l109 135q-68 103 -68 265q0 194 73.5 361t195.5 255t272 88q146 0 252 -68l104 129l105 -79l-119 -129q62 -97 62 -258zM647 971q-108 0 -195.5 -73t-137.5 -202t-50 -280 q0 -92 17 -137l518 645q-54 47 -152 47zM885 688q0 84 -13 119l-514 -641q57 -45 158 -45q103 0 188.5 71.5t133 200.5t47.5 295z" />
<glyph unicode="&#xf9;" horiz-adv-x="1182" d="M113 0zM451 1096l-150 -695q-18 -89 -18 -139q0 -143 147 -143q88 0 173 60t150 172t99 270l100 475h166l-231 -1096h-139l22 203h-12q-98 -125 -187 -174t-184 -49q-128 0 -201 69.5t-73 198.5q0 62 22 172l146 676h170zM800 1241h-104q-61 61 -128.5 154t-95.5 153v21 h181q43 -136 147 -303v-25z" />
<glyph unicode="&#xfa;" horiz-adv-x="1182" d="M113 0zM451 1096l-150 -695q-18 -89 -18 -139q0 -143 147 -143q88 0 173 60t150 172t99 270l100 475h166l-231 -1096h-139l22 203h-12q-98 -125 -187 -174t-184 -49q-128 0 -201 69.5t-73 198.5q0 62 22 172l146 676h170zM636 1268q56 60 125.5 151.5t106.5 149.5h190 v-21q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xfb;" horiz-adv-x="1182" d="M113 0zM451 1096l-150 -695q-18 -89 -18 -139q0 -143 147 -143q88 0 173 60t150 172t99 270l100 475h166l-231 -1096h-139l22 203h-12q-98 -125 -187 -174t-184 -49q-128 0 -201 69.5t-73 198.5q0 62 22 172l146 676h170zM1024 1241h-103q-57 48 -161 189 q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#xfc;" horiz-adv-x="1182" d="M113 0zM451 1096l-150 -695q-18 -89 -18 -139q0 -143 147 -143q88 0 173 60t150 172t99 270l100 475h166l-231 -1096h-139l22 203h-12q-98 -125 -187 -174t-184 -49q-128 0 -201 69.5t-73 198.5q0 62 22 172l146 676h170zM457 1378q0 46 28 79.5t74 33.5q78 0 78 -80 q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM821 1378q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#xfd;" horiz-adv-x="946" d="M0 0zM98 1096h168l74 -545q10 -69 19.5 -203.5t9.5 -216.5h6q35 87 87 200t77 156l325 609h178l-696 -1282q-93 -172 -184 -239t-219 -67q-72 0 -140 21v135q63 -18 131 -18q82 0 140.5 50.5t113.5 149.5l76 136zM500 1268q56 60 125.5 151.5t106.5 149.5h190v-21 q-38 -49 -140 -151t-177 -156h-105v27z" />
<glyph unicode="&#xfe;" horiz-adv-x="1182" d="M545 -20q-98 0 -172 51t-113 139h-10q-8 -104 -25 -176l-102 -486h-166l432 2048h168q-95 -441 -115 -522t-39 -149h9q101 125 189 177t183 52q139 0 218 -97.5t79 -273.5q0 -212 -69 -389t-191 -275.5t-276 -98.5zM731 975q-96 0 -195.5 -95t-158 -239t-58.5 -295 q0 -110 55.5 -168.5t160.5 -58.5q99 0 184.5 81t137.5 230.5t52 317.5q0 227 -178 227z" />
<glyph unicode="&#xff;" horiz-adv-x="946" d="M0 0zM98 1096h168l74 -545q10 -69 19.5 -203.5t9.5 -216.5h6q35 87 87 200t77 156l325 609h178l-696 -1282q-93 -172 -184 -239t-219 -67q-72 0 -140 21v135q63 -18 131 -18q82 0 140.5 50.5t113.5 149.5l76 136zM335 1378q0 46 28 79.5t74 33.5q78 0 78 -80 q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM699 1378q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62z" />
<glyph unicode="&#x131;" horiz-adv-x="520" d="M227 0h-168l234 1096h168z" />
<glyph unicode="&#x152;" horiz-adv-x="1751" d="M1524 0h-674q-78 -20 -158 -20q-256 0 -399 149.5t-143 419.5q0 264 96 482t263.5 336t377.5 118q152 0 237 -23h709l-31 -153h-565l-100 -469h528l-31 -150h-528l-115 -538h565zM696 131q69 0 123 19l246 1161q-76 22 -191 22q-157 0 -281 -101t-192.5 -281t-68.5 -398 q0 -199 98 -310.5t266 -111.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1769" d="M1251 -20q-270 0 -356 225q-69 -107 -171.5 -164t-225.5 -57q-184 0 -292 114t-108 308q0 193 75 360t201 255.5t281 88.5q270 0 359 -225q75 109 177.5 170t221.5 61q139 0 217 -65.5t78 -186.5q0 -183 -164.5 -284t-468.5 -101h-41l-4 -80q0 -131 61.5 -204.5 t190.5 -73.5q75 0 145 24.5t150 59.5v-150q-162 -75 -326 -75zM649 969q-109 0 -196 -73t-135 -202t-48 -284q0 -141 62 -214t172 -73q177 0 278 160.5t101 427.5q0 124 -59.5 191t-174.5 67zM1413 973q-125 0 -220.5 -94.5t-139.5 -260.5h18q231 0 351 61t120 177 q0 48 -32 82.5t-97 34.5z" />
<glyph unicode="&#x178;" horiz-adv-x="1030" d="M188 0zM537 715l489 747h193l-627 -921l-113 -541h-172l119 549l-238 913h170zM452 1716q0 46 28 79.5t74 33.5q78 0 78 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22t-23.5 62zM816 1716q0 46 28 79.5t75 33.5q77 0 77 -80q0 -49 -29.5 -83t-68.5 -34q-35 0 -58.5 22 t-23.5 62z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1135" d="M1014 1241h-103q-57 48 -161 189q-134 -119 -242 -189h-109v27q145 133 204.5 197.5t82.5 103.5h158q37 -99 128 -235l42 -66v-27z" />
<glyph unicode="&#x2da;" horiz-adv-x="1182" d="M989 1456q0 -99 -60 -157t-159 -58q-101 0 -160 57.5t-59 155.5q0 94 62 152.5t157 58.5q101 0 160 -57t59 -152zM885 1454q0 53 -32 84t-83 31q-49 0 -82 -31t-33 -84q0 -54 29.5 -84.5t85.5 -30.5q51 0 83 30.5t32 84.5z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1135" d="M838 1243q-40 0 -77.5 17.5t-73 39t-69 39t-65.5 17.5q-44 0 -69.5 -28.5t-47.5 -86.5h-100q58 258 231 258q44 0 83.5 -18t75 -39.5t66.5 -39.5t58 -18q44 0 69.5 27t51.5 90h100q-66 -258 -233 -258z" />
<glyph unicode="&#x2000;" horiz-adv-x="953" />
<glyph unicode="&#x2001;" horiz-adv-x="1907" />
<glyph unicode="&#x2002;" horiz-adv-x="953" />
<glyph unicode="&#x2003;" horiz-adv-x="1907" />
<glyph unicode="&#x2004;" horiz-adv-x="635" />
<glyph unicode="&#x2005;" horiz-adv-x="476" />
<glyph unicode="&#x2006;" horiz-adv-x="317" />
<glyph unicode="&#x2007;" horiz-adv-x="317" />
<glyph unicode="&#x2008;" horiz-adv-x="238" />
<glyph unicode="&#x2009;" horiz-adv-x="381" />
<glyph unicode="&#x200a;" horiz-adv-x="105" />
<glyph unicode="&#x2010;" horiz-adv-x="639" d="M55 469l35 158h479l-34 -158h-480z" />
<glyph unicode="&#x2011;" horiz-adv-x="639" d="M55 469l35 158h479l-34 -158h-480z" />
<glyph unicode="&#x2012;" horiz-adv-x="639" d="M55 469l35 158h479l-34 -158h-480z" />
<glyph unicode="&#x2013;" horiz-adv-x="983" d="M55 469l35 160h823l-34 -160h-824z" />
<glyph unicode="&#x2014;" horiz-adv-x="1966" d="M55 469l35 160h1806l-34 -160h-1807z" />
<glyph unicode="&#x2018;" horiz-adv-x="348" d="M129 961l-6 22q98 211 270 479h127q-147 -345 -203 -501h-188z" />
<glyph unicode="&#x2019;" horiz-adv-x="348" d="M516 1462l8 -22q-40 -91 -111 -218.5t-159 -260.5h-129q134 298 203 501h188z" />
<glyph unicode="&#x201a;" horiz-adv-x="492" d="M291 238l8 -23q-113 -235 -270 -479h-129q126 286 204 502h187z" />
<glyph unicode="&#x201c;" horiz-adv-x="719" d="M500 961l-8 22q80 181 272 479h127q-162 -379 -203 -501h-188zM129 961l-6 22q98 211 270 479h127q-147 -345 -203 -501h-188z" />
<glyph unicode="&#x201d;" horiz-adv-x="719" d="M516 1462l8 -22q-40 -91 -111 -218.5t-159 -260.5h-129q134 298 203 501h188zM885 1462l10 -22q-94 -206 -274 -479h-127q57 126 115.5 272.5t86.5 228.5h189z" />
<glyph unicode="&#x201e;" horiz-adv-x="858" d="M291 238l8 -23q-113 -235 -270 -479h-129q126 286 204 502h187zM659 238l9 -23q-95 -205 -271 -479h-129q140 316 203 502h188z" />
<glyph unicode="&#x2022;" horiz-adv-x="774" d="M199 684q0 145 73.5 231t198.5 86q92 0 139 -49t47 -141q0 -141 -74 -230t-202 -89q-89 0 -135.5 49.5t-46.5 142.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1563" d="M43 0zM563 74q0 77 40.5 122.5t111.5 45.5q43 0 69.5 -26t26.5 -79q0 -71 -40 -118.5t-108 -47.5q-46 0 -73 26t-27 77zM1085 74q0 77 40.5 122.5t111.5 45.5q43 0 69.5 -26t26.5 -79q0 -71 -40 -118.5t-108 -47.5q-46 0 -73 26t-27 77zM43 74q0 77 40.5 122.5 t111.5 45.5q43 0 69.5 -26t26.5 -79q0 -71 -40 -118.5t-108 -47.5q-46 0 -73 26t-27 77z" />
<glyph unicode="&#x202f;" horiz-adv-x="381" />
<glyph unicode="&#x2039;" horiz-adv-x="580" d="M88 578l391 380l78 -81l-297 -334l172 -381l-113 -49l-231 436v29z" />
<glyph unicode="&#x203a;" horiz-adv-x="580" d="M492 496l-392 -381l-77 82l296 333l-172 381l113 50l232 -437v-28z" />
<glyph unicode="&#x2044;" horiz-adv-x="268" d="M752 1462l-1086 -1462h-153l1085 1462h154z" />
<glyph unicode="&#x205f;" horiz-adv-x="476" />
<glyph unicode="&#x2074;" horiz-adv-x="717" d="M713 788h-129l-43 -202h-127l43 202h-365l23 101l481 579h133l-121 -563h127zM481 905q69 322 90 395q-20 -36 -110 -149l-205 -246h225z" />
<glyph unicode="&#x20ac;" d="M928 1329q-140 0 -254 -99t-189 -298h426l-26 -133h-441q-21 -65 -32 -164h381l-29 -131h-361q0 -373 297 -373q123 0 256 55v-147q-127 -59 -278 -59q-212 0 -328.5 133.5t-116.5 378.5v12h-170l27 131h154q8 80 30 164h-151l27 133h159q97 267 259.5 408t369.5 141 q89 0 160 -21.5t141 -70.5l-80 -138q-113 78 -231 78z" />
<glyph unicode="&#x2122;" horiz-adv-x="1534" d="M438 741h-121v617h-196v104h516v-104h-199v-617zM1014 741l-189 551h-6q4 -52 4 -121v-430h-118v721h180l182 -557l193 557h170v-721h-121v430q0 73 4 121h-6l-197 -551h-96z" />
<glyph unicode="&#xe000;" horiz-adv-x="1095" d="M0 1095h1095v-1095h-1095v1095z" />
<glyph horiz-adv-x="1182" d="M0 0z" />
<hkern u1="&#x22;" u2="&#x178;" k="-20" />
<hkern u1="&#x22;" u2="&#x153;" k="123" />
<hkern u1="&#x22;" u2="&#xfc;" k="61" />
<hkern u1="&#x22;" u2="&#xfb;" k="61" />
<hkern u1="&#x22;" u2="&#xfa;" k="61" />
<hkern u1="&#x22;" u2="&#xf9;" k="61" />
<hkern u1="&#x22;" u2="&#xf8;" k="123" />
<hkern u1="&#x22;" u2="&#xf6;" k="123" />
<hkern u1="&#x22;" u2="&#xf5;" k="123" />
<hkern u1="&#x22;" u2="&#xf4;" k="123" />
<hkern u1="&#x22;" u2="&#xf3;" k="123" />
<hkern u1="&#x22;" u2="&#xf2;" k="123" />
<hkern u1="&#x22;" u2="&#xeb;" k="123" />
<hkern u1="&#x22;" u2="&#xea;" k="123" />
<hkern u1="&#x22;" u2="&#xe9;" k="123" />
<hkern u1="&#x22;" u2="&#xe8;" k="123" />
<hkern u1="&#x22;" u2="&#xe7;" k="123" />
<hkern u1="&#x22;" u2="&#xe6;" k="82" />
<hkern u1="&#x22;" u2="&#xe5;" k="82" />
<hkern u1="&#x22;" u2="&#xe4;" k="82" />
<hkern u1="&#x22;" u2="&#xe3;" k="82" />
<hkern u1="&#x22;" u2="&#xe2;" k="82" />
<hkern u1="&#x22;" u2="&#xe1;" k="82" />
<hkern u1="&#x22;" u2="&#xe0;" k="123" />
<hkern u1="&#x22;" u2="&#xdd;" k="-20" />
<hkern u1="&#x22;" u2="&#xc5;" k="143" />
<hkern u1="&#x22;" u2="&#xc4;" k="143" />
<hkern u1="&#x22;" u2="&#xc3;" k="143" />
<hkern u1="&#x22;" u2="&#xc2;" k="143" />
<hkern u1="&#x22;" u2="&#xc1;" k="143" />
<hkern u1="&#x22;" u2="&#xc0;" k="143" />
<hkern u1="&#x22;" u2="u" k="61" />
<hkern u1="&#x22;" u2="s" k="61" />
<hkern u1="&#x22;" u2="r" k="61" />
<hkern u1="&#x22;" u2="q" k="123" />
<hkern u1="&#x22;" u2="p" k="61" />
<hkern u1="&#x22;" u2="o" k="123" />
<hkern u1="&#x22;" u2="n" k="61" />
<hkern u1="&#x22;" u2="m" k="61" />
<hkern u1="&#x22;" u2="g" k="61" />
<hkern u1="&#x22;" u2="e" k="123" />
<hkern u1="&#x22;" u2="d" k="123" />
<hkern u1="&#x22;" u2="c" k="123" />
<hkern u1="&#x22;" u2="a" k="82" />
<hkern u1="&#x22;" u2="Y" k="-20" />
<hkern u1="&#x22;" u2="W" k="-41" />
<hkern u1="&#x22;" u2="V" k="-41" />
<hkern u1="&#x22;" u2="T" k="-41" />
<hkern u1="&#x22;" u2="A" k="143" />
<hkern u1="&#x27;" u2="&#x178;" k="-20" />
<hkern u1="&#x27;" u2="&#x153;" k="123" />
<hkern u1="&#x27;" u2="&#xfc;" k="61" />
<hkern u1="&#x27;" u2="&#xfb;" k="61" />
<hkern u1="&#x27;" u2="&#xfa;" k="61" />
<hkern u1="&#x27;" u2="&#xf9;" k="61" />
<hkern u1="&#x27;" u2="&#xf8;" k="123" />
<hkern u1="&#x27;" u2="&#xf6;" k="123" />
<hkern u1="&#x27;" u2="&#xf5;" k="123" />
<hkern u1="&#x27;" u2="&#xf4;" k="123" />
<hkern u1="&#x27;" u2="&#xf3;" k="123" />
<hkern u1="&#x27;" u2="&#xf2;" k="123" />
<hkern u1="&#x27;" u2="&#xeb;" k="123" />
<hkern u1="&#x27;" u2="&#xea;" k="123" />
<hkern u1="&#x27;" u2="&#xe9;" k="123" />
<hkern u1="&#x27;" u2="&#xe8;" k="123" />
<hkern u1="&#x27;" u2="&#xe7;" k="123" />
<hkern u1="&#x27;" u2="&#xe6;" k="82" />
<hkern u1="&#x27;" u2="&#xe5;" k="82" />
<hkern u1="&#x27;" u2="&#xe4;" k="82" />
<hkern u1="&#x27;" u2="&#xe3;" k="82" />
<hkern u1="&#x27;" u2="&#xe2;" k="82" />
<hkern u1="&#x27;" u2="&#xe1;" k="82" />
<hkern u1="&#x27;" u2="&#xe0;" k="123" />
<hkern u1="&#x27;" u2="&#xdd;" k="-20" />
<hkern u1="&#x27;" u2="&#xc5;" k="143" />
<hkern u1="&#x27;" u2="&#xc4;" k="143" />
<hkern u1="&#x27;" u2="&#xc3;" k="143" />
<hkern u1="&#x27;" u2="&#xc2;" k="143" />
<hkern u1="&#x27;" u2="&#xc1;" k="143" />
<hkern u1="&#x27;" u2="&#xc0;" k="143" />
<hkern u1="&#x27;" u2="u" k="61" />
<hkern u1="&#x27;" u2="s" k="61" />
<hkern u1="&#x27;" u2="r" k="61" />
<hkern u1="&#x27;" u2="q" k="123" />
<hkern u1="&#x27;" u2="p" k="61" />
<hkern u1="&#x27;" u2="o" k="123" />
<hkern u1="&#x27;" u2="n" k="61" />
<hkern u1="&#x27;" u2="m" k="61" />
<hkern u1="&#x27;" u2="g" k="61" />
<hkern u1="&#x27;" u2="e" k="123" />
<hkern u1="&#x27;" u2="d" k="123" />
<hkern u1="&#x27;" u2="c" k="123" />
<hkern u1="&#x27;" u2="a" k="82" />
<hkern u1="&#x27;" u2="Y" k="-20" />
<hkern u1="&#x27;" u2="W" k="-41" />
<hkern u1="&#x27;" u2="V" k="-41" />
<hkern u1="&#x27;" u2="T" k="-41" />
<hkern u1="&#x27;" u2="A" k="143" />
<hkern u1="&#x28;" u2="J" k="-184" />
<hkern u1="&#x2c;" u2="&#x178;" k="123" />
<hkern u1="&#x2c;" u2="&#x152;" k="102" />
<hkern u1="&#x2c;" u2="&#xdd;" k="123" />
<hkern u1="&#x2c;" u2="&#xdc;" k="41" />
<hkern u1="&#x2c;" u2="&#xdb;" k="41" />
<hkern u1="&#x2c;" u2="&#xda;" k="41" />
<hkern u1="&#x2c;" u2="&#xd9;" k="41" />
<hkern u1="&#x2c;" u2="&#xd8;" k="102" />
<hkern u1="&#x2c;" u2="&#xd6;" k="102" />
<hkern u1="&#x2c;" u2="&#xd5;" k="102" />
<hkern u1="&#x2c;" u2="&#xd4;" k="102" />
<hkern u1="&#x2c;" u2="&#xd3;" k="102" />
<hkern u1="&#x2c;" u2="&#xd2;" k="102" />
<hkern u1="&#x2c;" u2="&#xc7;" k="102" />
<hkern u1="&#x2c;" u2="Y" k="123" />
<hkern u1="&#x2c;" u2="W" k="123" />
<hkern u1="&#x2c;" u2="V" k="123" />
<hkern u1="&#x2c;" u2="U" k="41" />
<hkern u1="&#x2c;" u2="T" k="143" />
<hkern u1="&#x2c;" u2="Q" k="102" />
<hkern u1="&#x2c;" u2="O" k="102" />
<hkern u1="&#x2c;" u2="G" k="102" />
<hkern u1="&#x2c;" u2="C" k="102" />
<hkern u1="&#x2d;" u2="T" k="82" />
<hkern u1="&#x2e;" u2="&#x178;" k="123" />
<hkern u1="&#x2e;" u2="&#x152;" k="102" />
<hkern u1="&#x2e;" u2="&#xdd;" k="123" />
<hkern u1="&#x2e;" u2="&#xdc;" k="41" />
<hkern u1="&#x2e;" u2="&#xdb;" k="41" />
<hkern u1="&#x2e;" u2="&#xda;" k="41" />
<hkern u1="&#x2e;" u2="&#xd9;" k="41" />
<hkern u1="&#x2e;" u2="&#xd8;" k="102" />
<hkern u1="&#x2e;" u2="&#xd6;" k="102" />
<hkern u1="&#x2e;" u2="&#xd5;" k="102" />
<hkern u1="&#x2e;" u2="&#xd4;" k="102" />
<hkern u1="&#x2e;" u2="&#xd3;" k="102" />
<hkern u1="&#x2e;" u2="&#xd2;" k="102" />
<hkern u1="&#x2e;" u2="&#xc7;" k="102" />
<hkern u1="&#x2e;" u2="Y" k="123" />
<hkern u1="&#x2e;" u2="W" k="123" />
<hkern u1="&#x2e;" u2="V" k="123" />
<hkern u1="&#x2e;" u2="U" k="41" />
<hkern u1="&#x2e;" u2="T" k="143" />
<hkern u1="&#x2e;" u2="Q" k="102" />
<hkern u1="&#x2e;" u2="O" k="102" />
<hkern u1="&#x2e;" u2="G" k="102" />
<hkern u1="&#x2e;" u2="C" k="102" />
<hkern u1="A" u2="&#x201d;" k="143" />
<hkern u1="A" u2="&#x2019;" k="143" />
<hkern u1="A" u2="&#x178;" k="123" />
<hkern u1="A" u2="&#x152;" k="41" />
<hkern u1="A" u2="&#xdd;" k="123" />
<hkern u1="A" u2="&#xd8;" k="41" />
<hkern u1="A" u2="&#xd6;" k="41" />
<hkern u1="A" u2="&#xd5;" k="41" />
<hkern u1="A" u2="&#xd4;" k="41" />
<hkern u1="A" u2="&#xd3;" k="41" />
<hkern u1="A" u2="&#xd2;" k="41" />
<hkern u1="A" u2="&#xc7;" k="41" />
<hkern u1="A" u2="Y" k="123" />
<hkern u1="A" u2="W" k="82" />
<hkern u1="A" u2="V" k="82" />
<hkern u1="A" u2="T" k="143" />
<hkern u1="A" u2="Q" k="41" />
<hkern u1="A" u2="O" k="41" />
<hkern u1="A" u2="J" k="-266" />
<hkern u1="A" u2="G" k="41" />
<hkern u1="A" u2="C" k="41" />
<hkern u1="A" u2="&#x27;" k="143" />
<hkern u1="A" u2="&#x22;" k="143" />
<hkern u1="B" u2="&#x201e;" k="82" />
<hkern u1="B" u2="&#x201a;" k="82" />
<hkern u1="B" u2="&#x178;" k="20" />
<hkern u1="B" u2="&#xdd;" k="20" />
<hkern u1="B" u2="&#xc5;" k="41" />
<hkern u1="B" u2="&#xc4;" k="41" />
<hkern u1="B" u2="&#xc3;" k="41" />
<hkern u1="B" u2="&#xc2;" k="41" />
<hkern u1="B" u2="&#xc1;" k="41" />
<hkern u1="B" u2="&#xc0;" k="41" />
<hkern u1="B" u2="Z" k="20" />
<hkern u1="B" u2="Y" k="20" />
<hkern u1="B" u2="X" k="41" />
<hkern u1="B" u2="W" k="20" />
<hkern u1="B" u2="V" k="20" />
<hkern u1="B" u2="T" k="61" />
<hkern u1="B" u2="A" k="41" />
<hkern u1="B" u2="&#x2e;" k="82" />
<hkern u1="B" u2="&#x2c;" k="82" />
<hkern u1="C" u2="&#x152;" k="41" />
<hkern u1="C" u2="&#xd8;" k="41" />
<hkern u1="C" u2="&#xd6;" k="41" />
<hkern u1="C" u2="&#xd5;" k="41" />
<hkern u1="C" u2="&#xd4;" k="41" />
<hkern u1="C" u2="&#xd3;" k="41" />
<hkern u1="C" u2="&#xd2;" k="41" />
<hkern u1="C" u2="&#xc7;" k="41" />
<hkern u1="C" u2="Q" k="41" />
<hkern u1="C" u2="O" k="41" />
<hkern u1="C" u2="G" k="41" />
<hkern u1="C" u2="C" k="41" />
<hkern u1="D" u2="&#x201e;" k="82" />
<hkern u1="D" u2="&#x201a;" k="82" />
<hkern u1="D" u2="&#x178;" k="20" />
<hkern u1="D" u2="&#xdd;" k="20" />
<hkern u1="D" u2="&#xc5;" k="41" />
<hkern u1="D" u2="&#xc4;" k="41" />
<hkern u1="D" u2="&#xc3;" k="41" />
<hkern u1="D" u2="&#xc2;" k="41" />
<hkern u1="D" u2="&#xc1;" k="41" />
<hkern u1="D" u2="&#xc0;" k="41" />
<hkern u1="D" u2="Z" k="20" />
<hkern u1="D" u2="Y" k="20" />
<hkern u1="D" u2="X" k="41" />
<hkern u1="D" u2="W" k="20" />
<hkern u1="D" u2="V" k="20" />
<hkern u1="D" u2="T" k="61" />
<hkern u1="D" u2="A" k="41" />
<hkern u1="D" u2="&#x2e;" k="82" />
<hkern u1="D" u2="&#x2c;" k="82" />
<hkern u1="E" u2="J" k="-123" />
<hkern u1="F" u2="&#x201e;" k="123" />
<hkern u1="F" u2="&#x201a;" k="123" />
<hkern u1="F" u2="&#xc5;" k="41" />
<hkern u1="F" u2="&#xc4;" k="41" />
<hkern u1="F" u2="&#xc3;" k="41" />
<hkern u1="F" u2="&#xc2;" k="41" />
<hkern u1="F" u2="&#xc1;" k="41" />
<hkern u1="F" u2="&#xc0;" k="41" />
<hkern u1="F" u2="A" k="41" />
<hkern u1="F" u2="&#x3f;" k="-41" />
<hkern u1="F" u2="&#x2e;" k="123" />
<hkern u1="F" u2="&#x2c;" k="123" />
<hkern u1="K" u2="&#x152;" k="41" />
<hkern u1="K" u2="&#xd8;" k="41" />
<hkern u1="K" u2="&#xd6;" k="41" />
<hkern u1="K" u2="&#xd5;" k="41" />
<hkern u1="K" u2="&#xd4;" k="41" />
<hkern u1="K" u2="&#xd3;" k="41" />
<hkern u1="K" u2="&#xd2;" k="41" />
<hkern u1="K" u2="&#xc7;" k="41" />
<hkern u1="K" u2="Q" k="41" />
<hkern u1="K" u2="O" k="41" />
<hkern u1="K" u2="G" k="41" />
<hkern u1="K" u2="C" k="41" />
<hkern u1="L" u2="&#x201d;" k="164" />
<hkern u1="L" u2="&#x2019;" k="164" />
<hkern u1="L" u2="&#x178;" k="61" />
<hkern u1="L" u2="&#x152;" k="41" />
<hkern u1="L" u2="&#xdd;" k="61" />
<hkern u1="L" u2="&#xdc;" k="20" />
<hkern u1="L" u2="&#xdb;" k="20" />
<hkern u1="L" u2="&#xda;" k="20" />
<hkern u1="L" u2="&#xd9;" k="20" />
<hkern u1="L" u2="&#xd8;" k="41" />
<hkern u1="L" u2="&#xd6;" k="41" />
<hkern u1="L" u2="&#xd5;" k="41" />
<hkern u1="L" u2="&#xd4;" k="41" />
<hkern u1="L" u2="&#xd3;" k="41" />
<hkern u1="L" u2="&#xd2;" k="41" />
<hkern u1="L" u2="&#xc7;" k="41" />
<hkern u1="L" u2="Y" k="61" />
<hkern u1="L" u2="W" k="41" />
<hkern u1="L" u2="V" k="41" />
<hkern u1="L" u2="U" k="20" />
<hkern u1="L" u2="T" k="41" />
<hkern u1="L" u2="Q" k="41" />
<hkern u1="L" u2="O" k="41" />
<hkern u1="L" u2="G" k="41" />
<hkern u1="L" u2="C" k="41" />
<hkern u1="L" u2="&#x27;" k="164" />
<hkern u1="L" u2="&#x22;" k="164" />
<hkern u1="O" u2="&#x201e;" k="82" />
<hkern u1="O" u2="&#x201a;" k="82" />
<hkern u1="O" u2="&#x178;" k="20" />
<hkern u1="O" u2="&#xdd;" k="20" />
<hkern u1="O" u2="&#xc5;" k="41" />
<hkern u1="O" u2="&#xc4;" k="41" />
<hkern u1="O" u2="&#xc3;" k="41" />
<hkern u1="O" u2="&#xc2;" k="41" />
<hkern u1="O" u2="&#xc1;" k="41" />
<hkern u1="O" u2="&#xc0;" k="41" />
<hkern u1="O" u2="Z" k="20" />
<hkern u1="O" u2="Y" k="20" />
<hkern u1="O" u2="X" k="41" />
<hkern u1="O" u2="W" k="20" />
<hkern u1="O" u2="V" k="20" />
<hkern u1="O" u2="T" k="61" />
<hkern u1="O" u2="A" k="41" />
<hkern u1="O" u2="&#x2e;" k="82" />
<hkern u1="O" u2="&#x2c;" k="82" />
<hkern u1="P" u2="&#x201e;" k="266" />
<hkern u1="P" u2="&#x201a;" k="266" />
<hkern u1="P" u2="&#xc5;" k="102" />
<hkern u1="P" u2="&#xc4;" k="102" />
<hkern u1="P" u2="&#xc3;" k="102" />
<hkern u1="P" u2="&#xc2;" k="102" />
<hkern u1="P" u2="&#xc1;" k="102" />
<hkern u1="P" u2="&#xc0;" k="102" />
<hkern u1="P" u2="Z" k="20" />
<hkern u1="P" u2="X" k="41" />
<hkern u1="P" u2="A" k="102" />
<hkern u1="P" u2="&#x2e;" k="266" />
<hkern u1="P" u2="&#x2c;" k="266" />
<hkern u1="Q" u2="&#x201e;" k="82" />
<hkern u1="Q" u2="&#x201a;" k="82" />
<hkern u1="Q" u2="&#x178;" k="20" />
<hkern u1="Q" u2="&#xdd;" k="20" />
<hkern u1="Q" u2="&#xc5;" k="41" />
<hkern u1="Q" u2="&#xc4;" k="41" />
<hkern u1="Q" u2="&#xc3;" k="41" />
<hkern u1="Q" u2="&#xc2;" k="41" />
<hkern u1="Q" u2="&#xc1;" k="41" />
<hkern u1="Q" u2="&#xc0;" k="41" />
<hkern u1="Q" u2="Z" k="20" />
<hkern u1="Q" u2="Y" k="20" />
<hkern u1="Q" u2="X" k="41" />
<hkern u1="Q" u2="W" k="20" />
<hkern u1="Q" u2="V" k="20" />
<hkern u1="Q" u2="T" k="61" />
<hkern u1="Q" u2="A" k="41" />
<hkern u1="Q" u2="&#x2e;" k="82" />
<hkern u1="Q" u2="&#x2c;" k="82" />
<hkern u1="T" u2="&#x201e;" k="123" />
<hkern u1="T" u2="&#x201a;" k="123" />
<hkern u1="T" u2="&#x2014;" k="82" />
<hkern u1="T" u2="&#x2013;" k="82" />
<hkern u1="T" u2="&#x153;" k="143" />
<hkern u1="T" u2="&#x152;" k="41" />
<hkern u1="T" u2="&#xfd;" k="41" />
<hkern u1="T" u2="&#xfc;" k="102" />
<hkern u1="T" u2="&#xfb;" k="102" />
<hkern u1="T" u2="&#xfa;" k="102" />
<hkern u1="T" u2="&#xf9;" k="102" />
<hkern u1="T" u2="&#xf8;" k="143" />
<hkern u1="T" u2="&#xf6;" k="143" />
<hkern u1="T" u2="&#xf5;" k="143" />
<hkern u1="T" u2="&#xf4;" k="143" />
<hkern u1="T" u2="&#xf3;" k="143" />
<hkern u1="T" u2="&#xf2;" k="143" />
<hkern u1="T" u2="&#xeb;" k="143" />
<hkern u1="T" u2="&#xea;" k="143" />
<hkern u1="T" u2="&#xe9;" k="143" />
<hkern u1="T" u2="&#xe8;" k="143" />
<hkern u1="T" u2="&#xe7;" k="143" />
<hkern u1="T" u2="&#xe6;" k="164" />
<hkern u1="T" u2="&#xe5;" k="164" />
<hkern u1="T" u2="&#xe4;" k="164" />
<hkern u1="T" u2="&#xe3;" k="164" />
<hkern u1="T" u2="&#xe2;" k="164" />
<hkern u1="T" u2="&#xe1;" k="164" />
<hkern u1="T" u2="&#xe0;" k="143" />
<hkern u1="T" u2="&#xd8;" k="41" />
<hkern u1="T" u2="&#xd6;" k="41" />
<hkern u1="T" u2="&#xd5;" k="41" />
<hkern u1="T" u2="&#xd4;" k="41" />
<hkern u1="T" u2="&#xd3;" k="41" />
<hkern u1="T" u2="&#xd2;" k="41" />
<hkern u1="T" u2="&#xc7;" k="41" />
<hkern u1="T" u2="&#xc5;" k="143" />
<hkern u1="T" u2="&#xc4;" k="143" />
<hkern u1="T" u2="&#xc3;" k="143" />
<hkern u1="T" u2="&#xc2;" k="143" />
<hkern u1="T" u2="&#xc1;" k="143" />
<hkern u1="T" u2="&#xc0;" k="143" />
<hkern u1="T" u2="z" k="82" />
<hkern u1="T" u2="y" k="41" />
<hkern u1="T" u2="x" k="41" />
<hkern u1="T" u2="w" k="41" />
<hkern u1="T" u2="v" k="41" />
<hkern u1="T" u2="u" k="102" />
<hkern u1="T" u2="s" k="123" />
<hkern u1="T" u2="r" k="102" />
<hkern u1="T" u2="q" k="143" />
<hkern u1="T" u2="p" k="102" />
<hkern u1="T" u2="o" k="143" />
<hkern u1="T" u2="n" k="102" />
<hkern u1="T" u2="m" k="102" />
<hkern u1="T" u2="g" k="143" />
<hkern u1="T" u2="e" k="143" />
<hkern u1="T" u2="d" k="143" />
<hkern u1="T" u2="c" k="143" />
<hkern u1="T" u2="a" k="164" />
<hkern u1="T" u2="T" k="-41" />
<hkern u1="T" u2="Q" k="41" />
<hkern u1="T" u2="O" k="41" />
<hkern u1="T" u2="G" k="41" />
<hkern u1="T" u2="C" k="41" />
<hkern u1="T" u2="A" k="143" />
<hkern u1="T" u2="&#x3f;" k="-41" />
<hkern u1="T" u2="&#x2e;" k="123" />
<hkern u1="T" u2="&#x2d;" k="82" />
<hkern u1="T" u2="&#x2c;" k="123" />
<hkern u1="U" u2="&#x201e;" k="41" />
<hkern u1="U" u2="&#x201a;" k="41" />
<hkern u1="U" u2="&#xc5;" k="20" />
<hkern u1="U" u2="&#xc4;" k="20" />
<hkern u1="U" u2="&#xc3;" k="20" />
<hkern u1="U" u2="&#xc2;" k="20" />
<hkern u1="U" u2="&#xc1;" k="20" />
<hkern u1="U" u2="&#xc0;" k="20" />
<hkern u1="U" u2="A" k="20" />
<hkern u1="U" u2="&#x2e;" k="41" />
<hkern u1="U" u2="&#x2c;" k="41" />
<hkern u1="V" u2="&#x201e;" k="102" />
<hkern u1="V" u2="&#x201a;" k="102" />
<hkern u1="V" u2="&#x153;" k="41" />
<hkern u1="V" u2="&#x152;" k="20" />
<hkern u1="V" u2="&#xfc;" k="20" />
<hkern u1="V" u2="&#xfb;" k="20" />
<hkern u1="V" u2="&#xfa;" k="20" />
<hkern u1="V" u2="&#xf9;" k="20" />
<hkern u1="V" u2="&#xf8;" k="41" />
<hkern u1="V" u2="&#xf6;" k="41" />
<hkern u1="V" u2="&#xf5;" k="41" />
<hkern u1="V" u2="&#xf4;" k="41" />
<hkern u1="V" u2="&#xf3;" k="41" />
<hkern u1="V" u2="&#xf2;" k="41" />
<hkern u1="V" u2="&#xeb;" k="41" />
<hkern u1="V" u2="&#xea;" k="41" />
<hkern u1="V" u2="&#xe9;" k="41" />
<hkern u1="V" u2="&#xe8;" k="41" />
<hkern u1="V" u2="&#xe7;" k="41" />
<hkern u1="V" u2="&#xe6;" k="41" />
<hkern u1="V" u2="&#xe5;" k="41" />
<hkern u1="V" u2="&#xe4;" k="41" />
<hkern u1="V" u2="&#xe3;" k="41" />
<hkern u1="V" u2="&#xe2;" k="41" />
<hkern u1="V" u2="&#xe1;" k="41" />
<hkern u1="V" u2="&#xe0;" k="41" />
<hkern u1="V" u2="&#xd8;" k="20" />
<hkern u1="V" u2="&#xd6;" k="20" />
<hkern u1="V" u2="&#xd5;" k="20" />
<hkern u1="V" u2="&#xd4;" k="20" />
<hkern u1="V" u2="&#xd3;" k="20" />
<hkern u1="V" u2="&#xd2;" k="20" />
<hkern u1="V" u2="&#xc7;" k="20" />
<hkern u1="V" u2="&#xc5;" k="82" />
<hkern u1="V" u2="&#xc4;" k="82" />
<hkern u1="V" u2="&#xc3;" k="82" />
<hkern u1="V" u2="&#xc2;" k="82" />
<hkern u1="V" u2="&#xc1;" k="82" />
<hkern u1="V" u2="&#xc0;" k="82" />
<hkern u1="V" u2="u" k="20" />
<hkern u1="V" u2="s" k="20" />
<hkern u1="V" u2="r" k="20" />
<hkern u1="V" u2="q" k="41" />
<hkern u1="V" u2="p" k="20" />
<hkern u1="V" u2="o" k="41" />
<hkern u1="V" u2="n" k="20" />
<hkern u1="V" u2="m" k="20" />
<hkern u1="V" u2="g" k="20" />
<hkern u1="V" u2="e" k="41" />
<hkern u1="V" u2="d" k="41" />
<hkern u1="V" u2="c" k="41" />
<hkern u1="V" u2="a" k="41" />
<hkern u1="V" u2="Q" k="20" />
<hkern u1="V" u2="O" k="20" />
<hkern u1="V" u2="G" k="20" />
<hkern u1="V" u2="C" k="20" />
<hkern u1="V" u2="A" k="82" />
<hkern u1="V" u2="&#x3f;" k="-41" />
<hkern u1="V" u2="&#x2e;" k="102" />
<hkern u1="V" u2="&#x2c;" k="102" />
<hkern u1="W" u2="&#x201e;" k="102" />
<hkern u1="W" u2="&#x201a;" k="102" />
<hkern u1="W" u2="&#x153;" k="41" />
<hkern u1="W" u2="&#x152;" k="20" />
<hkern u1="W" u2="&#xfc;" k="20" />
<hkern u1="W" u2="&#xfb;" k="20" />
<hkern u1="W" u2="&#xfa;" k="20" />
<hkern u1="W" u2="&#xf9;" k="20" />
<hkern u1="W" u2="&#xf8;" k="41" />
<hkern u1="W" u2="&#xf6;" k="41" />
<hkern u1="W" u2="&#xf5;" k="41" />
<hkern u1="W" u2="&#xf4;" k="41" />
<hkern u1="W" u2="&#xf3;" k="41" />
<hkern u1="W" u2="&#xf2;" k="41" />
<hkern u1="W" u2="&#xeb;" k="41" />
<hkern u1="W" u2="&#xea;" k="41" />
<hkern u1="W" u2="&#xe9;" k="41" />
<hkern u1="W" u2="&#xe8;" k="41" />
<hkern u1="W" u2="&#xe7;" k="41" />
<hkern u1="W" u2="&#xe6;" k="41" />
<hkern u1="W" u2="&#xe5;" k="41" />
<hkern u1="W" u2="&#xe4;" k="41" />
<hkern u1="W" u2="&#xe3;" k="41" />
<hkern u1="W" u2="&#xe2;" k="41" />
<hkern u1="W" u2="&#xe1;" k="41" />
<hkern u1="W" u2="&#xe0;" k="41" />
<hkern u1="W" u2="&#xd8;" k="20" />
<hkern u1="W" u2="&#xd6;" k="20" />
<hkern u1="W" u2="&#xd5;" k="20" />
<hkern u1="W" u2="&#xd4;" k="20" />
<hkern u1="W" u2="&#xd3;" k="20" />
<hkern u1="W" u2="&#xd2;" k="20" />
<hkern u1="W" u2="&#xc7;" k="20" />
<hkern u1="W" u2="&#xc5;" k="82" />
<hkern u1="W" u2="&#xc4;" k="82" />
<hkern u1="W" u2="&#xc3;" k="82" />
<hkern u1="W" u2="&#xc2;" k="82" />
<hkern u1="W" u2="&#xc1;" k="82" />
<hkern u1="W" u2="&#xc0;" k="82" />
<hkern u1="W" u2="u" k="20" />
<hkern u1="W" u2="s" k="20" />
<hkern u1="W" u2="r" k="20" />
<hkern u1="W" u2="q" k="41" />
<hkern u1="W" u2="p" k="20" />
<hkern u1="W" u2="o" k="41" />
<hkern u1="W" u2="n" k="20" />
<hkern u1="W" u2="m" k="20" />
<hkern u1="W" u2="g" k="20" />
<hkern u1="W" u2="e" k="41" />
<hkern u1="W" u2="d" k="41" />
<hkern u1="W" u2="c" k="41" />
<hkern u1="W" u2="a" k="41" />
<hkern u1="W" u2="Q" k="20" />
<hkern u1="W" u2="O" k="20" />
<hkern u1="W" u2="G" k="20" />
<hkern u1="W" u2="C" k="20" />
<hkern u1="W" u2="A" k="82" />
<hkern u1="W" u2="&#x3f;" k="-41" />
<hkern u1="W" u2="&#x2e;" k="102" />
<hkern u1="W" u2="&#x2c;" k="102" />
<hkern u1="X" u2="&#x152;" k="41" />
<hkern u1="X" u2="&#xd8;" k="41" />
<hkern u1="X" u2="&#xd6;" k="41" />
<hkern u1="X" u2="&#xd5;" k="41" />
<hkern u1="X" u2="&#xd4;" k="41" />
<hkern u1="X" u2="&#xd3;" k="41" />
<hkern u1="X" u2="&#xd2;" k="41" />
<hkern u1="X" u2="&#xc7;" k="41" />
<hkern u1="X" u2="Q" k="41" />
<hkern u1="X" u2="O" k="41" />
<hkern u1="X" u2="G" k="41" />
<hkern u1="X" u2="C" k="41" />
<hkern u1="Y" u2="&#x201e;" k="123" />
<hkern u1="Y" u2="&#x201a;" k="123" />
<hkern u1="Y" u2="&#x153;" k="102" />
<hkern u1="Y" u2="&#x152;" k="41" />
<hkern u1="Y" u2="&#xfc;" k="61" />
<hkern u1="Y" u2="&#xfb;" k="61" />
<hkern u1="Y" u2="&#xfa;" k="61" />
<hkern u1="Y" u2="&#xf9;" k="61" />
<hkern u1="Y" u2="&#xf8;" k="102" />
<hkern u1="Y" u2="&#xf6;" k="102" />
<hkern u1="Y" u2="&#xf5;" k="102" />
<hkern u1="Y" u2="&#xf4;" k="102" />
<hkern u1="Y" u2="&#xf3;" k="102" />
<hkern u1="Y" u2="&#xf2;" k="102" />
<hkern u1="Y" u2="&#xeb;" k="102" />
<hkern u1="Y" u2="&#xea;" k="102" />
<hkern u1="Y" u2="&#xe9;" k="102" />
<hkern u1="Y" u2="&#xe8;" k="102" />
<hkern u1="Y" u2="&#xe7;" k="102" />
<hkern u1="Y" u2="&#xe6;" k="102" />
<hkern u1="Y" u2="&#xe5;" k="102" />
<hkern u1="Y" u2="&#xe4;" k="102" />
<hkern u1="Y" u2="&#xe3;" k="102" />
<hkern u1="Y" u2="&#xe2;" k="102" />
<hkern u1="Y" u2="&#xe1;" k="102" />
<hkern u1="Y" u2="&#xe0;" k="102" />
<hkern u1="Y" u2="&#xd8;" k="41" />
<hkern u1="Y" u2="&#xd6;" k="41" />
<hkern u1="Y" u2="&#xd5;" k="41" />
<hkern u1="Y" u2="&#xd4;" k="41" />
<hkern u1="Y" u2="&#xd3;" k="41" />
<hkern u1="Y" u2="&#xd2;" k="41" />
<hkern u1="Y" u2="&#xc7;" k="41" />
<hkern u1="Y" u2="&#xc5;" k="123" />
<hkern u1="Y" u2="&#xc4;" k="123" />
<hkern u1="Y" u2="&#xc3;" k="123" />
<hkern u1="Y" u2="&#xc2;" k="123" />
<hkern u1="Y" u2="&#xc1;" k="123" />
<hkern u1="Y" u2="&#xc0;" k="123" />
<hkern u1="Y" u2="z" k="41" />
<hkern u1="Y" u2="u" k="61" />
<hkern u1="Y" u2="s" k="82" />
<hkern u1="Y" u2="r" k="61" />
<hkern u1="Y" u2="q" k="102" />
<hkern u1="Y" u2="p" k="61" />
<hkern u1="Y" u2="o" k="102" />
<hkern u1="Y" u2="n" k="61" />
<hkern u1="Y" u2="m" k="61" />
<hkern u1="Y" u2="g" k="41" />
<hkern u1="Y" u2="e" k="102" />
<hkern u1="Y" u2="d" k="102" />
<hkern u1="Y" u2="c" k="102" />
<hkern u1="Y" u2="a" k="102" />
<hkern u1="Y" u2="Q" k="41" />
<hkern u1="Y" u2="O" k="41" />
<hkern u1="Y" u2="G" k="41" />
<hkern u1="Y" u2="C" k="41" />
<hkern u1="Y" u2="A" k="123" />
<hkern u1="Y" u2="&#x3f;" k="-41" />
<hkern u1="Y" u2="&#x2e;" k="123" />
<hkern u1="Y" u2="&#x2c;" k="123" />
<hkern u1="Z" u2="&#x152;" k="20" />
<hkern u1="Z" u2="&#xd8;" k="20" />
<hkern u1="Z" u2="&#xd6;" k="20" />
<hkern u1="Z" u2="&#xd5;" k="20" />
<hkern u1="Z" u2="&#xd4;" k="20" />
<hkern u1="Z" u2="&#xd3;" k="20" />
<hkern u1="Z" u2="&#xd2;" k="20" />
<hkern u1="Z" u2="&#xc7;" k="20" />
<hkern u1="Z" u2="Q" k="20" />
<hkern u1="Z" u2="O" k="20" />
<hkern u1="Z" u2="G" k="20" />
<hkern u1="Z" u2="C" k="20" />
<hkern u1="[" u2="J" k="-184" />
<hkern u1="a" u2="&#x201d;" k="20" />
<hkern u1="a" u2="&#x2019;" k="20" />
<hkern u1="a" u2="&#x27;" k="20" />
<hkern u1="a" u2="&#x22;" k="20" />
<hkern u1="b" u2="&#x201d;" k="20" />
<hkern u1="b" u2="&#x2019;" k="20" />
<hkern u1="b" u2="&#xfd;" k="41" />
<hkern u1="b" u2="z" k="20" />
<hkern u1="b" u2="y" k="41" />
<hkern u1="b" u2="x" k="41" />
<hkern u1="b" u2="w" k="41" />
<hkern u1="b" u2="v" k="41" />
<hkern u1="b" u2="&#x27;" k="20" />
<hkern u1="b" u2="&#x22;" k="20" />
<hkern u1="c" u2="&#x201d;" k="-41" />
<hkern u1="c" u2="&#x2019;" k="-41" />
<hkern u1="c" u2="&#x27;" k="-41" />
<hkern u1="c" u2="&#x22;" k="-41" />
<hkern u1="e" u2="&#x201d;" k="20" />
<hkern u1="e" u2="&#x2019;" k="20" />
<hkern u1="e" u2="&#xfd;" k="41" />
<hkern u1="e" u2="z" k="20" />
<hkern u1="e" u2="y" k="41" />
<hkern u1="e" u2="x" k="41" />
<hkern u1="e" u2="w" k="41" />
<hkern u1="e" u2="v" k="41" />
<hkern u1="e" u2="&#x27;" k="20" />
<hkern u1="e" u2="&#x22;" k="20" />
<hkern u1="f" u2="&#x201d;" k="-123" />
<hkern u1="f" u2="&#x2019;" k="-123" />
<hkern u1="f" u2="&#x27;" k="-123" />
<hkern u1="f" u2="&#x22;" k="-123" />
<hkern u1="h" u2="&#x201d;" k="20" />
<hkern u1="h" u2="&#x2019;" k="20" />
<hkern u1="h" u2="&#x27;" k="20" />
<hkern u1="h" u2="&#x22;" k="20" />
<hkern u1="k" u2="&#x153;" k="41" />
<hkern u1="k" u2="&#xf8;" k="41" />
<hkern u1="k" u2="&#xf6;" k="41" />
<hkern u1="k" u2="&#xf5;" k="41" />
<hkern u1="k" u2="&#xf4;" k="41" />
<hkern u1="k" u2="&#xf3;" k="41" />
<hkern u1="k" u2="&#xf2;" k="41" />
<hkern u1="k" u2="&#xeb;" k="41" />
<hkern u1="k" u2="&#xea;" k="41" />
<hkern u1="k" u2="&#xe9;" k="41" />
<hkern u1="k" u2="&#xe8;" k="41" />
<hkern u1="k" u2="&#xe7;" k="41" />
<hkern u1="k" u2="&#xe0;" k="41" />
<hkern u1="k" u2="q" k="41" />
<hkern u1="k" u2="o" k="41" />
<hkern u1="k" u2="e" k="41" />
<hkern u1="k" u2="d" k="41" />
<hkern u1="k" u2="c" k="41" />
<hkern u1="m" u2="&#x201d;" k="20" />
<hkern u1="m" u2="&#x2019;" k="20" />
<hkern u1="m" u2="&#x27;" k="20" />
<hkern u1="m" u2="&#x22;" k="20" />
<hkern u1="n" u2="&#x201d;" k="20" />
<hkern u1="n" u2="&#x2019;" k="20" />
<hkern u1="n" u2="&#x27;" k="20" />
<hkern u1="n" u2="&#x22;" k="20" />
<hkern u1="o" u2="&#x201d;" k="20" />
<hkern u1="o" u2="&#x2019;" k="20" />
<hkern u1="o" u2="&#xfd;" k="41" />
<hkern u1="o" u2="z" k="20" />
<hkern u1="o" u2="y" k="41" />
<hkern u1="o" u2="x" k="41" />
<hkern u1="o" u2="w" k="41" />
<hkern u1="o" u2="v" k="41" />
<hkern u1="o" u2="&#x27;" k="20" />
<hkern u1="o" u2="&#x22;" k="20" />
<hkern u1="p" u2="&#x201d;" k="20" />
<hkern u1="p" u2="&#x2019;" k="20" />
<hkern u1="p" u2="&#xfd;" k="41" />
<hkern u1="p" u2="z" k="20" />
<hkern u1="p" u2="y" k="41" />
<hkern u1="p" u2="x" k="41" />
<hkern u1="p" u2="w" k="41" />
<hkern u1="p" u2="v" k="41" />
<hkern u1="p" u2="&#x27;" k="20" />
<hkern u1="p" u2="&#x22;" k="20" />
<hkern u1="r" u2="&#x201d;" k="-82" />
<hkern u1="r" u2="&#x2019;" k="-82" />
<hkern u1="r" u2="&#x153;" k="41" />
<hkern u1="r" u2="&#xf8;" k="41" />
<hkern u1="r" u2="&#xf6;" k="41" />
<hkern u1="r" u2="&#xf5;" k="41" />
<hkern u1="r" u2="&#xf4;" k="41" />
<hkern u1="r" u2="&#xf3;" k="41" />
<hkern u1="r" u2="&#xf2;" k="41" />
<hkern u1="r" u2="&#xeb;" k="41" />
<hkern u1="r" u2="&#xea;" k="41" />
<hkern u1="r" u2="&#xe9;" k="41" />
<hkern u1="r" u2="&#xe8;" k="41" />
<hkern u1="r" u2="&#xe7;" k="41" />
<hkern u1="r" u2="&#xe6;" k="41" />
<hkern u1="r" u2="&#xe5;" k="41" />
<hkern u1="r" u2="&#xe4;" k="41" />
<hkern u1="r" u2="&#xe3;" k="41" />
<hkern u1="r" u2="&#xe2;" k="41" />
<hkern u1="r" u2="&#xe1;" k="41" />
<hkern u1="r" u2="&#xe0;" k="41" />
<hkern u1="r" u2="q" k="41" />
<hkern u1="r" u2="o" k="41" />
<hkern u1="r" u2="g" k="20" />
<hkern u1="r" u2="e" k="41" />
<hkern u1="r" u2="d" k="41" />
<hkern u1="r" u2="c" k="41" />
<hkern u1="r" u2="a" k="41" />
<hkern u1="r" u2="&#x27;" k="-82" />
<hkern u1="r" u2="&#x22;" k="-82" />
<hkern u1="t" u2="&#x201d;" k="-41" />
<hkern u1="t" u2="&#x2019;" k="-41" />
<hkern u1="t" u2="&#x27;" k="-41" />
<hkern u1="t" u2="&#x22;" k="-41" />
<hkern u1="v" u2="&#x201e;" k="82" />
<hkern u1="v" u2="&#x201d;" k="-82" />
<hkern u1="v" u2="&#x201a;" k="82" />
<hkern u1="v" u2="&#x2019;" k="-82" />
<hkern u1="v" u2="&#x3f;" k="-41" />
<hkern u1="v" u2="&#x2e;" k="82" />
<hkern u1="v" u2="&#x2c;" k="82" />
<hkern u1="v" u2="&#x27;" k="-82" />
<hkern u1="v" u2="&#x22;" k="-82" />
<hkern u1="w" u2="&#x201e;" k="82" />
<hkern u1="w" u2="&#x201d;" k="-82" />
<hkern u1="w" u2="&#x201a;" k="82" />
<hkern u1="w" u2="&#x2019;" k="-82" />
<hkern u1="w" u2="&#x3f;" k="-41" />
<hkern u1="w" u2="&#x2e;" k="82" />
<hkern u1="w" u2="&#x2c;" k="82" />
<hkern u1="w" u2="&#x27;" k="-82" />
<hkern u1="w" u2="&#x22;" k="-82" />
<hkern u1="x" u2="&#x153;" k="41" />
<hkern u1="x" u2="&#xf8;" k="41" />
<hkern u1="x" u2="&#xf6;" k="41" />
<hkern u1="x" u2="&#xf5;" k="41" />
<hkern u1="x" u2="&#xf4;" k="41" />
<hkern u1="x" u2="&#xf3;" k="41" />
<hkern u1="x" u2="&#xf2;" k="41" />
<hkern u1="x" u2="&#xeb;" k="41" />
<hkern u1="x" u2="&#xea;" k="41" />
<hkern u1="x" u2="&#xe9;" k="41" />
<hkern u1="x" u2="&#xe8;" k="41" />
<hkern u1="x" u2="&#xe7;" k="41" />
<hkern u1="x" u2="&#xe0;" k="41" />
<hkern u1="x" u2="q" k="41" />
<hkern u1="x" u2="o" k="41" />
<hkern u1="x" u2="e" k="41" />
<hkern u1="x" u2="d" k="41" />
<hkern u1="x" u2="c" k="41" />
<hkern u1="y" u2="&#x201e;" k="82" />
<hkern u1="y" u2="&#x201d;" k="-82" />
<hkern u1="y" u2="&#x201a;" k="82" />
<hkern u1="y" u2="&#x2019;" k="-82" />
<hkern u1="y" u2="&#x3f;" k="-41" />
<hkern u1="y" u2="&#x2e;" k="82" />
<hkern u1="y" u2="&#x2c;" k="82" />
<hkern u1="y" u2="&#x27;" k="-82" />
<hkern u1="y" u2="&#x22;" k="-82" />
<hkern u1="&#x7b;" u2="J" k="-184" />
<hkern u1="&#xc0;" u2="&#x201d;" k="143" />
<hkern u1="&#xc0;" u2="&#x2019;" k="143" />
<hkern u1="&#xc0;" u2="&#x178;" k="123" />
<hkern u1="&#xc0;" u2="&#x152;" k="41" />
<hkern u1="&#xc0;" u2="&#xdd;" k="123" />
<hkern u1="&#xc0;" u2="&#xd8;" k="41" />
<hkern u1="&#xc0;" u2="&#xd6;" k="41" />
<hkern u1="&#xc0;" u2="&#xd5;" k="41" />
<hkern u1="&#xc0;" u2="&#xd4;" k="41" />
<hkern u1="&#xc0;" u2="&#xd3;" k="41" />
<hkern u1="&#xc0;" u2="&#xd2;" k="41" />
<hkern u1="&#xc0;" u2="&#xc7;" k="41" />
<hkern u1="&#xc0;" u2="Y" k="123" />
<hkern u1="&#xc0;" u2="W" k="82" />
<hkern u1="&#xc0;" u2="V" k="82" />
<hkern u1="&#xc0;" u2="T" k="143" />
<hkern u1="&#xc0;" u2="Q" k="41" />
<hkern u1="&#xc0;" u2="O" k="41" />
<hkern u1="&#xc0;" u2="J" k="-266" />
<hkern u1="&#xc0;" u2="G" k="41" />
<hkern u1="&#xc0;" u2="C" k="41" />
<hkern u1="&#xc0;" u2="&#x27;" k="143" />
<hkern u1="&#xc0;" u2="&#x22;" k="143" />
<hkern u1="&#xc1;" u2="&#x201d;" k="143" />
<hkern u1="&#xc1;" u2="&#x2019;" k="143" />
<hkern u1="&#xc1;" u2="&#x178;" k="123" />
<hkern u1="&#xc1;" u2="&#x152;" k="41" />
<hkern u1="&#xc1;" u2="&#xdd;" k="123" />
<hkern u1="&#xc1;" u2="&#xd8;" k="41" />
<hkern u1="&#xc1;" u2="&#xd6;" k="41" />
<hkern u1="&#xc1;" u2="&#xd5;" k="41" />
<hkern u1="&#xc1;" u2="&#xd4;" k="41" />
<hkern u1="&#xc1;" u2="&#xd3;" k="41" />
<hkern u1="&#xc1;" u2="&#xd2;" k="41" />
<hkern u1="&#xc1;" u2="&#xc7;" k="41" />
<hkern u1="&#xc1;" u2="Y" k="123" />
<hkern u1="&#xc1;" u2="W" k="82" />
<hkern u1="&#xc1;" u2="V" k="82" />
<hkern u1="&#xc1;" u2="T" k="143" />
<hkern u1="&#xc1;" u2="Q" k="41" />
<hkern u1="&#xc1;" u2="O" k="41" />
<hkern u1="&#xc1;" u2="J" k="-266" />
<hkern u1="&#xc1;" u2="G" k="41" />
<hkern u1="&#xc1;" u2="C" k="41" />
<hkern u1="&#xc1;" u2="&#x27;" k="143" />
<hkern u1="&#xc1;" u2="&#x22;" k="143" />
<hkern u1="&#xc2;" u2="&#x201d;" k="143" />
<hkern u1="&#xc2;" u2="&#x2019;" k="143" />
<hkern u1="&#xc2;" u2="&#x178;" k="123" />
<hkern u1="&#xc2;" u2="&#x152;" k="41" />
<hkern u1="&#xc2;" u2="&#xdd;" k="123" />
<hkern u1="&#xc2;" u2="&#xd8;" k="41" />
<hkern u1="&#xc2;" u2="&#xd6;" k="41" />
<hkern u1="&#xc2;" u2="&#xd5;" k="41" />
<hkern u1="&#xc2;" u2="&#xd4;" k="41" />
<hkern u1="&#xc2;" u2="&#xd3;" k="41" />
<hkern u1="&#xc2;" u2="&#xd2;" k="41" />
<hkern u1="&#xc2;" u2="&#xc7;" k="41" />
<hkern u1="&#xc2;" u2="Y" k="123" />
<hkern u1="&#xc2;" u2="W" k="82" />
<hkern u1="&#xc2;" u2="V" k="82" />
<hkern u1="&#xc2;" u2="T" k="143" />
<hkern u1="&#xc2;" u2="Q" k="41" />
<hkern u1="&#xc2;" u2="O" k="41" />
<hkern u1="&#xc2;" u2="J" k="-266" />
<hkern u1="&#xc2;" u2="G" k="41" />
<hkern u1="&#xc2;" u2="C" k="41" />
<hkern u1="&#xc2;" u2="&#x27;" k="143" />
<hkern u1="&#xc2;" u2="&#x22;" k="143" />
<hkern u1="&#xc3;" u2="&#x201d;" k="143" />
<hkern u1="&#xc3;" u2="&#x2019;" k="143" />
<hkern u1="&#xc3;" u2="&#x178;" k="123" />
<hkern u1="&#xc3;" u2="&#x152;" k="41" />
<hkern u1="&#xc3;" u2="&#xdd;" k="123" />
<hkern u1="&#xc3;" u2="&#xd8;" k="41" />
<hkern u1="&#xc3;" u2="&#xd6;" k="41" />
<hkern u1="&#xc3;" u2="&#xd5;" k="41" />
<hkern u1="&#xc3;" u2="&#xd4;" k="41" />
<hkern u1="&#xc3;" u2="&#xd3;" k="41" />
<hkern u1="&#xc3;" u2="&#xd2;" k="41" />
<hkern u1="&#xc3;" u2="&#xc7;" k="41" />
<hkern u1="&#xc3;" u2="Y" k="123" />
<hkern u1="&#xc3;" u2="W" k="82" />
<hkern u1="&#xc3;" u2="V" k="82" />
<hkern u1="&#xc3;" u2="T" k="143" />
<hkern u1="&#xc3;" u2="Q" k="41" />
<hkern u1="&#xc3;" u2="O" k="41" />
<hkern u1="&#xc3;" u2="J" k="-266" />
<hkern u1="&#xc3;" u2="G" k="41" />
<hkern u1="&#xc3;" u2="C" k="41" />
<hkern u1="&#xc3;" u2="&#x27;" k="143" />
<hkern u1="&#xc3;" u2="&#x22;" k="143" />
<hkern u1="&#xc4;" u2="&#x201d;" k="143" />
<hkern u1="&#xc4;" u2="&#x2019;" k="143" />
<hkern u1="&#xc4;" u2="&#x178;" k="123" />
<hkern u1="&#xc4;" u2="&#x152;" k="41" />
<hkern u1="&#xc4;" u2="&#xdd;" k="123" />
<hkern u1="&#xc4;" u2="&#xd8;" k="41" />
<hkern u1="&#xc4;" u2="&#xd6;" k="41" />
<hkern u1="&#xc4;" u2="&#xd5;" k="41" />
<hkern u1="&#xc4;" u2="&#xd4;" k="41" />
<hkern u1="&#xc4;" u2="&#xd3;" k="41" />
<hkern u1="&#xc4;" u2="&#xd2;" k="41" />
<hkern u1="&#xc4;" u2="&#xc7;" k="41" />
<hkern u1="&#xc4;" u2="Y" k="123" />
<hkern u1="&#xc4;" u2="W" k="82" />
<hkern u1="&#xc4;" u2="V" k="82" />
<hkern u1="&#xc4;" u2="T" k="143" />
<hkern u1="&#xc4;" u2="Q" k="41" />
<hkern u1="&#xc4;" u2="O" k="41" />
<hkern u1="&#xc4;" u2="J" k="-266" />
<hkern u1="&#xc4;" u2="G" k="41" />
<hkern u1="&#xc4;" u2="C" k="41" />
<hkern u1="&#xc4;" u2="&#x27;" k="143" />
<hkern u1="&#xc4;" u2="&#x22;" k="143" />
<hkern u1="&#xc5;" u2="&#x201d;" k="143" />
<hkern u1="&#xc5;" u2="&#x2019;" k="143" />
<hkern u1="&#xc5;" u2="&#x178;" k="123" />
<hkern u1="&#xc5;" u2="&#x152;" k="41" />
<hkern u1="&#xc5;" u2="&#xdd;" k="123" />
<hkern u1="&#xc5;" u2="&#xd8;" k="41" />
<hkern u1="&#xc5;" u2="&#xd6;" k="41" />
<hkern u1="&#xc5;" u2="&#xd5;" k="41" />
<hkern u1="&#xc5;" u2="&#xd4;" k="41" />
<hkern u1="&#xc5;" u2="&#xd3;" k="41" />
<hkern u1="&#xc5;" u2="&#xd2;" k="41" />
<hkern u1="&#xc5;" u2="&#xc7;" k="41" />
<hkern u1="&#xc5;" u2="Y" k="123" />
<hkern u1="&#xc5;" u2="W" k="82" />
<hkern u1="&#xc5;" u2="V" k="82" />
<hkern u1="&#xc5;" u2="T" k="143" />
<hkern u1="&#xc5;" u2="Q" k="41" />
<hkern u1="&#xc5;" u2="O" k="41" />
<hkern u1="&#xc5;" u2="J" k="-266" />
<hkern u1="&#xc5;" u2="G" k="41" />
<hkern u1="&#xc5;" u2="C" k="41" />
<hkern u1="&#xc5;" u2="&#x27;" k="143" />
<hkern u1="&#xc5;" u2="&#x22;" k="143" />
<hkern u1="&#xc6;" u2="J" k="-123" />
<hkern u1="&#xc7;" u2="&#x152;" k="41" />
<hkern u1="&#xc7;" u2="&#xd8;" k="41" />
<hkern u1="&#xc7;" u2="&#xd6;" k="41" />
<hkern u1="&#xc7;" u2="&#xd5;" k="41" />
<hkern u1="&#xc7;" u2="&#xd4;" k="41" />
<hkern u1="&#xc7;" u2="&#xd3;" k="41" />
<hkern u1="&#xc7;" u2="&#xd2;" k="41" />
<hkern u1="&#xc7;" u2="&#xc7;" k="41" />
<hkern u1="&#xc7;" u2="Q" k="41" />
<hkern u1="&#xc7;" u2="O" k="41" />
<hkern u1="&#xc7;" u2="G" k="41" />
<hkern u1="&#xc7;" u2="C" k="41" />
<hkern u1="&#xc8;" u2="J" k="-123" />
<hkern u1="&#xc9;" u2="J" k="-123" />
<hkern u1="&#xca;" u2="J" k="-123" />
<hkern u1="&#xcb;" u2="J" k="-123" />
<hkern u1="&#xd0;" u2="&#x201e;" k="82" />
<hkern u1="&#xd0;" u2="&#x201a;" k="82" />
<hkern u1="&#xd0;" u2="&#x178;" k="20" />
<hkern u1="&#xd0;" u2="&#xdd;" k="20" />
<hkern u1="&#xd0;" u2="&#xc5;" k="41" />
<hkern u1="&#xd0;" u2="&#xc4;" k="41" />
<hkern u1="&#xd0;" u2="&#xc3;" k="41" />
<hkern u1="&#xd0;" u2="&#xc2;" k="41" />
<hkern u1="&#xd0;" u2="&#xc1;" k="41" />
<hkern u1="&#xd0;" u2="&#xc0;" k="41" />
<hkern u1="&#xd0;" u2="Z" k="20" />
<hkern u1="&#xd0;" u2="Y" k="20" />
<hkern u1="&#xd0;" u2="X" k="41" />
<hkern u1="&#xd0;" u2="W" k="20" />
<hkern u1="&#xd0;" u2="V" k="20" />
<hkern u1="&#xd0;" u2="T" k="61" />
<hkern u1="&#xd0;" u2="A" k="41" />
<hkern u1="&#xd0;" u2="&#x2e;" k="82" />
<hkern u1="&#xd0;" u2="&#x2c;" k="82" />
<hkern u1="&#xd2;" u2="&#x201e;" k="82" />
<hkern u1="&#xd2;" u2="&#x201a;" k="82" />
<hkern u1="&#xd2;" u2="&#x178;" k="20" />
<hkern u1="&#xd2;" u2="&#xdd;" k="20" />
<hkern u1="&#xd2;" u2="&#xc5;" k="41" />
<hkern u1="&#xd2;" u2="&#xc4;" k="41" />
<hkern u1="&#xd2;" u2="&#xc3;" k="41" />
<hkern u1="&#xd2;" u2="&#xc2;" k="41" />
<hkern u1="&#xd2;" u2="&#xc1;" k="41" />
<hkern u1="&#xd2;" u2="&#xc0;" k="41" />
<hkern u1="&#xd2;" u2="Z" k="20" />
<hkern u1="&#xd2;" u2="Y" k="20" />
<hkern u1="&#xd2;" u2="X" k="41" />
<hkern u1="&#xd2;" u2="W" k="20" />
<hkern u1="&#xd2;" u2="V" k="20" />
<hkern u1="&#xd2;" u2="T" k="61" />
<hkern u1="&#xd2;" u2="A" k="41" />
<hkern u1="&#xd2;" u2="&#x2e;" k="82" />
<hkern u1="&#xd2;" u2="&#x2c;" k="82" />
<hkern u1="&#xd3;" u2="&#x201e;" k="82" />
<hkern u1="&#xd3;" u2="&#x201a;" k="82" />
<hkern u1="&#xd3;" u2="&#x178;" k="20" />
<hkern u1="&#xd3;" u2="&#xdd;" k="20" />
<hkern u1="&#xd3;" u2="&#xc5;" k="41" />
<hkern u1="&#xd3;" u2="&#xc4;" k="41" />
<hkern u1="&#xd3;" u2="&#xc3;" k="41" />
<hkern u1="&#xd3;" u2="&#xc2;" k="41" />
<hkern u1="&#xd3;" u2="&#xc1;" k="41" />
<hkern u1="&#xd3;" u2="&#xc0;" k="41" />
<hkern u1="&#xd3;" u2="Z" k="20" />
<hkern u1="&#xd3;" u2="Y" k="20" />
<hkern u1="&#xd3;" u2="X" k="41" />
<hkern u1="&#xd3;" u2="W" k="20" />
<hkern u1="&#xd3;" u2="V" k="20" />
<hkern u1="&#xd3;" u2="T" k="61" />
<hkern u1="&#xd3;" u2="A" k="41" />
<hkern u1="&#xd3;" u2="&#x2e;" k="82" />
<hkern u1="&#xd3;" u2="&#x2c;" k="82" />
<hkern u1="&#xd4;" u2="&#x201e;" k="82" />
<hkern u1="&#xd4;" u2="&#x201a;" k="82" />
<hkern u1="&#xd4;" u2="&#x178;" k="20" />
<hkern u1="&#xd4;" u2="&#xdd;" k="20" />
<hkern u1="&#xd4;" u2="&#xc5;" k="41" />
<hkern u1="&#xd4;" u2="&#xc4;" k="41" />
<hkern u1="&#xd4;" u2="&#xc3;" k="41" />
<hkern u1="&#xd4;" u2="&#xc2;" k="41" />
<hkern u1="&#xd4;" u2="&#xc1;" k="41" />
<hkern u1="&#xd4;" u2="&#xc0;" k="41" />
<hkern u1="&#xd4;" u2="Z" k="20" />
<hkern u1="&#xd4;" u2="Y" k="20" />
<hkern u1="&#xd4;" u2="X" k="41" />
<hkern u1="&#xd4;" u2="W" k="20" />
<hkern u1="&#xd4;" u2="V" k="20" />
<hkern u1="&#xd4;" u2="T" k="61" />
<hkern u1="&#xd4;" u2="A" k="41" />
<hkern u1="&#xd4;" u2="&#x2e;" k="82" />
<hkern u1="&#xd4;" u2="&#x2c;" k="82" />
<hkern u1="&#xd5;" u2="&#x201e;" k="82" />
<hkern u1="&#xd5;" u2="&#x201a;" k="82" />
<hkern u1="&#xd5;" u2="&#x178;" k="20" />
<hkern u1="&#xd5;" u2="&#xdd;" k="20" />
<hkern u1="&#xd5;" u2="&#xc5;" k="41" />
<hkern u1="&#xd5;" u2="&#xc4;" k="41" />
<hkern u1="&#xd5;" u2="&#xc3;" k="41" />
<hkern u1="&#xd5;" u2="&#xc2;" k="41" />
<hkern u1="&#xd5;" u2="&#xc1;" k="41" />
<hkern u1="&#xd5;" u2="&#xc0;" k="41" />
<hkern u1="&#xd5;" u2="Z" k="20" />
<hkern u1="&#xd5;" u2="Y" k="20" />
<hkern u1="&#xd5;" u2="X" k="41" />
<hkern u1="&#xd5;" u2="W" k="20" />
<hkern u1="&#xd5;" u2="V" k="20" />
<hkern u1="&#xd5;" u2="T" k="61" />
<hkern u1="&#xd5;" u2="A" k="41" />
<hkern u1="&#xd5;" u2="&#x2e;" k="82" />
<hkern u1="&#xd5;" u2="&#x2c;" k="82" />
<hkern u1="&#xd6;" u2="&#x201e;" k="82" />
<hkern u1="&#xd6;" u2="&#x201a;" k="82" />
<hkern u1="&#xd6;" u2="&#x178;" k="20" />
<hkern u1="&#xd6;" u2="&#xdd;" k="20" />
<hkern u1="&#xd6;" u2="&#xc5;" k="41" />
<hkern u1="&#xd6;" u2="&#xc4;" k="41" />
<hkern u1="&#xd6;" u2="&#xc3;" k="41" />
<hkern u1="&#xd6;" u2="&#xc2;" k="41" />
<hkern u1="&#xd6;" u2="&#xc1;" k="41" />
<hkern u1="&#xd6;" u2="&#xc0;" k="41" />
<hkern u1="&#xd6;" u2="Z" k="20" />
<hkern u1="&#xd6;" u2="Y" k="20" />
<hkern u1="&#xd6;" u2="X" k="41" />
<hkern u1="&#xd6;" u2="W" k="20" />
<hkern u1="&#xd6;" u2="V" k="20" />
<hkern u1="&#xd6;" u2="T" k="61" />
<hkern u1="&#xd6;" u2="A" k="41" />
<hkern u1="&#xd6;" u2="&#x2e;" k="82" />
<hkern u1="&#xd6;" u2="&#x2c;" k="82" />
<hkern u1="&#xd8;" u2="&#x201e;" k="82" />
<hkern u1="&#xd8;" u2="&#x201a;" k="82" />
<hkern u1="&#xd8;" u2="&#x178;" k="20" />
<hkern u1="&#xd8;" u2="&#xdd;" k="20" />
<hkern u1="&#xd8;" u2="&#xc5;" k="41" />
<hkern u1="&#xd8;" u2="&#xc4;" k="41" />
<hkern u1="&#xd8;" u2="&#xc3;" k="41" />
<hkern u1="&#xd8;" u2="&#xc2;" k="41" />
<hkern u1="&#xd8;" u2="&#xc1;" k="41" />
<hkern u1="&#xd8;" u2="&#xc0;" k="41" />
<hkern u1="&#xd8;" u2="Z" k="20" />
<hkern u1="&#xd8;" u2="Y" k="20" />
<hkern u1="&#xd8;" u2="X" k="41" />
<hkern u1="&#xd8;" u2="W" k="20" />
<hkern u1="&#xd8;" u2="V" k="20" />
<hkern u1="&#xd8;" u2="T" k="61" />
<hkern u1="&#xd8;" u2="A" k="41" />
<hkern u1="&#xd8;" u2="&#x2e;" k="82" />
<hkern u1="&#xd8;" u2="&#x2c;" k="82" />
<hkern u1="&#xd9;" u2="&#x201e;" k="41" />
<hkern u1="&#xd9;" u2="&#x201a;" k="41" />
<hkern u1="&#xd9;" u2="&#xc5;" k="20" />
<hkern u1="&#xd9;" u2="&#xc4;" k="20" />
<hkern u1="&#xd9;" u2="&#xc3;" k="20" />
<hkern u1="&#xd9;" u2="&#xc2;" k="20" />
<hkern u1="&#xd9;" u2="&#xc1;" k="20" />
<hkern u1="&#xd9;" u2="&#xc0;" k="20" />
<hkern u1="&#xd9;" u2="A" k="20" />
<hkern u1="&#xd9;" u2="&#x2e;" k="41" />
<hkern u1="&#xd9;" u2="&#x2c;" k="41" />
<hkern u1="&#xda;" u2="&#x201e;" k="41" />
<hkern u1="&#xda;" u2="&#x201a;" k="41" />
<hkern u1="&#xda;" u2="&#xc5;" k="20" />
<hkern u1="&#xda;" u2="&#xc4;" k="20" />
<hkern u1="&#xda;" u2="&#xc3;" k="20" />
<hkern u1="&#xda;" u2="&#xc2;" k="20" />
<hkern u1="&#xda;" u2="&#xc1;" k="20" />
<hkern u1="&#xda;" u2="&#xc0;" k="20" />
<hkern u1="&#xda;" u2="A" k="20" />
<hkern u1="&#xda;" u2="&#x2e;" k="41" />
<hkern u1="&#xda;" u2="&#x2c;" k="41" />
<hkern u1="&#xdb;" u2="&#x201e;" k="41" />
<hkern u1="&#xdb;" u2="&#x201a;" k="41" />
<hkern u1="&#xdb;" u2="&#xc5;" k="20" />
<hkern u1="&#xdb;" u2="&#xc4;" k="20" />
<hkern u1="&#xdb;" u2="&#xc3;" k="20" />
<hkern u1="&#xdb;" u2="&#xc2;" k="20" />
<hkern u1="&#xdb;" u2="&#xc1;" k="20" />
<hkern u1="&#xdb;" u2="&#xc0;" k="20" />
<hkern u1="&#xdb;" u2="A" k="20" />
<hkern u1="&#xdb;" u2="&#x2e;" k="41" />
<hkern u1="&#xdb;" u2="&#x2c;" k="41" />
<hkern u1="&#xdc;" u2="&#x201e;" k="41" />
<hkern u1="&#xdc;" u2="&#x201a;" k="41" />
<hkern u1="&#xdc;" u2="&#xc5;" k="20" />
<hkern u1="&#xdc;" u2="&#xc4;" k="20" />
<hkern u1="&#xdc;" u2="&#xc3;" k="20" />
<hkern u1="&#xdc;" u2="&#xc2;" k="20" />
<hkern u1="&#xdc;" u2="&#xc1;" k="20" />
<hkern u1="&#xdc;" u2="&#xc0;" k="20" />
<hkern u1="&#xdc;" u2="A" k="20" />
<hkern u1="&#xdc;" u2="&#x2e;" k="41" />
<hkern u1="&#xdc;" u2="&#x2c;" k="41" />
<hkern u1="&#xdd;" u2="&#x201e;" k="123" />
<hkern u1="&#xdd;" u2="&#x201a;" k="123" />
<hkern u1="&#xdd;" u2="&#x153;" k="102" />
<hkern u1="&#xdd;" u2="&#x152;" k="41" />
<hkern u1="&#xdd;" u2="&#xfc;" k="61" />
<hkern u1="&#xdd;" u2="&#xfb;" k="61" />
<hkern u1="&#xdd;" u2="&#xfa;" k="61" />
<hkern u1="&#xdd;" u2="&#xf9;" k="61" />
<hkern u1="&#xdd;" u2="&#xf8;" k="102" />
<hkern u1="&#xdd;" u2="&#xf6;" k="102" />
<hkern u1="&#xdd;" u2="&#xf5;" k="102" />
<hkern u1="&#xdd;" u2="&#xf4;" k="102" />
<hkern u1="&#xdd;" u2="&#xf3;" k="102" />
<hkern u1="&#xdd;" u2="&#xf2;" k="102" />
<hkern u1="&#xdd;" u2="&#xeb;" k="102" />
<hkern u1="&#xdd;" u2="&#xea;" k="102" />
<hkern u1="&#xdd;" u2="&#xe9;" k="102" />
<hkern u1="&#xdd;" u2="&#xe8;" k="102" />
<hkern u1="&#xdd;" u2="&#xe7;" k="102" />
<hkern u1="&#xdd;" u2="&#xe6;" k="102" />
<hkern u1="&#xdd;" u2="&#xe5;" k="102" />
<hkern u1="&#xdd;" u2="&#xe4;" k="102" />
<hkern u1="&#xdd;" u2="&#xe3;" k="102" />
<hkern u1="&#xdd;" u2="&#xe2;" k="102" />
<hkern u1="&#xdd;" u2="&#xe1;" k="102" />
<hkern u1="&#xdd;" u2="&#xe0;" k="102" />
<hkern u1="&#xdd;" u2="&#xd8;" k="41" />
<hkern u1="&#xdd;" u2="&#xd6;" k="41" />
<hkern u1="&#xdd;" u2="&#xd5;" k="41" />
<hkern u1="&#xdd;" u2="&#xd4;" k="41" />
<hkern u1="&#xdd;" u2="&#xd3;" k="41" />
<hkern u1="&#xdd;" u2="&#xd2;" k="41" />
<hkern u1="&#xdd;" u2="&#xc7;" k="41" />
<hkern u1="&#xdd;" u2="&#xc5;" k="123" />
<hkern u1="&#xdd;" u2="&#xc4;" k="123" />
<hkern u1="&#xdd;" u2="&#xc3;" k="123" />
<hkern u1="&#xdd;" u2="&#xc2;" k="123" />
<hkern u1="&#xdd;" u2="&#xc1;" k="123" />
<hkern u1="&#xdd;" u2="&#xc0;" k="123" />
<hkern u1="&#xdd;" u2="z" k="41" />
<hkern u1="&#xdd;" u2="u" k="61" />
<hkern u1="&#xdd;" u2="s" k="82" />
<hkern u1="&#xdd;" u2="r" k="61" />
<hkern u1="&#xdd;" u2="q" k="102" />
<hkern u1="&#xdd;" u2="p" k="61" />
<hkern u1="&#xdd;" u2="o" k="102" />
<hkern u1="&#xdd;" u2="n" k="61" />
<hkern u1="&#xdd;" u2="m" k="61" />
<hkern u1="&#xdd;" u2="g" k="41" />
<hkern u1="&#xdd;" u2="e" k="102" />
<hkern u1="&#xdd;" u2="d" k="102" />
<hkern u1="&#xdd;" u2="c" k="102" />
<hkern u1="&#xdd;" u2="a" k="102" />
<hkern u1="&#xdd;" u2="Q" k="41" />
<hkern u1="&#xdd;" u2="O" k="41" />
<hkern u1="&#xdd;" u2="G" k="41" />
<hkern u1="&#xdd;" u2="C" k="41" />
<hkern u1="&#xdd;" u2="A" k="123" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-41" />
<hkern u1="&#xdd;" u2="&#x2e;" k="123" />
<hkern u1="&#xdd;" u2="&#x2c;" k="123" />
<hkern u1="&#xde;" u2="&#x201e;" k="266" />
<hkern u1="&#xde;" u2="&#x201a;" k="266" />
<hkern u1="&#xde;" u2="&#xc5;" k="102" />
<hkern u1="&#xde;" u2="&#xc4;" k="102" />
<hkern u1="&#xde;" u2="&#xc3;" k="102" />
<hkern u1="&#xde;" u2="&#xc2;" k="102" />
<hkern u1="&#xde;" u2="&#xc1;" k="102" />
<hkern u1="&#xde;" u2="&#xc0;" k="102" />
<hkern u1="&#xde;" u2="Z" k="20" />
<hkern u1="&#xde;" u2="X" k="41" />
<hkern u1="&#xde;" u2="A" k="102" />
<hkern u1="&#xde;" u2="&#x2e;" k="266" />
<hkern u1="&#xde;" u2="&#x2c;" k="266" />
<hkern u1="&#xe0;" u2="&#x201d;" k="20" />
<hkern u1="&#xe0;" u2="&#x2019;" k="20" />
<hkern u1="&#xe0;" u2="&#x27;" k="20" />
<hkern u1="&#xe0;" u2="&#x22;" k="20" />
<hkern u1="&#xe1;" u2="&#x201d;" k="20" />
<hkern u1="&#xe1;" u2="&#x2019;" k="20" />
<hkern u1="&#xe1;" u2="&#x27;" k="20" />
<hkern u1="&#xe1;" u2="&#x22;" k="20" />
<hkern u1="&#xe2;" u2="&#x201d;" k="20" />
<hkern u1="&#xe2;" u2="&#x2019;" k="20" />
<hkern u1="&#xe2;" u2="&#x27;" k="20" />
<hkern u1="&#xe2;" u2="&#x22;" k="20" />
<hkern u1="&#xe3;" u2="&#x201d;" k="20" />
<hkern u1="&#xe3;" u2="&#x2019;" k="20" />
<hkern u1="&#xe3;" u2="&#x27;" k="20" />
<hkern u1="&#xe3;" u2="&#x22;" k="20" />
<hkern u1="&#xe4;" u2="&#x201d;" k="20" />
<hkern u1="&#xe4;" u2="&#x2019;" k="20" />
<hkern u1="&#xe4;" u2="&#x27;" k="20" />
<hkern u1="&#xe4;" u2="&#x22;" k="20" />
<hkern u1="&#xe5;" u2="&#x201d;" k="20" />
<hkern u1="&#xe5;" u2="&#x2019;" k="20" />
<hkern u1="&#xe5;" u2="&#x27;" k="20" />
<hkern u1="&#xe5;" u2="&#x22;" k="20" />
<hkern u1="&#xe8;" u2="&#x201d;" k="20" />
<hkern u1="&#xe8;" u2="&#x2019;" k="20" />
<hkern u1="&#xe8;" u2="&#xfd;" k="41" />
<hkern u1="&#xe8;" u2="z" k="20" />
<hkern u1="&#xe8;" u2="y" k="41" />
<hkern u1="&#xe8;" u2="x" k="41" />
<hkern u1="&#xe8;" u2="w" k="41" />
<hkern u1="&#xe8;" u2="v" k="41" />
<hkern u1="&#xe8;" u2="&#x27;" k="20" />
<hkern u1="&#xe8;" u2="&#x22;" k="20" />
<hkern u1="&#xe9;" u2="&#x201d;" k="20" />
<hkern u1="&#xe9;" u2="&#x2019;" k="20" />
<hkern u1="&#xe9;" u2="&#xfd;" k="41" />
<hkern u1="&#xe9;" u2="z" k="20" />
<hkern u1="&#xe9;" u2="y" k="41" />
<hkern u1="&#xe9;" u2="x" k="41" />
<hkern u1="&#xe9;" u2="w" k="41" />
<hkern u1="&#xe9;" u2="v" k="41" />
<hkern u1="&#xe9;" u2="&#x27;" k="20" />
<hkern u1="&#xe9;" u2="&#x22;" k="20" />
<hkern u1="&#xea;" u2="&#x201d;" k="20" />
<hkern u1="&#xea;" u2="&#x2019;" k="20" />
<hkern u1="&#xea;" u2="&#xfd;" k="41" />
<hkern u1="&#xea;" u2="z" k="20" />
<hkern u1="&#xea;" u2="y" k="41" />
<hkern u1="&#xea;" u2="x" k="41" />
<hkern u1="&#xea;" u2="w" k="41" />
<hkern u1="&#xea;" u2="v" k="41" />
<hkern u1="&#xea;" u2="&#x27;" k="20" />
<hkern u1="&#xea;" u2="&#x22;" k="20" />
<hkern u1="&#xeb;" u2="&#x201d;" k="20" />
<hkern u1="&#xeb;" u2="&#x2019;" k="20" />
<hkern u1="&#xeb;" u2="&#xfd;" k="41" />
<hkern u1="&#xeb;" u2="z" k="20" />
<hkern u1="&#xeb;" u2="y" k="41" />
<hkern u1="&#xeb;" u2="x" k="41" />
<hkern u1="&#xeb;" u2="w" k="41" />
<hkern u1="&#xeb;" u2="v" k="41" />
<hkern u1="&#xeb;" u2="&#x27;" k="20" />
<hkern u1="&#xeb;" u2="&#x22;" k="20" />
<hkern u1="&#xf0;" u2="&#x201d;" k="20" />
<hkern u1="&#xf0;" u2="&#x2019;" k="20" />
<hkern u1="&#xf0;" u2="&#xfd;" k="41" />
<hkern u1="&#xf0;" u2="z" k="20" />
<hkern u1="&#xf0;" u2="y" k="41" />
<hkern u1="&#xf0;" u2="x" k="41" />
<hkern u1="&#xf0;" u2="w" k="41" />
<hkern u1="&#xf0;" u2="v" k="41" />
<hkern u1="&#xf0;" u2="&#x27;" k="20" />
<hkern u1="&#xf0;" u2="&#x22;" k="20" />
<hkern u1="&#xf2;" u2="&#x201d;" k="20" />
<hkern u1="&#xf2;" u2="&#x2019;" k="20" />
<hkern u1="&#xf2;" u2="&#xfd;" k="41" />
<hkern u1="&#xf2;" u2="z" k="20" />
<hkern u1="&#xf2;" u2="y" k="41" />
<hkern u1="&#xf2;" u2="x" k="41" />
<hkern u1="&#xf2;" u2="w" k="41" />
<hkern u1="&#xf2;" u2="v" k="41" />
<hkern u1="&#xf2;" u2="&#x27;" k="20" />
<hkern u1="&#xf2;" u2="&#x22;" k="20" />
<hkern u1="&#xf3;" u2="&#x201d;" k="20" />
<hkern u1="&#xf3;" u2="&#x2019;" k="20" />
<hkern u1="&#xf3;" u2="&#xfd;" k="41" />
<hkern u1="&#xf3;" u2="z" k="20" />
<hkern u1="&#xf3;" u2="y" k="41" />
<hkern u1="&#xf3;" u2="x" k="41" />
<hkern u1="&#xf3;" u2="w" k="41" />
<hkern u1="&#xf3;" u2="v" k="41" />
<hkern u1="&#xf3;" u2="&#x27;" k="20" />
<hkern u1="&#xf3;" u2="&#x22;" k="20" />
<hkern u1="&#xf4;" u2="&#x201d;" k="20" />
<hkern u1="&#xf4;" u2="&#x2019;" k="20" />
<hkern u1="&#xf4;" u2="&#xfd;" k="41" />
<hkern u1="&#xf4;" u2="z" k="20" />
<hkern u1="&#xf4;" u2="y" k="41" />
<hkern u1="&#xf4;" u2="x" k="41" />
<hkern u1="&#xf4;" u2="w" k="41" />
<hkern u1="&#xf4;" u2="v" k="41" />
<hkern u1="&#xf4;" u2="&#x27;" k="20" />
<hkern u1="&#xf4;" u2="&#x22;" k="20" />
<hkern u1="&#xf6;" u2="&#x201d;" k="41" />
<hkern u1="&#xf6;" u2="&#x2019;" k="41" />
<hkern u1="&#xf6;" u2="&#x27;" k="41" />
<hkern u1="&#xf6;" u2="&#x22;" k="41" />
<hkern u1="&#xf8;" u2="&#x201d;" k="20" />
<hkern u1="&#xf8;" u2="&#x2019;" k="20" />
<hkern u1="&#xf8;" u2="&#xfd;" k="41" />
<hkern u1="&#xf8;" u2="z" k="20" />
<hkern u1="&#xf8;" u2="y" k="41" />
<hkern u1="&#xf8;" u2="x" k="41" />
<hkern u1="&#xf8;" u2="w" k="41" />
<hkern u1="&#xf8;" u2="v" k="41" />
<hkern u1="&#xf8;" u2="&#x27;" k="20" />
<hkern u1="&#xf8;" u2="&#x22;" k="20" />
<hkern u1="&#xfd;" u2="&#x201e;" k="82" />
<hkern u1="&#xfd;" u2="&#x201d;" k="-82" />
<hkern u1="&#xfd;" u2="&#x201a;" k="82" />
<hkern u1="&#xfd;" u2="&#x2019;" k="-82" />
<hkern u1="&#xfd;" u2="&#x3f;" k="-41" />
<hkern u1="&#xfd;" u2="&#x2e;" k="82" />
<hkern u1="&#xfd;" u2="&#x2c;" k="82" />
<hkern u1="&#xfd;" u2="&#x27;" k="-82" />
<hkern u1="&#xfd;" u2="&#x22;" k="-82" />
<hkern u1="&#xfe;" u2="&#x201d;" k="20" />
<hkern u1="&#xfe;" u2="&#x2019;" k="20" />
<hkern u1="&#xfe;" u2="&#xfd;" k="41" />
<hkern u1="&#xfe;" u2="z" k="20" />
<hkern u1="&#xfe;" u2="y" k="41" />
<hkern u1="&#xfe;" u2="x" k="41" />
<hkern u1="&#xfe;" u2="w" k="41" />
<hkern u1="&#xfe;" u2="v" k="41" />
<hkern u1="&#xfe;" u2="&#x27;" k="20" />
<hkern u1="&#xfe;" u2="&#x22;" k="20" />
<hkern u1="&#xff;" u2="&#x201e;" k="82" />
<hkern u1="&#xff;" u2="&#x201d;" k="-82" />
<hkern u1="&#xff;" u2="&#x201a;" k="82" />
<hkern u1="&#xff;" u2="&#x2019;" k="-82" />
<hkern u1="&#xff;" u2="&#x3f;" k="-41" />
<hkern u1="&#xff;" u2="&#x2e;" k="82" />
<hkern u1="&#xff;" u2="&#x2c;" k="82" />
<hkern u1="&#xff;" u2="&#x27;" k="-82" />
<hkern u1="&#xff;" u2="&#x22;" k="-82" />
<hkern u1="&#x152;" u2="J" k="-123" />
<hkern u1="&#x178;" u2="&#x201e;" k="123" />
<hkern u1="&#x178;" u2="&#x201a;" k="123" />
<hkern u1="&#x178;" u2="&#x153;" k="102" />
<hkern u1="&#x178;" u2="&#x152;" k="41" />
<hkern u1="&#x178;" u2="&#xfc;" k="61" />
<hkern u1="&#x178;" u2="&#xfb;" k="61" />
<hkern u1="&#x178;" u2="&#xfa;" k="61" />
<hkern u1="&#x178;" u2="&#xf9;" k="61" />
<hkern u1="&#x178;" u2="&#xf8;" k="102" />
<hkern u1="&#x178;" u2="&#xf6;" k="102" />
<hkern u1="&#x178;" u2="&#xf5;" k="102" />
<hkern u1="&#x178;" u2="&#xf4;" k="102" />
<hkern u1="&#x178;" u2="&#xf3;" k="102" />
<hkern u1="&#x178;" u2="&#xf2;" k="102" />
<hkern u1="&#x178;" u2="&#xeb;" k="102" />
<hkern u1="&#x178;" u2="&#xea;" k="102" />
<hkern u1="&#x178;" u2="&#xe9;" k="102" />
<hkern u1="&#x178;" u2="&#xe8;" k="102" />
<hkern u1="&#x178;" u2="&#xe7;" k="102" />
<hkern u1="&#x178;" u2="&#xe6;" k="102" />
<hkern u1="&#x178;" u2="&#xe5;" k="102" />
<hkern u1="&#x178;" u2="&#xe4;" k="102" />
<hkern u1="&#x178;" u2="&#xe3;" k="102" />
<hkern u1="&#x178;" u2="&#xe2;" k="102" />
<hkern u1="&#x178;" u2="&#xe1;" k="102" />
<hkern u1="&#x178;" u2="&#xe0;" k="102" />
<hkern u1="&#x178;" u2="&#xd8;" k="41" />
<hkern u1="&#x178;" u2="&#xd6;" k="41" />
<hkern u1="&#x178;" u2="&#xd5;" k="41" />
<hkern u1="&#x178;" u2="&#xd4;" k="41" />
<hkern u1="&#x178;" u2="&#xd3;" k="41" />
<hkern u1="&#x178;" u2="&#xd2;" k="41" />
<hkern u1="&#x178;" u2="&#xc7;" k="41" />
<hkern u1="&#x178;" u2="&#xc5;" k="123" />
<hkern u1="&#x178;" u2="&#xc4;" k="123" />
<hkern u1="&#x178;" u2="&#xc3;" k="123" />
<hkern u1="&#x178;" u2="&#xc2;" k="123" />
<hkern u1="&#x178;" u2="&#xc1;" k="123" />
<hkern u1="&#x178;" u2="&#xc0;" k="123" />
<hkern u1="&#x178;" u2="z" k="41" />
<hkern u1="&#x178;" u2="u" k="61" />
<hkern u1="&#x178;" u2="s" k="82" />
<hkern u1="&#x178;" u2="r" k="61" />
<hkern u1="&#x178;" u2="q" k="102" />
<hkern u1="&#x178;" u2="p" k="61" />
<hkern u1="&#x178;" u2="o" k="102" />
<hkern u1="&#x178;" u2="n" k="61" />
<hkern u1="&#x178;" u2="m" k="61" />
<hkern u1="&#x178;" u2="g" k="41" />
<hkern u1="&#x178;" u2="e" k="102" />
<hkern u1="&#x178;" u2="d" k="102" />
<hkern u1="&#x178;" u2="c" k="102" />
<hkern u1="&#x178;" u2="a" k="102" />
<hkern u1="&#x178;" u2="Q" k="41" />
<hkern u1="&#x178;" u2="O" k="41" />
<hkern u1="&#x178;" u2="G" k="41" />
<hkern u1="&#x178;" u2="C" k="41" />
<hkern u1="&#x178;" u2="A" k="123" />
<hkern u1="&#x178;" u2="&#x3f;" k="-41" />
<hkern u1="&#x178;" u2="&#x2e;" k="123" />
<hkern u1="&#x178;" u2="&#x2c;" k="123" />
<hkern u1="&#x2013;" u2="T" k="82" />
<hkern u1="&#x2014;" u2="T" k="82" />
<hkern u1="&#x2018;" u2="&#x178;" k="-20" />
<hkern u1="&#x2018;" u2="&#x153;" k="123" />
<hkern u1="&#x2018;" u2="&#xfc;" k="61" />
<hkern u1="&#x2018;" u2="&#xfb;" k="61" />
<hkern u1="&#x2018;" u2="&#xfa;" k="61" />
<hkern u1="&#x2018;" u2="&#xf9;" k="61" />
<hkern u1="&#x2018;" u2="&#xf8;" k="123" />
<hkern u1="&#x2018;" u2="&#xf6;" k="123" />
<hkern u1="&#x2018;" u2="&#xf5;" k="123" />
<hkern u1="&#x2018;" u2="&#xf4;" k="123" />
<hkern u1="&#x2018;" u2="&#xf3;" k="123" />
<hkern u1="&#x2018;" u2="&#xf2;" k="123" />
<hkern u1="&#x2018;" u2="&#xeb;" k="123" />
<hkern u1="&#x2018;" u2="&#xea;" k="123" />
<hkern u1="&#x2018;" u2="&#xe9;" k="123" />
<hkern u1="&#x2018;" u2="&#xe8;" k="123" />
<hkern u1="&#x2018;" u2="&#xe7;" k="123" />
<hkern u1="&#x2018;" u2="&#xe6;" k="82" />
<hkern u1="&#x2018;" u2="&#xe5;" k="82" />
<hkern u1="&#x2018;" u2="&#xe4;" k="82" />
<hkern u1="&#x2018;" u2="&#xe3;" k="82" />
<hkern u1="&#x2018;" u2="&#xe2;" k="82" />
<hkern u1="&#x2018;" u2="&#xe1;" k="82" />
<hkern u1="&#x2018;" u2="&#xe0;" k="123" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-20" />
<hkern u1="&#x2018;" u2="&#xc5;" k="143" />
<hkern u1="&#x2018;" u2="&#xc4;" k="143" />
<hkern u1="&#x2018;" u2="&#xc3;" k="143" />
<hkern u1="&#x2018;" u2="&#xc2;" k="143" />
<hkern u1="&#x2018;" u2="&#xc1;" k="143" />
<hkern u1="&#x2018;" u2="&#xc0;" k="143" />
<hkern u1="&#x2018;" u2="u" k="61" />
<hkern u1="&#x2018;" u2="s" k="61" />
<hkern u1="&#x2018;" u2="r" k="61" />
<hkern u1="&#x2018;" u2="q" k="123" />
<hkern u1="&#x2018;" u2="p" k="61" />
<hkern u1="&#x2018;" u2="o" k="123" />
<hkern u1="&#x2018;" u2="n" k="61" />
<hkern u1="&#x2018;" u2="m" k="61" />
<hkern u1="&#x2018;" u2="g" k="61" />
<hkern u1="&#x2018;" u2="e" k="123" />
<hkern u1="&#x2018;" u2="d" k="123" />
<hkern u1="&#x2018;" u2="c" k="123" />
<hkern u1="&#x2018;" u2="a" k="82" />
<hkern u1="&#x2018;" u2="Y" k="-20" />
<hkern u1="&#x2018;" u2="W" k="-41" />
<hkern u1="&#x2018;" u2="V" k="-41" />
<hkern u1="&#x2018;" u2="T" k="-41" />
<hkern u1="&#x2018;" u2="A" k="143" />
<hkern u1="&#x2019;" u2="&#x178;" k="-20" />
<hkern u1="&#x2019;" u2="&#x153;" k="123" />
<hkern u1="&#x2019;" u2="&#xfc;" k="61" />
<hkern u1="&#x2019;" u2="&#xfb;" k="61" />
<hkern u1="&#x2019;" u2="&#xfa;" k="61" />
<hkern u1="&#x2019;" u2="&#xf9;" k="61" />
<hkern u1="&#x2019;" u2="&#xf8;" k="123" />
<hkern u1="&#x2019;" u2="&#xf6;" k="123" />
<hkern u1="&#x2019;" u2="&#xf5;" k="123" />
<hkern u1="&#x2019;" u2="&#xf4;" k="123" />
<hkern u1="&#x2019;" u2="&#xf3;" k="123" />
<hkern u1="&#x2019;" u2="&#xf2;" k="123" />
<hkern u1="&#x2019;" u2="&#xeb;" k="123" />
<hkern u1="&#x2019;" u2="&#xea;" k="123" />
<hkern u1="&#x2019;" u2="&#xe9;" k="123" />
<hkern u1="&#x2019;" u2="&#xe8;" k="123" />
<hkern u1="&#x2019;" u2="&#xe7;" k="123" />
<hkern u1="&#x2019;" u2="&#xe6;" k="82" />
<hkern u1="&#x2019;" u2="&#xe5;" k="82" />
<hkern u1="&#x2019;" u2="&#xe4;" k="82" />
<hkern u1="&#x2019;" u2="&#xe3;" k="82" />
<hkern u1="&#x2019;" u2="&#xe2;" k="82" />
<hkern u1="&#x2019;" u2="&#xe1;" k="82" />
<hkern u1="&#x2019;" u2="&#xe0;" k="123" />
<hkern u1="&#x2019;" u2="&#xdd;" k="-20" />
<hkern u1="&#x2019;" u2="&#xc5;" k="143" />
<hkern u1="&#x2019;" u2="&#xc4;" k="143" />
<hkern u1="&#x2019;" u2="&#xc3;" k="143" />
<hkern u1="&#x2019;" u2="&#xc2;" k="143" />
<hkern u1="&#x2019;" u2="&#xc1;" k="143" />
<hkern u1="&#x2019;" u2="&#xc0;" k="143" />
<hkern u1="&#x2019;" u2="u" k="61" />
<hkern u1="&#x2019;" u2="s" k="61" />
<hkern u1="&#x2019;" u2="r" k="61" />
<hkern u1="&#x2019;" u2="q" k="123" />
<hkern u1="&#x2019;" u2="p" k="61" />
<hkern u1="&#x2019;" u2="o" k="123" />
<hkern u1="&#x2019;" u2="n" k="61" />
<hkern u1="&#x2019;" u2="m" k="61" />
<hkern u1="&#x2019;" u2="g" k="61" />
<hkern u1="&#x2019;" u2="e" k="123" />
<hkern u1="&#x2019;" u2="d" k="123" />
<hkern u1="&#x2019;" u2="c" k="123" />
<hkern u1="&#x2019;" u2="a" k="82" />
<hkern u1="&#x2019;" u2="Y" k="-20" />
<hkern u1="&#x2019;" u2="W" k="-41" />
<hkern u1="&#x2019;" u2="V" k="-41" />
<hkern u1="&#x2019;" u2="T" k="-41" />
<hkern u1="&#x2019;" u2="A" k="143" />
<hkern u1="&#x201a;" u2="&#x178;" k="123" />
<hkern u1="&#x201a;" u2="&#x152;" k="102" />
<hkern u1="&#x201a;" u2="&#xdd;" k="123" />
<hkern u1="&#x201a;" u2="&#xdc;" k="41" />
<hkern u1="&#x201a;" u2="&#xdb;" k="41" />
<hkern u1="&#x201a;" u2="&#xda;" k="41" />
<hkern u1="&#x201a;" u2="&#xd9;" k="41" />
<hkern u1="&#x201a;" u2="&#xd8;" k="102" />
<hkern u1="&#x201a;" u2="&#xd6;" k="102" />
<hkern u1="&#x201a;" u2="&#xd5;" k="102" />
<hkern u1="&#x201a;" u2="&#xd4;" k="102" />
<hkern u1="&#x201a;" u2="&#xd3;" k="102" />
<hkern u1="&#x201a;" u2="&#xd2;" k="102" />
<hkern u1="&#x201a;" u2="&#xc7;" k="102" />
<hkern u1="&#x201a;" u2="Y" k="123" />
<hkern u1="&#x201a;" u2="W" k="123" />
<hkern u1="&#x201a;" u2="V" k="123" />
<hkern u1="&#x201a;" u2="U" k="41" />
<hkern u1="&#x201a;" u2="T" k="143" />
<hkern u1="&#x201a;" u2="Q" k="102" />
<hkern u1="&#x201a;" u2="O" k="102" />
<hkern u1="&#x201a;" u2="G" k="102" />
<hkern u1="&#x201a;" u2="C" k="102" />
<hkern u1="&#x201c;" u2="&#x178;" k="-20" />
<hkern u1="&#x201c;" u2="&#x153;" k="123" />
<hkern u1="&#x201c;" u2="&#xfc;" k="61" />
<hkern u1="&#x201c;" u2="&#xfb;" k="61" />
<hkern u1="&#x201c;" u2="&#xfa;" k="61" />
<hkern u1="&#x201c;" u2="&#xf9;" k="61" />
<hkern u1="&#x201c;" u2="&#xf8;" k="123" />
<hkern u1="&#x201c;" u2="&#xf6;" k="123" />
<hkern u1="&#x201c;" u2="&#xf5;" k="123" />
<hkern u1="&#x201c;" u2="&#xf4;" k="123" />
<hkern u1="&#x201c;" u2="&#xf3;" k="123" />
<hkern u1="&#x201c;" u2="&#xf2;" k="123" />
<hkern u1="&#x201c;" u2="&#xeb;" k="123" />
<hkern u1="&#x201c;" u2="&#xea;" k="123" />
<hkern u1="&#x201c;" u2="&#xe9;" k="123" />
<hkern u1="&#x201c;" u2="&#xe8;" k="123" />
<hkern u1="&#x201c;" u2="&#xe7;" k="123" />
<hkern u1="&#x201c;" u2="&#xe6;" k="82" />
<hkern u1="&#x201c;" u2="&#xe5;" k="82" />
<hkern u1="&#x201c;" u2="&#xe4;" k="82" />
<hkern u1="&#x201c;" u2="&#xe3;" k="82" />
<hkern u1="&#x201c;" u2="&#xe2;" k="82" />
<hkern u1="&#x201c;" u2="&#xe1;" k="82" />
<hkern u1="&#x201c;" u2="&#xe0;" k="123" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-20" />
<hkern u1="&#x201c;" u2="&#xc5;" k="143" />
<hkern u1="&#x201c;" u2="&#xc4;" k="143" />
<hkern u1="&#x201c;" u2="&#xc3;" k="143" />
<hkern u1="&#x201c;" u2="&#xc2;" k="143" />
<hkern u1="&#x201c;" u2="&#xc1;" k="143" />
<hkern u1="&#x201c;" u2="&#xc0;" k="143" />
<hkern u1="&#x201c;" u2="u" k="61" />
<hkern u1="&#x201c;" u2="s" k="61" />
<hkern u1="&#x201c;" u2="r" k="61" />
<hkern u1="&#x201c;" u2="q" k="123" />
<hkern u1="&#x201c;" u2="p" k="61" />
<hkern u1="&#x201c;" u2="o" k="123" />
<hkern u1="&#x201c;" u2="n" k="61" />
<hkern u1="&#x201c;" u2="m" k="61" />
<hkern u1="&#x201c;" u2="g" k="61" />
<hkern u1="&#x201c;" u2="e" k="123" />
<hkern u1="&#x201c;" u2="d" k="123" />
<hkern u1="&#x201c;" u2="c" k="123" />
<hkern u1="&#x201c;" u2="a" k="82" />
<hkern u1="&#x201c;" u2="Y" k="-20" />
<hkern u1="&#x201c;" u2="W" k="-41" />
<hkern u1="&#x201c;" u2="V" k="-41" />
<hkern u1="&#x201c;" u2="T" k="-41" />
<hkern u1="&#x201c;" u2="A" k="143" />
<hkern u1="&#x201e;" u2="&#x178;" k="123" />
<hkern u1="&#x201e;" u2="&#x152;" k="102" />
<hkern u1="&#x201e;" u2="&#xdd;" k="123" />
<hkern u1="&#x201e;" u2="&#xdc;" k="41" />
<hkern u1="&#x201e;" u2="&#xdb;" k="41" />
<hkern u1="&#x201e;" u2="&#xda;" k="41" />
<hkern u1="&#x201e;" u2="&#xd9;" k="41" />
<hkern u1="&#x201e;" u2="&#xd8;" k="102" />
<hkern u1="&#x201e;" u2="&#xd6;" k="102" />
<hkern u1="&#x201e;" u2="&#xd5;" k="102" />
<hkern u1="&#x201e;" u2="&#xd4;" k="102" />
<hkern u1="&#x201e;" u2="&#xd3;" k="102" />
<hkern u1="&#x201e;" u2="&#xd2;" k="102" />
<hkern u1="&#x201e;" u2="&#xc7;" k="102" />
<hkern u1="&#x201e;" u2="Y" k="123" />
<hkern u1="&#x201e;" u2="W" k="123" />
<hkern u1="&#x201e;" u2="V" k="123" />
<hkern u1="&#x201e;" u2="U" k="41" />
<hkern u1="&#x201e;" u2="T" k="143" />
<hkern u1="&#x201e;" u2="Q" k="102" />
<hkern u1="&#x201e;" u2="O" k="102" />
<hkern u1="&#x201e;" u2="G" k="102" />
<hkern u1="&#x201e;" u2="C" k="102" />
</font>
</defs></svg> 