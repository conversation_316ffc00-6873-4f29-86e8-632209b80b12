import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { TeachersService } from './teachers.service';
import { CreateTeacherDto } from './dto/create-teacher.dto';
import { UpdateTeacherProfileDto } from './dto/update-teacher-profile.dto';
import { UpdateTeacherAdminDto } from './dto/update-teacher-admin.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiQuery,
} from '@nestjs/swagger';
import { TrackingService } from '../common/services';

/**
 * Teacher management endpoints
 *
 * These endpoints allow college admins to add, manage, and remove teachers.
 * Teachers are users with the 'teacher' role who can create and manage question papers.
 */
@ApiTags('Teachers')
@ApiBearerAuth('JWT-auth')
@Controller()
@UseGuards(JwtAuthGuard, RolesGuard)
export class TeachersController {
  constructor(
    private readonly teachersService: TeachersService,
    private readonly trackingService: TrackingService,
  ) {}

  /**
   * Add a new teacher to a college
   * @param collegeId College ID
   * @param createTeacherDto Teacher data
   * @returns The created teacher
   */
  @Post('colleges/:collegeId/teachers')
  @Roles('collegeAdmin')
  @ApiOperation({
    summary: 'Add a teacher to a college',
    description:
      'Adds a new teacher to a specific college (college admin only). Only name and email are required - phone, department, designation, and profile image are optional.',
  })
  @ApiParam({
    name: 'collegeId',
    description: 'College ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiBody({ type: CreateTeacherDto })
  @ApiCreatedResponse({
    description: 'Teacher added successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        email: { type: 'string', example: '<EMAIL>' },
        displayName: { type: 'string', example: 'John Doe' },
        firstName: { type: 'string', example: 'John' },
        lastName: { type: 'string', example: 'Doe' },
        phone: { type: 'string', example: '+****************', nullable: true },
        role: { type: 'string', example: 'teacher' },
        collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        status: { type: 'string', example: 'active' },
        department: {
          type: 'string',
          example: 'Computer Science',
          nullable: true,
        },
        designation: {
          type: 'string',
          example: 'Associate Professor',
          nullable: true,
        },
        profileImageUrl: {
          type: 'string',
          example: 'https://example.com/images/teacher-profile.jpg',
          nullable: true,
        },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Bad request - Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'College not found' })
  addTeacherToCollege(
    @Param('collegeId') collegeId: string,
    @Body() createTeacherDto: CreateTeacherDto,
  ) {
    return this.teachersService.addTeacherToCollege(
      collegeId,
      createTeacherDto,
    );
  }

  /**
   * Get all teachers in a college
   * @param collegeId College ID
   * @returns List of teachers in the college
   */
  @Get('colleges/:collegeId/teachers')
  @Roles('collegeAdmin')
  @ApiOperation({
    summary: 'Get all teachers in a college',
    description:
      'Returns all teachers associated with a specific college (college admin only)',
  })
  @ApiParam({
    name: 'collegeId',
    description: 'College ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'Returns all teachers in the college',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          email: { type: 'string', example: '<EMAIL>' },
          displayName: { type: 'string', example: 'John Doe' },
          firstName: { type: 'string', example: 'John' },
          lastName: { type: 'string', example: 'Doe' },
          phone: { type: 'string', example: '+****************' },
          role: { type: 'string', example: 'teacher' },
          collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          status: { type: 'string', example: 'active' },
          department: { type: 'string', example: 'Computer Science' },
          designation: { type: 'string', example: 'Associate Professor' },
          profileImageUrl: {
            type: 'string',
            example: 'https://example.com/images/teacher-profile.jpg',
          },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'College not found' })
  findAllTeachersInCollege(@Param('collegeId') collegeId: string) {
    return this.teachersService.findAllTeachersInCollege(collegeId);
  }

  /**
   * Get a specific teacher by ID
   * @param id Teacher ID
   * @returns The teacher with the specified ID
   */
  @Get('teachers/:id')
  @Roles('collegeAdmin', 'teacher')
  @ApiOperation({
    summary: 'Get a teacher by ID',
    description:
      'Returns a teacher by ID (college admin or teacher can access)',
  })
  @ApiParam({
    name: 'id',
    description: 'Teacher ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'Returns the teacher',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        email: { type: 'string', example: '<EMAIL>' },
        displayName: { type: 'string', example: 'John Doe' },
        firstName: { type: 'string', example: 'John' },
        lastName: { type: 'string', example: 'Doe' },
        phone: { type: 'string', example: '+****************' },
        role: { type: 'string', example: 'teacher' },
        collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        status: { type: 'string', example: 'active' },
        department: { type: 'string', example: 'Computer Science' },
        designation: { type: 'string', example: 'Associate Professor' },
        profileImageUrl: {
          type: 'string',
          example: 'https://example.com/images/teacher-profile.jpg',
        },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'Teacher not found' })
  findOne(@Param('id') id: string) {
    return this.teachersService.findOne(id);
  }

  /**
   * Update teacher's own profile
   * @param user Current user from JWT token
   * @param updateTeacherProfileDto Updated teacher data (restricted fields)
   * @returns The updated teacher
   */
  @Put('teachers/me')
  @Roles('teacher')
  @ApiOperation({
    summary: 'Update own profile',
    description:
      'Allows teachers to update their own profile information (excludes status, designation, department, and timestamps)',
  })
  @ApiBody({ type: UpdateTeacherProfileDto })
  @ApiOkResponse({
    description: 'Profile updated successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        email: { type: 'string', example: '<EMAIL>' },
        displayName: { type: 'string', example: 'John Doe' },
        firstName: { type: 'string', example: 'John' },
        lastName: { type: 'string', example: 'Doe' },
        phone: { type: 'string', example: '+****************' },
        role: { type: 'string', example: 'teacher' },
        collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        status: { type: 'string', example: 'active' },
        department: { type: 'string', example: 'Computer Science' },
        designation: { type: 'string', example: 'Associate Professor' },
        profileImageUrl: {
          type: 'string',
          example: 'https://example.com/images/teacher-profile.jpg',
        },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Bad request - Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'Teacher not found' })
  updateOwnProfile(
    @CurrentUser() user: any,
    @Body() updateTeacherProfileDto: UpdateTeacherProfileDto,
  ) {
    // Use _id with fallback to userId for backward compatibility
    const userId = user._id || user.userId;
    return this.teachersService.update(userId, updateTeacherProfileDto);
  }

  /**
   * Update a teacher's information
   * @param id Teacher ID
   * @param updateTeacherAdminDto Updated teacher data (admin fields only)
   * @returns The updated teacher
   */
  @Put('teachers/:id')
  @Roles('collegeAdmin')
  @ApiOperation({
    summary: 'Update a teacher',
    description:
      "Updates a teacher's administrative information (college admin only) - phone, department, designation, status, role",
  })
  @ApiParam({
    name: 'id',
    description: 'Teacher ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiBody({ type: UpdateTeacherAdminDto })
  @ApiOkResponse({
    description: 'Teacher updated successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        email: { type: 'string', example: '<EMAIL>' },
        displayName: { type: 'string', example: 'John Doe' },
        firstName: { type: 'string', example: 'John' },
        lastName: { type: 'string', example: 'Doe' },
        phone: { type: 'string', example: '+****************' },
        role: { type: 'string', example: 'teacher' },
        collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        status: { type: 'string', example: 'active' },
        department: { type: 'string', example: 'Computer Science' },
        designation: { type: 'string', example: 'Associate Professor' },
        profileImageUrl: {
          type: 'string',
          example: 'https://example.com/images/teacher-profile.jpg',
        },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Bad request - Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'Teacher not found' })
  update(
    @Param('id') id: string,
    @Body() updateTeacherAdminDto: UpdateTeacherAdminDto,
  ) {
    return this.teachersService.update(id, updateTeacherAdminDto);
  }

  /**
   * Remove a teacher
   * @param id Teacher ID
   * @returns Success message
   */
  @Delete('teachers/:id')
  @Roles('collegeAdmin')
  @ApiOperation({
    summary: 'Remove a teacher',
    description: 'Removes a teacher from a college (college admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'Teacher ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'Teacher removed successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Teacher removed successfully' },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'Teacher not found' })
  remove(@Param('id') id: string) {
    return this.teachersService.remove(id);
  }

  /**
   * Get analytics for a specific teacher
   * @param id Teacher ID
   * @param startDate Start date for filtering (optional)
   * @param endDate End date for filtering (optional)
   * @param subjectId Subject ID for filtering (optional)
   * @returns Teacher analytics including downloads and paper generation stats
   */
  @Get('teachers/:id/analytics')
  @Roles('collegeAdmin', 'teacher')
  @ApiOperation({
    summary: 'Get teacher analytics',
    description:
      'Returns analytics for a specific teacher including download and paper generation statistics',
  })
  @ApiParam({
    name: 'id',
    description: 'Teacher ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date for filtering (ISO string)',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date for filtering (ISO string)',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiQuery({
    name: 'subjectId',
    description: 'Subject ID for filtering',
    required: false,
    example: '60d21b4667d0d8992e610c86',
  })
  @ApiOkResponse({
    description: 'Teacher analytics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        teacherId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        downloads: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              subjectId: {
                type: 'string',
                example: '60d21b4667d0d8992e610c86',
              },
              subjectName: { type: 'string', example: 'Mathematics' },
              totalDownloads: { type: 'number', example: 25 },
              formatBreakdown: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    format: { type: 'string', example: 'pdf' },
                    count: { type: 'number', example: 15 },
                  },
                },
              },
              lastDownload: { type: 'string', format: 'date-time' },
            },
          },
        },
        paperGeneration: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              subjectId: {
                type: 'string',
                example: '60d21b4667d0d8992e610c86',
              },
              subjectName: { type: 'string', example: 'Mathematics' },
              totalPapers: { type: 'number', example: 12 },
              lastGenerated: { type: 'string', format: 'date-time' },
            },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'Teacher not found' })
  async getTeacherAnalytics(
    @Param('id') id: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('subjectId') subjectId?: string,
  ) {
    const filters: any = {};

    if (startDate) {
      filters.startDate = new Date(startDate);
    }
    if (endDate) {
      filters.endDate = new Date(endDate);
    }
    if (subjectId) {
      filters.subjectId = subjectId;
    }

    const [downloads, paperGeneration] = await Promise.all([
      this.trackingService.getTeacherDownloadStats(id, filters),
      this.trackingService.getTeacherPaperGenerationStats(id, filters),
    ]);

    return {
      teacherId: id,
      downloads,
      paperGeneration,
    };
  }
}
