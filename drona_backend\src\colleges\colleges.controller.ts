import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
} from '@nestjs/common';
import { CollegesService } from './colleges.service';
import { CreateCollegeDto } from './dto/create-college.dto';
import { UpdateCollegeDto } from './dto/update-college.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';

/**
 * College management endpoints
 *
 * These endpoints allow super admins to create, manage, and delete colleges.
 * Colleges are the top-level organizational units in the system.
 */
@ApiTags('Colleges')
@ApiBearerAuth('JWT-auth')
@Controller('colleges')
@UseGuards(JwtAuthGuard, RolesGuard)
export class CollegesController {
  constructor(private readonly collegesService: CollegesService) {}

  /**
   * Create a new college
   * @param createCollegeDto College data
   * @returns The created college
   */
  @Post()
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Create a new college',
    description: 'Creates a new college (super admin only)',
  })
  @ApiBody({ type: CreateCollegeDto })
  @ApiCreatedResponse({
    description: 'College created successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        name: { type: 'string', example: 'Harvard University' },
        address: {
          type: 'string',
          example: '123 Main St, Cambridge, MA 02138',
        },
        city: { type: 'string', example: 'Cambridge' },
        state: { type: 'string', example: 'Massachusetts' },
        country: { type: 'string', example: 'USA' },
        postalCode: { type: 'string', example: '02138' },
        contactPhone: { type: 'string', example: '+****************' },
        contactEmail: { type: 'string', example: '<EMAIL>' },
        website: { type: 'string', example: 'https://www.harvard.edu' },
        logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
        status: { type: 'string', example: 'active' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Bad request - Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  create(@Body() createCollegeDto: CreateCollegeDto) {
    return this.collegesService.create(createCollegeDto);
  }

  /**
   * Get all colleges
   * @returns List of all colleges
   */
  @Get()
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Get all colleges',
    description: 'Returns all colleges (super admin only)',
  })
  @ApiOkResponse({
    description: 'Returns all colleges',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          name: { type: 'string', example: 'Harvard University' },
          address: {
            type: 'string',
            example: '123 Main St, Cambridge, MA 02138',
          },
          city: { type: 'string', example: 'Cambridge' },
          state: { type: 'string', example: 'Massachusetts' },
          country: { type: 'string', example: 'USA' },
          postalCode: { type: 'string', example: '02138' },
          contactPhone: { type: 'string', example: '+****************' },
          contactEmail: { type: 'string', example: '<EMAIL>' },
          website: { type: 'string', example: 'https://www.harvard.edu' },
          logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
          status: { type: 'string', example: 'active' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  findAll() {
    return this.collegesService.findAll();
  }

  /**
   * Get a college by ID
   * @param id College ID
   * @returns The college with the specified ID
   */
  @Get(':id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Get a college by ID',
    description: 'Returns a college by ID (super admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'College ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'Returns the college',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        name: { type: 'string', example: 'Harvard University' },
        address: {
          type: 'string',
          example: '123 Main St, Cambridge, MA 02138',
        },
        city: { type: 'string', example: 'Cambridge' },
        state: { type: 'string', example: 'Massachusetts' },
        country: { type: 'string', example: 'USA' },
        postalCode: { type: 'string', example: '02138' },
        contactPhone: { type: 'string', example: '+****************' },
        contactEmail: { type: 'string', example: '<EMAIL>' },
        website: { type: 'string', example: 'https://www.harvard.edu' },
        logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
        status: { type: 'string', example: 'active' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'College not found' })
  findOne(@Param('id') id: string) {
    return this.collegesService.findOne(id);
  }

  /**
   * Update a college
   * @param id College ID
   * @param updateCollegeDto Updated college data
   * @returns The updated college
   */
  @Put(':id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Update a college',
    description: 'Updates a college by ID (super admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'College ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiBody({ type: UpdateCollegeDto })
  @ApiOkResponse({
    description: 'College updated successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        name: { type: 'string', example: 'Harvard University' },
        address: {
          type: 'string',
          example: '123 Main St, Cambridge, MA 02138',
        },
        city: { type: 'string', example: 'Cambridge' },
        state: { type: 'string', example: 'Massachusetts' },
        country: { type: 'string', example: 'USA' },
        postalCode: { type: 'string', example: '02138' },
        contactPhone: { type: 'string', example: '+****************' },
        contactEmail: { type: 'string', example: '<EMAIL>' },
        website: { type: 'string', example: 'https://www.harvard.edu' },
        logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
        status: { type: 'string', example: 'active' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Bad request - Invalid input data' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'College not found' })
  update(@Param('id') id: string, @Body() updateCollegeDto: UpdateCollegeDto) {
    return this.collegesService.update(id, updateCollegeDto);
  }

  /**
   * Delete a college
   * @param id College ID
   * @returns Success message
   */
  @Delete(':id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Delete a college',
    description: 'Deletes a college by ID (super admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'College ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'College deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'College deleted successfully' },
        id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'College not found' })
  remove(@Param('id') id: string) {
    return this.collegesService.remove(id);
  }

  /**
   * Get all registered colleges (public)
   * @returns List of colleges with only id, name, and logoUrl
   */
  @Get('public/list')
  @ApiOperation({
    summary: 'Get all registered colleges (public)',
    description:
      'Returns a list of all registered colleges with limited information (ID, name, logoUrl). This endpoint is public.',
  })
  @ApiOkResponse({
    description: 'Returns a list of colleges with limited information',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          name: { type: 'string', example: 'Harvard University' },
          logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
        },
      },
    },
  })
  findAllPublic() {
    return this.collegesService.findAllPublic();
  }
}
