/**
 * Utility functions for handling images, including base64 detection and conversion
 */

/**
 * Checks if a string is a base64 encoded image
 */
export function isBase64Image(str: string): boolean {
  if (!str || typeof str !== 'string') return false;
  
  // Check for data URL format
  const dataUrlPattern = /^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i;
  if (dataUrlPattern.test(str)) return true;
  
  // Check for raw base64 (without data URL prefix)
  // Base64 strings are typically long and contain only valid base64 characters
  const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
  return str.length > 100 && base64Pattern.test(str);
}

/**
 * Converts a base64 string to a data URL if it's not already one
 * Also handles cases where the data URL prefix is duplicated
 */
export function ensureDataUrl(base64String: string): string {
  if (!base64String) return '';

  // Handle duplicated data URL prefixes (e.g., "data:image/jpeg;base64,data:image/jpeg;base64,...")
  const duplicatedPrefixPattern = /^(data:image\/[^;]+;base64,)(data:image\/[^;]+;base64,)/;
  if (duplicatedPrefixPattern.test(base64String)) {
    // Remove the first occurrence of the duplicated prefix
    base64String = base64String.replace(duplicatedPrefixPattern, '$2');
  }

  // If it's already a data URL, return as is
  if (base64String.startsWith('data:image/')) {
    return base64String;
  }

  // If it's raw base64, add the data URL prefix
  // Default to PNG if we can't determine the format
  return `data:image/png;base64,${base64String}`;
}

/**
 * Extracts and processes images from text content
 * Returns an object with cleaned text and extracted images
 */
export function extractImagesFromText(text: string): {
  cleanText: string;
  images: Array<{ id: string; src: string; alt: string }>;
} {
  if (!text) return { cleanText: text, images: [] };

  const images: Array<{ id: string; src: string; alt: string }> = [];
  let cleanText = text;

  // First, look for markdown-style image syntax: ![alt](data:image/type;base64,...)
  const markdownImagePattern = /!\[([^\]]*)\]\(([^)]+)\)/g;
  const markdownMatches = [...text.matchAll(markdownImagePattern)];

  if (markdownMatches.length > 0) {
    markdownMatches.forEach((match, index) => {
      const fullMatch = match[0];
      const altText = match[1] || `Image ${images.length + 1}`;
      let imageSrc = match[2];

      // Handle cases where the image src might have duplicated prefixes or other issues
      // Clean up the image source before validation
      imageSrc = imageSrc.trim();

      // Check if the src contains base64 data (either with data URL or raw base64)
      const containsBase64 = imageSrc.includes('base64,') || isBase64Image(imageSrc);

      if (containsBase64) {
        const imageId = `extracted-image-markdown-${index}`;
        const validatedSrc = ensureDataUrl(imageSrc);

        images.push({
          id: imageId,
          src: validatedSrc,
          alt: altText
        });

        // Remove the markdown image syntax from the text
        cleanText = cleanText.replace(fullMatch, '');
      }
    });
  }

  // Second, look for complete data URLs (these take priority over raw base64)
  const dataUrlPattern = /data:image\/[^;]+;base64,[A-Za-z0-9+/]+=*/g;
  const dataUrlMatches = cleanText.match(dataUrlPattern); // Use cleanText to avoid already processed markdown images

  if (dataUrlMatches) {
    dataUrlMatches.forEach((match, index) => {
      if (isBase64Image(match)) {
        const imageId = `extracted-image-dataurl-${index}`;
        const imageSrc = ensureDataUrl(match);

        images.push({
          id: imageId,
          src: imageSrc,
          alt: `Extracted image ${images.length + 1}`
        });

        // Remove the complete data URL from the text
        cleanText = cleanText.replace(match, '');
      }
    });
  }

  // Finally, look for raw base64 strings (only if they're not part of already processed content)
  const rawBase64Pattern = /\b[A-Za-z0-9+/]{200,}={0,2}\b/g;
  const rawBase64Matches = cleanText.match(rawBase64Pattern); // Use cleanText to avoid already processed content

  if (rawBase64Matches) {
    rawBase64Matches.forEach((match, index) => {
      if (isBase64Image(match)) {
        const imageId = `extracted-image-raw-${index}`;
        const imageSrc = ensureDataUrl(match);

        images.push({
          id: imageId,
          src: imageSrc,
          alt: `Extracted image ${images.length + 1}`
        });

        // Remove the raw base64 string from the text
        cleanText = cleanText.replace(match, '');
      }
    });
  }

  // Clean up any extra whitespace left after removing base64 strings
  cleanText = cleanText.replace(/\s+/g, ' ').trim();

  return { cleanText, images };
}

/**
 * Component props for rendering base64 images safely
 */
export interface Base64ImageProps {
  src: string;
  alt?: string;
  className?: string;
  maxWidth?: number;
  maxHeight?: number;
}

/**
 * Validates and sanitizes base64 image source
 */
export function validateBase64ImageSrc(src: string): string | null {
  if (!src || !isBase64Image(src)) return null;
  
  try {
    const dataUrl = ensureDataUrl(src);
    // Additional validation could be added here
    return dataUrl;
  } catch (error) {
    console.warn('Invalid base64 image source:', error);
    return null;
  }
}
