"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import Image from "next/image"

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { X, Upload, Info, Loader2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { toast } from "@/components/ui/use-toast"
import { FileUploader } from "./file-uploader"
import { getSubjectsWithTopics } from "@/lib/api/subjects"
import { createQuestion, QuestionData } from "@/lib/api/questions"

// Define interfaces for API data
interface Topic {
  _id: string;
  name: string;
  description?: string;
}

interface SubjectWithTopics {
  _id: string;
  name: string;
  description?: string;
  topics: Topic[];
}

const MAX_FILE_SIZE = 50 * 1024 * 1024 // 50MB
const ACCEPTED_FILE_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/svg+xml"]

// Form schema with custom validation for options
const formSchema = z.object({
  subject: z.string().min(1, { message: "Please select a subject" }),
  topic: z.string().min(1, { message: "Please select a topic" }),
  questionText: z.string().min(5, { message: "Question must be at least 5 characters" }),
  optionA: z.string().optional(),
  optionB: z.string().optional(),
  optionC: z.string().optional(),
  optionD: z.string().optional(),
  correctAnswer: z.enum(["A", "B", "C", "D"], {
    required_error: "Please select the correct answer",
  }),
  explanation: z.string().optional(),
  difficulty: z.enum(["Easy", "Medium", "Hard"], {
    required_error: "Please select a difficulty level",
  }),
  question_options: z
      .any()
      .optional(),
})

type FormValues = z.infer<typeof formSchema>

export default function AddQuestionForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [questionImage, setQuestionImage] = useState<string | null>(null)
  const [optionImages, setOptionImages] = useState<{ [key: string]: string | null }>({
    A: null,
    B: null,
    C: null,
    D: null,
  })
  const [subjects, setSubjects] = useState<SubjectWithTopics[]>([])
  const [topics, setTopics] = useState<Topic[]>([])
  const [loading, setLoading] = useState(true)
  const [optionValidationErrors, setOptionValidationErrors] = useState<{[key: string]: boolean}>({
    A: false,
    B: false,
    C: false,
    D: false
  })

  // Fetch subjects and topics from API
  useEffect(() => {
    const fetchSubjectsAndTopics = async () => {
      try {
        setLoading(true)
        const data = await getSubjectsWithTopics()
        setSubjects(data)
      } catch (error: any) {
        console.error("Error fetching subjects and topics:", error)
        toast({
          title: "Error",
          description: error.message || "Failed to load subjects and topics",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchSubjectsAndTopics()
  }, [])

  // Refs for file inputs
  const questionImageRef = useRef<HTMLInputElement>(null)
  const optionImageRefs = {
    A: useRef<HTMLInputElement>(null),
    B: useRef<HTMLInputElement>(null),
    C: useRef<HTMLInputElement>(null),
    D: useRef<HTMLInputElement>(null),
  }

  // Initialize form with empty string values instead of undefined
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subject: "",
      topic: "",
      questionText: "",
      optionA: "",
      optionB: "",
      optionC: "",
      optionD: "",
      correctAnswer: "" as any, // Use empty string instead of undefined
      explanation: "",
      difficulty: "" as any, // Use empty string instead of undefined
      question_options: null,
    },
  })

  // Handle subject change to update topics
  const handleSubjectChange = (value: string) => {
    form.setValue("subject", value)
    form.setValue("topic", "")
    
    // Find the selected subject and set its topics
    const selectedSubject = subjects.find(subject => subject._id === value)
    if (selectedSubject) {
      setTopics(selectedSubject.topics || [])
    } else {
      setTopics([])
    }
  }

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>, type: string) => {
    const file = e.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (event) => {
      if (type === "question") {
        setQuestionImage(event.target?.result as string)
      } else {
        setOptionImages((prev) => ({
          ...prev,
          [type]: event.target?.result as string,
        }))
        // Clear validation error for this option
        setOptionValidationErrors(prev => ({
          ...prev,
          [type]: false
        }))
      }
    }
    reader.readAsDataURL(file)
  }

  // Remove image
  const removeImage = (type: string) => {
    if (type === "question") {
      setQuestionImage(null)
      if (questionImageRef.current) {
        questionImageRef.current.value = ""
      }
    } else {
      setOptionImages((prev) => ({
        ...prev,
        [type]: null,
      }))
      if (optionImageRefs[type as keyof typeof optionImageRefs]?.current) {
        optionImageRefs[type as keyof typeof optionImageRefs].current!.value = ""
      }
      // Clear form validation error for this option if it exists
      form.clearErrors(`option${type}` as keyof FormValues);
      // Update validation state
      setOptionValidationErrors(prev => ({
        ...prev,
        [type]: false
      }));
    }
  }



  // Custom validation function
  const validateOptions = (formData: FormValues) => {
    const errors: string[] = [];
    const validationState: {[key: string]: boolean} = {};
    const options = ['A', 'B', 'C', 'D'];

    for (const option of options) {
      const hasText = formData[`option${option}` as keyof FormValues] &&
                     (formData[`option${option}` as keyof FormValues] as string).trim() !== '';
      const hasImage = optionImages[option] !== null;

      if (!hasText && !hasImage) {
        errors.push(`Option ${option} must have either text or an image`);
        validationState[option] = true;
      } else {
        validationState[option] = false;
      }
    }

    // Update validation state
    setOptionValidationErrors(validationState);

    return errors;
  };

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);

    try {
      console.log("Form data:", data);

      // Validate that each option has either text or image
      const validationErrors = validateOptions(data);
      if (validationErrors.length > 0) {
        toast({
          title: "Validation Error",
          description: validationErrors.join(', '),
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      // Convert option data to array format expected by API
      // Use image base64 if no text, otherwise use text
      const options = [
        data.optionA?.trim() || optionImages.A || '',
        data.optionB?.trim() || optionImages.B || '',
        data.optionC?.trim() || optionImages.C || '',
        data.optionD?.trim() || optionImages.D || ''
      ];

      // Map correctAnswer (A, B, C, D) to the actual option value
      const answerMap: { [key: string]: number } = { A: 0, B: 1, C: 2, D: 3 };
      const answerIndex = answerMap[data.correctAnswer];
      const answer = options[answerIndex];
      
      // Convert difficulty to lowercase to match API expectations
      const difficulty = data.difficulty.toLowerCase() as 'easy' | 'medium' | 'hard';
      
      // Get user ID from localStorage if available
      const userData = localStorage.getItem("userData");
      let userId;
      try {
        if (userData) {
          const parsed = JSON.parse(userData);
          userId = parsed._id || parsed.id;
          console.log("User data from localStorage:", parsed);
        }
      } catch (e) {
        console.error("Error parsing user data:", e);
      }
      
      // Create base question data
      const baseQuestionData: QuestionData = {
        content: data.questionText,
        options,
        answer,
        subjectId: data.subject,
        topicId: data.topic,
        difficulty,
        type: "multiple-choice"
      };
      
      // Only add createdBy if we have a valid user ID
      if (userId) {
        baseQuestionData.createdBy = userId;
      }
      
      // Only add explanation if it has a value
      const questionData = data.explanation && data.explanation.trim() !== '' 
        ? { ...baseQuestionData, explanation: data.explanation }
        : baseQuestionData;
      
      console.log("Prepared question data:", questionData);
      
      // For now, we'll convert all images to base64 and include them in the options
      // This matches the format expected by the question bank display
      console.log("Submitting question with base64 images embedded in options");

      // If question has an image, we could embed it in the question text
      // For now, we'll focus on option images being converted to base64
      let finalQuestionData = { ...questionData };

      // If question has an image, embed it in the question text as base64
      if (questionImage) {
        finalQuestionData.content = `${questionData.content}\n${questionImage}`;
      }

      // Submit to API - the options already contain base64 images where needed
      await createQuestion(finalQuestionData);
      
      // Display success toast
      toast({
        title: "Question Added",
        description: "Your question has been successfully added to the question bank.",
      });
      
      // Reset form with empty strings for radio values
      form.reset({
        subject: "",
        topic: "",
        questionText: "",
        optionA: "",
        optionB: "",
        optionC: "",
        optionD: "",
        correctAnswer: "" as any, // Use empty string instead of undefined
        explanation: "",
        difficulty: "" as any, // Use empty string instead of undefined
        question_options: null,
      });
      
      // Reset other state
      setQuestionImage(null);
      setOptionImages({
        A: null,
        B: null,
        C: null,
        D: null,
      });
      setTopics([]);
      setOptionValidationErrors({
        A: false,
        B: false,
        C: false,
        D: false
      });
      
    } catch (error: any) {
      console.error("Error adding question:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to add question. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  // Reset form
  const resetForm = () => {
    form.reset({
      subject: "",
      topic: "",
      questionText: "",
      optionA: "",
      optionB: "",
      optionC: "",
      optionD: "",
      correctAnswer: "" as any, // Use empty string instead of undefined
      explanation: "",
      difficulty: "" as any, // Use empty string instead of undefined
      question_options: null,
    });
    setQuestionImage(null);
    setOptionImages({
      A: null,
      B: null,
      C: null,
      D: null,
    });
    setTopics([]);
    setOptionValidationErrors({
      A: false,
      B: false,
      C: false,
      D: false
    });
  }

  return (
    <Card className="w-full">
      <CardContent className="pt-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Subject and Topic Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subject</FormLabel>
                    <Select onValueChange={handleSubjectChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder={loading ? "Loading subjects..." : "Select a subject"} />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {loading ? (
                          <SelectItem value="loading" disabled>
                            Loading subjects...
                          </SelectItem>
                        ) : (
                          subjects.map((subject) => (
                            <SelectItem key={subject._id} value={subject._id}>
                              {subject.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="topic"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Topic</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value} disabled={topics.length === 0}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue 
                            placeholder={
                              loading 
                                ? "Loading topics..." 
                                : topics.length > 0 
                                  ? "Select a topic" 
                                  : "Select a subject first"
                            } 
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {topics.map((topic) => (
                          <SelectItem key={topic._id} value={topic._id}>
                            {topic.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Question Input */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="questionText"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Question Text</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Enter your question here..." className="min-h-[100px]" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Question Image Upload - Not part of form validation */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Question Image (Optional)</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Upload an image to accompany your question</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <div className="flex items-center gap-4">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="h-9"
                    onClick={() => questionImageRef.current?.click()}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Image
                  </Button>
                  <input
                    type="file"
                    ref={questionImageRef}
                    className="hidden"
                    accept="image/*"
                    onChange={(e) => handleImageUpload(e, "question")}
                  />

                  {questionImage && (
                    <div className="relative">
                      <Image
                        src={questionImage || "/placeholder.svg"}
                        alt="Question image"
                        width={100}
                        height={100}
                        className="object-cover rounded-md border h-[100px] w-[100px]"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="h-6 w-6 absolute -top-2 -right-2 rounded-full"
                        onClick={() => removeImage("question")}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Answer Options */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium">Answer Options</h3>

              {["A", "B", "C", "D"].map((option) => (
                <div
                  key={option}
                  className="grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 items-start border-b pb-4 last:border-0"
                >
                  <FormField
                    control={form.control}
                    name={`option${option}` as "optionA" | "optionB" | "optionC" | "optionD"}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Option {option}
                          {optionImages[option] && (
                            <span className="text-sm text-green-600 ml-2">(Image uploaded)</span>
                          )}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={
                              optionImages[option]
                                ? `Option ${option} text (optional - image uploaded)`
                                : `Enter option ${option} text or upload an image...`
                            }
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                        {optionValidationErrors[option] && (
                          <p className="text-sm text-red-600">
                            Option {option} requires either text or an image
                          </p>
                        )}
                      </FormItem>
                    )}
                  />

                  {/* Option Image Upload - Not part of form validation */}
                  <div className="space-y-2 mt-8 md:mt-0">
                    <div className="flex items-center gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="h-9 text-xs"
                        onClick={() => optionImageRefs[option as keyof typeof optionImageRefs].current?.click()}
                      >
                        <Upload className="h-3 w-3 mr-1" />
                        Image
                      </Button>
                      <input
                        type="file"
                        ref={optionImageRefs[option as keyof typeof optionImageRefs]}
                        className="hidden"
                        accept="image/*"
                        onChange={(e) => handleImageUpload(e, option)}
                      />

                      {optionImages[option] && (
                        <div className="relative">
                          <Image
                            src={optionImages[option]! || "/placeholder.svg"}
                            alt={`Option ${option} image`}
                            width={60}
                            height={60}
                            className="object-cover rounded-md border h-[60px] w-[60px]"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="h-5 w-5 absolute -top-2 -right-2 rounded-full"
                            onClick={() => removeImage(option)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Correct Answer */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="correctAnswer"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Correct Answer</FormLabel>
                    <FormControl>
                      <RadioGroup 
                        onValueChange={field.onChange} 
                        value={field.value || ""} 
                        className="flex space-x-4"
                      >
                        {["A", "B", "C", "D"].map((option) => (
                          <FormItem key={option} className="flex items-center space-x-1">
                            <FormControl>
                              <RadioGroupItem value={option} id={`option-${option}`} />
                            </FormControl>
                            <FormLabel className="font-normal" htmlFor={`option-${option}`}>
                              {option}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="difficulty"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Difficulty Level</FormLabel>
                    <FormControl>
                      <RadioGroup 
                        onValueChange={field.onChange} 
                        value={field.value || ""} 
                        className="flex space-x-4"
                      >
                        {["Easy", "Medium", "Hard"].map((level) => (
                          <FormItem key={level} className="flex items-center space-x-1">
                            <FormControl>
                              <RadioGroupItem value={level} id={`level-${level}`} />
                            </FormControl>
                            <FormLabel className="font-normal" htmlFor={`level-${level}`}>
                              {level}
                            </FormLabel>
                          </FormItem>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Explanation */}
            <FormField
              control={form.control}
              name="explanation"
              render={({ field }) => (
                <FormItem>
                  <div className="flex items-center gap-2">
                    <FormLabel>Explanation (Optional)</FormLabel>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Provide an explanation for the correct answer</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <FormControl>
                    <Textarea
                      placeholder="Explain why the correct answer is right..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>This will be shown to students after they answer the question.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Submit and Reset Buttons */}
            
        <FormField
          control={form.control}
          name="question_options"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Upload Questions</FormLabel>
              <FormControl>
                <FileUploader
                  value={field.value}
                  onChange={(files) => {
                    field.onChange(files)
                  }}
                  maxSize={MAX_FILE_SIZE}
                  acceptedTypes={ACCEPTED_FILE_TYPES}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
            <div className="flex flex-col sm:flex-row gap-3 pt-2">
              <Button type="submit" className="w-full bg-blue-500 hover:bg-blue-600 sm:w-auto" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  "Add Question"
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                className="w-full sm:w-auto"
                onClick={resetForm}
                disabled={isSubmitting}
              >
                Reset Form
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
