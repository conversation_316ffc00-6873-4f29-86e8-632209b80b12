import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { JwtService } from '@nestjs/jwt';
import { Model } from 'mongoose';
import { AuthService } from './auth.service';
import { User } from '../schema/user.schema';
import { College } from '../schema/college.schema';
import { FirebaseAuthService } from './firebase-auth.service';
import { RegisterDto } from './dto/register.dto';
import { BadRequestException, ConflictException } from '@nestjs/common';

describe('AuthService', () => {
  let service: AuthService;
  let userModel: Model<User>;
  let collegeModel: Model<College>;
  let jwtService: JwtService;

  const mockCollege = {
    _id: 'college123',
    name: 'Test College',
    teachers: ['<EMAIL>', '<EMAIL>'],
    status: 'active',
  };

  const mockUser = {
    _id: 'user123',
    email: '<EMAIL>',
    displayName: '<PERSON>',
    role: 'teacher',
    collegeId: 'college123',
    status: 'active',
  };

  const mockUserModel = {
    findOne: jest.fn(),
    create: jest.fn(),
  };

  const mockCollegeModel = {
    findOne: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn().mockReturnValue('mock-jwt-token'),
  };

  const mockFirebaseAuthService = {
    verifyToken: jest.fn(),
    getUserByUid: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
        {
          provide: getModelToken(College.name),
          useValue: mockCollegeModel,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: FirebaseAuthService,
          useValue: mockFirebaseAuthService,
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    userModel = module.get<Model<User>>(getModelToken(User.name));
    collegeModel = module.get<Model<College>>(getModelToken(College.name));
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('register', () => {
    const registerDto: RegisterDto = {
      email: '<EMAIL>',
      displayName: 'John Doe',
      password: 'password123',
      role: 'teacher',
    };

    it('should successfully register a user when email is in college teachers array', async () => {
      // Mock college with teacher email in teachers array
      mockCollegeModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCollege),
      });

      // Mock no existing user
      mockUserModel.findOne.mockResolvedValue(null);

      // Mock user creation
      const createdUser = { ...mockUser, _id: 'new-user-id' };
      mockUserModel.create.mockResolvedValue(createdUser);

      const result = await service.register(registerDto);

      expect(mockCollegeModel.findOne).toHaveBeenCalledWith({
        teachers: { $in: ['<EMAIL>'] },
      });
      expect(mockUserModel.findOne).toHaveBeenCalledWith({
        email: '<EMAIL>',
      });
      expect(mockUserModel.create).toHaveBeenCalled();
      expect(result.accessToken).toBe('mock-jwt-token');
      expect(result.user.email).toBe('<EMAIL>');
    });

    it('should throw BadRequestException when email is not in any college teachers array', async () => {
      // Mock no college found with teacher email
      mockCollegeModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.register(registerDto)).rejects.toThrow(
        BadRequestException,
      );
      expect(mockCollegeModel.findOne).toHaveBeenCalledWith({
        teachers: { $in: ['<EMAIL>'] },
      });
    });

    it('should throw ConflictException when user already exists', async () => {
      // Mock college found
      mockCollegeModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCollege),
      });

      // Mock existing user
      mockUserModel.findOne.mockResolvedValue(mockUser);

      await expect(service.register(registerDto)).rejects.toThrow(
        ConflictException,
      );
      expect(mockUserModel.findOne).toHaveBeenCalledWith({
        email: '<EMAIL>',
      });
    });

    it('should throw BadRequestException with correct message for unauthorized email', async () => {
      const unauthorizedDto = {
        ...registerDto,
        email: '<EMAIL>',
      };

      mockCollegeModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(service.register(unauthorizedDto)).rejects.toThrow(
        new BadRequestException(
          'Email is not authorized as a teacher. Please contact your college administrator.',
        ),
      );
    });

    it('should throw ConflictException with correct message for existing user', async () => {
      mockCollegeModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCollege),
      });

      mockUserModel.findOne.mockResolvedValue(mockUser);

      await expect(service.register(registerDto)).rejects.toThrow(
        new ConflictException('User is already registered'),
      );
    });
  });
});
