<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1746761040056" clover="3.2.0">
  <project timestamp="1746761040056" name="All files">
    <metrics statements="93" coveredstatements="92" conditionals="10" coveredconditionals="10" methods="37" coveredmethods="37" elements="140" coveredelements="139" complexity="0" loc="93" ncloc="93" packages="2" files="2" classes="2"/>
    <package name="src">
      <metrics statements="27" coveredstatements="26" conditionals="10" coveredconditionals="10" methods="7" coveredmethods="7"/>
      <file name="index.jsx" path="/Users/<USER>/Development/react-katex/packages/react-katex/src/index.jsx">
        <metrics statements="27" coveredstatements="26" conditionals="10" coveredconditionals="10" methods="7" coveredmethods="7"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="30" count="2" type="stmt"/>
        <line num="36" count="4" type="stmt"/>
        <line num="37" count="32" type="cond" truecount="4" falsecount="0"/>
        <line num="39" count="32" type="stmt"/>
        <line num="40" count="32" type="stmt"/>
        <line num="41" count="32" type="stmt"/>
        <line num="47" count="22" type="stmt"/>
        <line num="49" count="10" type="cond" truecount="3" falsecount="0"/>
        <line num="50" count="10" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="57" count="32" type="cond" truecount="1" falsecount="0"/>
        <line num="58" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="61" count="22" type="stmt"/>
        <line num="64" count="4" type="stmt"/>
        <line num="71" count="4" type="stmt"/>
        <line num="74" count="2" type="stmt"/>
        <line num="78" count="2" type="stmt"/>
        <line num="79" count="12" type="stmt"/>
        <line num="82" count="2" type="stmt"/>
        <line num="84" count="2" type="stmt"/>
        <line num="85" count="12" type="stmt"/>
        <line num="88" count="2" type="stmt"/>
        <line num="90" count="2" type="stmt"/>
        <line num="91" count="2" type="stmt"/>
      </file>
    </package>
    <package name="tests">
      <metrics statements="66" coveredstatements="66" conditionals="0" coveredconditionals="0" methods="30" coveredmethods="30"/>
      <file name="sharedExamples.js" path="/Users/<USER>/Development/react-katex/packages/react-katex/tests/sharedExamples.js">
        <metrics statements="66" coveredstatements="66" conditionals="0" coveredconditionals="0" methods="30" coveredmethods="30"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="6" count="2" type="stmt"/>
        <line num="7" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="10" count="2" type="stmt"/>
        <line num="11" count="8" type="stmt"/>
        <line num="13" count="18" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="6" type="stmt"/>
        <line num="18" count="14" type="stmt"/>
        <line num="20" count="10" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="23" count="2" type="stmt"/>
        <line num="24" count="2" type="stmt"/>
        <line num="26" count="2" type="stmt"/>
        <line num="29" count="2" type="stmt"/>
        <line num="30" count="2" type="stmt"/>
        <line num="32" count="2" type="stmt"/>
        <line num="34" count="2" type="stmt"/>
        <line num="38" count="2" type="stmt"/>
        <line num="39" count="2" type="stmt"/>
        <line num="40" count="2" type="stmt"/>
        <line num="42" count="2" type="stmt"/>
        <line num="45" count="2" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="48" count="2" type="stmt"/>
        <line num="50" count="2" type="stmt"/>
        <line num="54" count="2" type="stmt"/>
        <line num="55" count="2" type="stmt"/>
        <line num="56" count="2" type="stmt"/>
        <line num="58" count="2" type="stmt"/>
        <line num="60" count="2" type="stmt"/>
        <line num="63" count="2" type="stmt"/>
        <line num="64" count="2" type="stmt"/>
        <line num="66" count="2" type="stmt"/>
        <line num="68" count="2" type="stmt"/>
        <line num="71" count="2" type="stmt"/>
        <line num="72" count="2" type="stmt"/>
        <line num="73" count="2" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="78" count="2" type="stmt"/>
        <line num="79" count="2" type="stmt"/>
        <line num="80" count="2" type="stmt"/>
        <line num="82" count="2" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="87" count="2" type="stmt"/>
        <line num="89" count="2" type="stmt"/>
        <line num="92" count="2" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="95" count="2" type="stmt"/>
        <line num="99" count="2" type="stmt"/>
        <line num="100" count="2" type="stmt"/>
        <line num="101" count="2" type="stmt"/>
        <line num="103" count="2" type="stmt"/>
        <line num="108" count="2" type="stmt"/>
        <line num="109" count="2" type="stmt"/>
        <line num="110" count="2" type="stmt"/>
        <line num="112" count="2" type="stmt"/>
        <line num="115" count="2" type="stmt"/>
        <line num="116" count="2" type="stmt"/>
        <line num="117" count="2" type="stmt"/>
        <line num="119" count="2" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
