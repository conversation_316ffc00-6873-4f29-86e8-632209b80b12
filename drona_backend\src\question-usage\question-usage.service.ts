import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  QuestionUsage,
  QuestionUsageDocument,
} from '../schema/question-usage.schema';

/**
 * Internal service for tracking question usage by colleges.
 * This service is used internally by QuestionPapersService to prevent
 * question reuse within the same college during question paper generation.
 *
 * No API endpoints are exposed - all functionality is handled internally.
 */
@Injectable()
export class QuestionUsageService {
  private readonly logger = new Logger(QuestionUsageService.name);

  constructor(
    @InjectModel(QuestionUsage.name)
    private questionUsageModel: Model<QuestionUsageDocument>,
  ) {}

  /**
   * Record that a question has been used by a college
   */
  async recordQuestionUsage(data: {
    collegeId: string;
    questionId: string;
    questionPaperId: string;
    usedBy: string;
    subjectId: string;
    topicId?: string;
    metadata?: any;
  }): Promise<QuestionUsage | null> {
    try {
      const usageRecord = new this.questionUsageModel({
        ...data,
        usedAt: new Date(),
        status: 'active',
      });

      return await usageRecord.save();
    } catch (error) {
      // Handle duplicate key error (question already used by this college)
      if (error.code === 11000) {
        this.logger.warn(
          `Question ${data.questionId} already used by college ${data.collegeId}`,
        );
        return null;
      }
      throw error;
    }
  }

  /**
   * Record multiple questions as used
   */
  async recordMultipleQuestionUsage(
    usageData: Array<{
      collegeId: string;
      questionId: string;
      questionPaperId: string;
      usedBy: string;
      subjectId: string;
      topicId?: string;
      metadata?: any;
    }>,
  ): Promise<{ recorded: number; skipped: number }> {
    let recorded = 0;
    let skipped = 0;

    for (const data of usageData) {
      const result = await this.recordQuestionUsage(data);
      if (result !== null) {
        recorded++;
      } else {
        skipped++;
      }
    }

    return { recorded, skipped };
  }

  /**
   * Get list of question IDs that have been used by a college
   */
  async getUsedQuestionIds(
    collegeId: string,
    filters?: {
      subjectId?: string;
      topicId?: string;
      status?: string;
    },
  ): Promise<string[]> {
    const query: any = {
      collegeId,
      status: filters?.status || 'active',
    };

    if (filters?.subjectId) {
      query.subjectId = filters.subjectId;
    }

    if (filters?.topicId) {
      query.topicId = filters.topicId;
    }

    const usageRecords = await this.questionUsageModel
      .find(query)
      .select('questionId')
      .lean()
      .exec();

    return usageRecords.map((record) => record.questionId.toString());
  }

  /**
   * Get questions that haven't been used by a college
   */
  async getUnusedQuestions(
    collegeId: string,
    availableQuestionIds: string[],
    filters?: {
      subjectId?: string;
      topicId?: string;
    },
  ): Promise<string[]> {
    const usedQuestionIds = await this.getUsedQuestionIds(collegeId, filters);

    return availableQuestionIds.filter(
      (questionId) => !usedQuestionIds.includes(questionId),
    );
  }
}
