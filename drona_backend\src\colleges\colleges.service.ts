import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { College } from '../schema/college.schema';
import { CreateCollegeDto } from './dto/create-college.dto';
import { UpdateCollegeDto } from './dto/update-college.dto';
import { AddAdminDto } from './dto/add-admin.dto';

@Injectable()
export class CollegesService {
  constructor(
    @InjectModel(College.name) private collegeModel: Model<College>,
  ) {}

  async create(createCollegeDto: CreateCollegeDto): Promise<College> {
    // Handle backward compatibility for renamed fields
    const collegeData = {
      ...createCollegeDto,
      // Map legacy field names if they exist in the DTO
      contactPhone:
        createCollegeDto.contactPhone || (createCollegeDto as any).contact,
      contactEmail:
        createCollegeDto.contactEmail || (createCollegeDto as any).email,
      logoUrl: createCollegeDto.logoUrl || (createCollegeDto as any).logoURL,
    };

    // Remove legacy field names if they exist
    if ((collegeData as any).contact) delete (collegeData as any).contact;
    if ((collegeData as any).email) delete (collegeData as any).email;
    if ((collegeData as any).logoURL) delete (collegeData as any).logoURL;

    // Initialize admins array and automatically add contactEmail if provided
    const adminsArray = createCollegeDto.admins ? [...createCollegeDto.admins] : [];

    // Add contactEmail to admins array if it exists and is not already included
    if (collegeData.contactEmail && !adminsArray.includes(collegeData.contactEmail)) {
      adminsArray.push(collegeData.contactEmail);
    }

    // Set the admins array
    collegeData.admins = adminsArray;

    const createdCollege = new this.collegeModel(collegeData);
    return createdCollege.save();
  }

  async findAll(): Promise<College[]> {
    return this.collegeModel.find().exec();
  }

  async findOne(id: string): Promise<College> {
    const college = await this.collegeModel.findById(id).exec();
    if (!college) {
      throw new NotFoundException(`College with ID ${id} not found`);
    }
    return college;
  }

  async update(
    id: string,
    updateCollegeDto: UpdateCollegeDto,
  ): Promise<College> {
    // Handle backward compatibility for renamed fields
    const collegeData = {
      ...updateCollegeDto,
      // Map legacy field names if they exist in the DTO
      contactPhone:
        updateCollegeDto.contactPhone || (updateCollegeDto as any).contact,
      contactEmail:
        updateCollegeDto.contactEmail || (updateCollegeDto as any).email,
      logoUrl: updateCollegeDto.logoUrl || (updateCollegeDto as any).logoURL,
    };

    // Remove legacy field names if they exist
    if ((collegeData as any).contact) delete (collegeData as any).contact;
    if ((collegeData as any).email) delete (collegeData as any).email;
    if ((collegeData as any).logoURL) delete (collegeData as any).logoURL;

    const updatedCollege = await this.collegeModel
      .findByIdAndUpdate(id, collegeData, { new: true })
      .exec();
    if (!updatedCollege) {
      throw new NotFoundException(`College with ID ${id} not found`);
    }
    return updatedCollege;
  }

  async remove(id: string): Promise<College> {
    const deletedCollege = await this.collegeModel.findByIdAndDelete(id).exec();
    if (!deletedCollege) {
      throw new NotFoundException(`College with ID ${id} not found`);
    }
    return deletedCollege;
  }

  async findAllPublic(): Promise<
    Array<{ _id: string; name: string; logoUrl: string }>
  > {
    // The `collegeId` is typically the `_id` in MongoDB.
    // If your schema uses a different field for `collegeId` that you want to return,
    // adjust the projection accordingly e.g. { name: 1, logoUrl: 1, collegeId: 1, _id: 0 }
    const colleges = await this.collegeModel
      .find({}, { name: 1, logoUrl: 1 })
      .exec();
    return colleges.map((college) => ({
      _id: college._id.toString(),
      name: college.name,
      logoUrl: college.logoUrl,
    }));
  }
}
