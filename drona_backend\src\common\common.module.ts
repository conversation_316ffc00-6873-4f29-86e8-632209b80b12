import { Module, Global } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AnalyticsCacheService, TrackingService } from './services';
import { Download, DownloadSchema } from '../schema/download.schema';
import {
  UserActivity,
  UserActivitySchema,
} from '../schema/user-activity.schema';
import {
  QuestionPaper,
  QuestionPaperSchema,
} from '../schema/question-paper.schema';

@Global()
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Download.name, schema: DownloadSchema },
      { name: UserActivity.name, schema: UserActivitySchema },
      { name: QuestionPaper.name, schema: QuestionPaperSchema },
    ]),
  ],
  providers: [AnalyticsCacheService, TrackingService],
  exports: [AnalyticsCacheService, TrackingService],
})
export class CommonModule {}
