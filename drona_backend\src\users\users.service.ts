import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User, UserDocument } from '../schema/user.schema';
import { AssignRoleDto } from './dto/assign-role.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private readonly userModel: Model<UserDocument>,
  ) {}

  async findOne(id: string): Promise<UserDocument> {
    console.log('id', id);
    const user = await this.userModel
      .findById(id)
      .select('-password -firebaseUid -__v')
      .populate('collegeId') // Populate the college information
      .exec();

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findAll(): Promise<UserDocument[]> {
    return this.userModel
      .find()
      .select('-password -firebaseUid -__v')
      .populate('collegeId') // Populate the college information
      .exec();
  }

  async findByCollege(collegeId: string): Promise<UserDocument[]> {
    return this.userModel
      .find({ collegeId })
      .select('-password -firebaseUid -__v')
      .populate('collegeId') // Populate the college information
      .exec();
  }

  async assignRole(
    id: string,
    assignRoleDto: AssignRoleDto,
  ): Promise<UserDocument> {
    const user = await this.userModel.findById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    user.role = assignRoleDto.role;
    const savedUser = await user.save();

    // Populate college information
    await savedUser.populate('collegeId');

    const userObject: any = savedUser.toObject();

    // Delete properties
    delete userObject.password;
    delete userObject.firebaseUid;
    delete userObject.__v;

    return userObject as UserDocument;
  }

  async create(userData: Partial<User>): Promise<UserDocument> {
    const user = new this.userModel(userData);
    const savedUser = await user.save();

    // Populate college information
    await savedUser.populate('collegeId');

    const userObject: any = savedUser.toObject();

    // Delete properties
    delete userObject.password;
    delete userObject.firebaseUid;
    delete userObject.__v;

    return userObject as UserDocument;
  }

  /**
   * Update a user's collegeId
   * @param userId The user ID
   * @param collegeId The college ID to set
   * @returns The updated user
   */
  async updateCollegeId(
    userId: string,
    collegeId: string,
  ): Promise<UserDocument> {
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Use updateOne to avoid type issues
    await this.userModel.updateOne(
      { _id: userId },
      { $set: { collegeId: collegeId } },
    );

    // Retrieve the updated user
    const updatedUser = await this.userModel
      .findById(userId)
      .select('-password -firebaseUid -__v')
      .populate('collegeId')
      .exec();

    if (!updatedUser) {
      throw new NotFoundException('User not found after update');
    }

    return updatedUser;
  }
}
