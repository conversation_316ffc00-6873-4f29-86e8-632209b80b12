import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { College } from './college.schema';
import { Question } from './question.schema';
import { User } from './user.schema';
import { QuestionPaper } from './question-paper.schema';

export type QuestionUsageDocument = QuestionUsage & Document;

/**
 * Schema to track which questions have been used by which college
 * This ensures questions are not reused within the same college
 */
@Schema({ timestamps: true })
export class QuestionUsage {
  @ApiProperty({
    description: 'College ID that used the question',
    example: '60d21b4667d0d8992e610c85',
    type: String,
    required: true,
  })
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true,
    ref: 'College',
    index: true,
  })
  collegeId: MongooseSchema.Types.ObjectId;

  @ApiProperty({
    description: 'Question ID that was used',
    example: '60d21b4667d0d8992e610c86',
    type: String,
    required: true,
  })
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true,
    ref: 'Question',
    index: true,
  })
  questionId: MongooseSchema.Types.ObjectId;

  @ApiProperty({
    description: 'Question Paper ID where the question was used',
    example: '60d21b4667d0d8992e610c87',
    type: String,
    required: true,
  })
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true,
    ref: 'QuestionPaper',
  })
  questionPaperId: MongooseSchema.Types.ObjectId;

  @ApiProperty({
    description: 'User ID who generated the question paper',
    example: '60d21b4667d0d8992e610c88',
    type: String,
    required: true,
  })
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true,
    ref: 'User',
    index: true,
  })
  usedBy: MongooseSchema.Types.ObjectId;

  @ApiProperty({
    description: 'Subject ID of the question',
    example: '60d21b4667d0d8992e610c89',
    type: String,
    required: true,
  })
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    required: true,
    ref: 'Subject',
    index: true,
  })
  subjectId: MongooseSchema.Types.ObjectId;

  @ApiPropertyOptional({
    description: 'Topic ID of the question (optional)',
    example: '60d21b4667d0d8992e610c90',
    type: String,
  })
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Topic', index: true })
  topicId?: MongooseSchema.Types.ObjectId;

  @ApiProperty({
    description: 'Date when the question was first used',
    example: '2024-01-15T10:30:00.000Z',
  })
  @Prop({ required: true, index: true })
  usedAt: Date;

  @ApiPropertyOptional({
    description: 'Additional metadata about the usage',
    example: { examType: 'NEET', difficulty: 'medium', section: 'Physics' },
  })
  @Prop({ type: Object })
  metadata?: {
    examType?: string;
    difficulty?: string;
    section?: string;
    marks?: number;
  };

  @ApiProperty({
    description: 'Status of the usage record',
    example: 'active',
    enum: ['active', 'reset'],
    default: 'active',
  })
  @Prop({ enum: ['active', 'reset'], default: 'active', index: true })
  status: string;
}

export const QuestionUsageSchema = SchemaFactory.createForClass(QuestionUsage);

// Create compound indexes for optimal query performance
QuestionUsageSchema.index({ collegeId: 1, questionId: 1 }, { unique: true });
QuestionUsageSchema.index({ collegeId: 1, subjectId: 1 });
QuestionUsageSchema.index({ collegeId: 1, topicId: 1 });
QuestionUsageSchema.index({ collegeId: 1, usedAt: -1 });
QuestionUsageSchema.index({ questionPaperId: 1 });
QuestionUsageSchema.index({ usedBy: 1, usedAt: -1 });
