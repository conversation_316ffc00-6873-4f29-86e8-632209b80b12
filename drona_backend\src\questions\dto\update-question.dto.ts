import { PartialType } from '@nestjs/swagger';
import { CreateQuestionDto } from './create-question.dto';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for updating a question
 * All fields are optional
 */
export class UpdateQuestionDto extends PartialType(CreateQuestionDto) {
  @ApiProperty({
    description: 'Question ID to update',
    example: '60d21b4667d0d8992e610c85',
    required: false,
  })
  id?: string;
}
