import {
  Is<PERSON><PERSON>,
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  IsMongoId,
  IsOption<PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO for setting question paper generation and download limits
 */
export class SetQuestionLimitDto {
  @ApiProperty({
    description: 'College ID to set the limit for',
    example: '60d21b4667d0d8992e610c91',
  })
  @IsMongoId()
  @IsString()
  @IsNotEmpty()
  collegeId: string;

  @ApiPropertyOptional({
    description:
      'Subject ID to set the limit for (optional for global college limits)',
    example: '60d21b4667d0d8992e610c86',
  })
  @IsMongoId()
  @IsString()
  @IsOptional()
  subjectId?: string;

  @ApiPropertyOptional({
    description: 'Maximum number of question papers that can be generated',
    example: 50,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  maxGeneration?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of question papers that can be downloaded',
    example: 100,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  maxDownloads?: number;

  // Keeping backward compatibility
  @ApiPropertyOptional({
    description:
      'Maximum number of questions allowed in a paper (deprecated, use maxGeneration)',
    example: 50,
    minimum: 1,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  maxQuestions?: number;
}
