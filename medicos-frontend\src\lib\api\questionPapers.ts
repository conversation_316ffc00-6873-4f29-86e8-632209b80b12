const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';

/**
 * Interface for creating a question paper
 */
export interface CreateQuestionPaperDto {
  title: string;
  description?: string;
  maxQuestions?: number;
  subject: string;
  topicId?: string;
  totalMarks: number;
  duration: number;
  instructions?: string;
  examType: string;
  customise?: {
    customDifficulty: {
      easyPercentage: number;
      mediumPercentage: number;
      hardPercentage: number;
    };
    numberOfQuestions: number;
    totalMarks: number;
    duration: number;
    includeAnswers: boolean;
  };
}

/**
 * Interface for question paper response
 */
export interface QuestionPaperResponse {
  _id: string;
  title: string;
  description?: string;
  subjectId: string;
  topicId?: string;
  totalMarks: number;
  duration: number;
  withAnswers: boolean;
  instructions?: string;
  examType: string;
  difficultyMode: string;
  questions: Array<{
    _id: string;
    content: string;
    options: string[];
    answer: string;
    difficulty: string;
    type: string;
    marks: number;
  }>;
  generatedBy: string;
  collegeId?: string;
  status: string;
  sections: Array<{
    name: string;
    description: string;
    order: number;
    sectionMarks: number;
    questions: Array<{
      question: any;
      order: number;
    }>;
  }>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Get authentication headers with proper token
 */
function getAuthHeaders(): Record<string, string> {
  // Try different token storage keys used in the codebase
  const backendToken = localStorage.getItem("backendToken");
  const firebaseToken = localStorage.getItem("firebaseToken");
  const token = localStorage.getItem("token");

  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };

  // Prefer backend token, then firebase token, then generic token
  if (backendToken) {
    headers["Authorization"] = `Bearer ${backendToken}`;
  } else if (firebaseToken) {
    headers["Authorization"] = `Bearer ${firebaseToken}`;
  } else if (token) {
    headers["Authorization"] = `Bearer ${token}`;
  } else {
    throw new Error("Authentication required - Please log in again. No valid authentication token found.");
  }

  return headers;
}

/**
 * Create a new question paper
 * @param questionPaperData The question paper data
 * @returns The created question paper or error object
 */
export async function createQuestionPaper(questionPaperData: CreateQuestionPaperDto): Promise<{success: true, data: QuestionPaperResponse} | {success: false, error: string}> {
  try {
    const headers = getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/question-papers`, {
      method: "POST",
      headers,
      body: JSON.stringify(questionPaperData)
    });

    if (!response.ok) {
      let errorMessage = `Error: ${response.status} - ${response.statusText}`;

      try {
        // Try to get error message from response body
        const errorText = await response.text();

        if (errorText) {
          try {
            // Try to parse as JSON first
            const errorData = JSON.parse(errorText);

            // Extract the message from the parsed JSON
            if (errorData && errorData.message) {
              errorMessage = errorData.message;
            } else if (errorData && errorData.error) {
              errorMessage = errorData.error;
            } else {
              errorMessage = errorText;
            }
          } catch (jsonError) {
            // If not JSON, use the text directly
            errorMessage = errorText;
          }
        }
      } catch (parseError) {
        // Silently handle parse errors
      }

      // Provide more specific error messages based on status code if we don't have a message
      if (!errorMessage || errorMessage === `Error: ${response.status} - ${response.statusText}`) {
        switch (response.status) {
          case 401:
            errorMessage = "Authentication required - Please log in again.";
            break;
          case 403:
            errorMessage = "Access denied - You don't have permission to perform this action.";
            break;
          case 404:
            errorMessage = "Resource not found - The requested item could not be found.";
            break;
          case 429:
            errorMessage = "Too many requests - Please wait a moment before trying again.";
            break;
          case 500:
            errorMessage = "Server error - Please try again later.";
            break;
          case 503:
            errorMessage = "Service unavailable - The server is temporarily down.";
            break;
          default:
            if (response.status >= 400 && response.status < 500) {
              errorMessage = "Invalid request - Please check your input and try again.";
            } else if (response.status >= 500) {
              errorMessage = "Server error - Please try again later.";
            }
        }
      }

      return { success: false, error: errorMessage };
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: "Network error - Please check your connection and try again." };
  }
}

/**
 * Download a question paper as PDF
 * @param questionPaperId The question paper ID
 * @param format The format (pdf or docx)
 * @returns The file blob
 */
export async function downloadQuestionPaper(questionPaperId: string, format: 'pdf' | 'docx' = 'pdf'): Promise<Blob> {
  try {
    const headers = getAuthHeaders();
    delete headers["Content-Type"]; // Remove content-type for blob response

    const response = await fetch(`${API_BASE_URL}/question-papers/${questionPaperId}/download?format=${format}`, {
      method: "GET",
      headers
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      let errorMessage = errorData.message || `Error: ${response.status} - ${response.statusText}`;

      if (response.status === 401) {
        errorMessage = "Authentication required - Please log in again.";
      } else if (response.status === 404) {
        errorMessage = "Question paper not found.";
      } else if (response.status >= 500) {
        errorMessage = "Server error - Please try again later.";
      }

      throw new Error(errorMessage);
    }

    return await response.blob();
  } catch (error) {
    console.error("Error downloading question paper:", error);
    throw error;
  }
}

/**
 * Get all question papers
 * @returns List of question papers
 */
export async function getQuestionPapers(): Promise<QuestionPaperResponse[]> {
  try {
    const headers = getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/question-papers`, {
      method: "GET",
      headers
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status} - ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching question papers:", error);
    throw error;
  }
}

/**
 * Get a specific question paper by ID
 * @param questionPaperId The question paper ID
 * @returns The question paper
 */
export async function getQuestionPaper(questionPaperId: string): Promise<QuestionPaperResponse> {
  try {
    const headers = getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/question-papers/${questionPaperId}`, {
      method: "GET",
      headers
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status} - ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching question paper:", error);
    throw error;
  }
}
