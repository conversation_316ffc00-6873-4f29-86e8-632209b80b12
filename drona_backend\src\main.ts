import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationPipe, Logger } from '@nestjs/common';
import { AllExceptionsFilter } from './common/filters';
import { ConfigService } from '@nestjs/config';
import { addSchemaIndexes } from './common/indexes/schema-indexes';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as path from 'path';
import * as express from 'express';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  // Apply MongoDB indexes
  try {
    logger.log('Applying MongoDB indexes...');
    addSchemaIndexes();
    logger.log('MongoDB indexes applied successfully');
  } catch (error) {
    logger.error(
      `Failed to apply MongoDB indexes: ${error.message}`,
      error.stack,
    );
  }

  // Check if Firebase service account file exists
  const firebaseServiceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
  if (firebaseServiceAccountPath) {
    const fullPath = firebaseServiceAccountPath.startsWith('/')
      ? firebaseServiceAccountPath
      : path.join(process.cwd(), firebaseServiceAccountPath);

    try {
      const fs = require('fs');
      if (fs.existsSync(fullPath)) {
        logger.log(`Firebase service account file found at: ${fullPath}`);
      } else {
        logger.warn(`Firebase service account file not found at: ${fullPath}`);
        logger.warn('Firebase authentication may not work correctly.');
      }
    } catch (error) {
      logger.error(
        `Error checking Firebase service account file: ${error.message}`,
        error.stack,
      );
    }
  }

  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const configService = app.get(ConfigService);

  // Configure Express body parser limits for large base64 images
  const maxJsonSize = configService.get<string>('MAX_JSON_SIZE') || '50mb';
  const maxUrlEncodedSize = configService.get<string>('MAX_URL_ENCODED_SIZE') || '50mb';

  app.use(express.json({ limit: maxJsonSize }));
  app.use(express.urlencoded({ limit: maxUrlEncodedSize, extended: true }));

  // Serve static files for uploaded images
  app.useStaticAssets(path.join(process.cwd(), 'uploads'), {
    prefix: '/uploads/',
  });

  // Apply global pipes and filters
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  app.useGlobalFilters(new AllExceptionsFilter());

  // Enable CORS if configured
  if (configService.get<boolean>('ENABLE_CORS')) {
    const allowedOrigins = configService.get<string>('ALLOWED_ORIGINS')?.split(',') || ['http://localhost:3001'];
    app.enableCors({
      origin: allowedOrigins,
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      credentials: true,
      allowedHeaders: 'Content-Type, Accept, Authorization',
    });
    logger.log(`CORS enabled for origins: ${allowedOrigins.join(', ')}`);
  }

  // Enhanced Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('Drona Backend API')
    .setDescription(
      'The Drona Backend API documentation for question paper generation and management',
    )
    .setVersion('1.0')
    .addServer('http://localhost:3000/api', 'Local Development')
    .addServer('https://api.drona.example.com/api', 'Production')
    .addTag('Authentication', 'User authentication and registration endpoints')
    .addTag('Users', 'User management endpoints')
    .addTag('Colleges', 'College management endpoints')
    .addTag('Teachers', 'Teacher management endpoints')
    .addTag('Questions', 'Question management endpoints with image compression')
    .addTag(
      'Question Papers',
      'Question paper generation and management endpoints',
    )
    .addTag('Analytics - College', 'College-level analytics endpoints')
    .addTag('Analytics - Super Admin', 'Platform-wide analytics endpoints')
    .addTag('App', 'Application health and status endpoints')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth', // This name is used in @ApiBearerAuth() decorator
    )
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    deepScanRoutes: true,
    extraModels: [],
  });

  SwaggerModule.setup('api/docs', app, document, {
    explorer: true,
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: 'none',
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
    },
  });

  // Set global prefix if configured
  const apiPrefix = configService.get<string>('API_PREFIX');
  if (apiPrefix) {
    app.setGlobalPrefix(apiPrefix);
  }

  await app.listen(configService.get<number>('PORT') || 3000);
}
bootstrap();
