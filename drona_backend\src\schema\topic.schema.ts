// topic.schema.ts
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Subject } from './subject.schema';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export type TopicDocument = Topic & Document;

@Schema()
export class Topic {
  @ApiProperty({
    description: 'Topic name',
    example: 'Calculus',
    required: true,
  })
  @Prop({ required: true })
  name: string;

  @ApiProperty({
    description: 'Subject ID this topic belongs to',
    example: '60d21b4667d0d8992e610c85',
    type: String,
    required: true,
  })
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Subject', required: true })
  subjectId: Subject;

  @ApiPropertyOptional({
    description: 'Topic description',
    example:
      'The branch of mathematics that deals with limits and the differentiation and integration of functions',
  })
  @Prop()
  description: string;
}

export const TopicSchema = SchemaFactory.createForClass(Topic);
