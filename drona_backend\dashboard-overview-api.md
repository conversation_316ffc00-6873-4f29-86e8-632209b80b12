# Super Admin Dashboard Overview API

## Overview
Complete implementation of Super Admin Dashboard Overview API requirements including usage trends (bar chart) and college growth data (line chart) with target comparisons and multiple data views.

## Endpoints

### 1. Usage Trends (Bar Chart) - `GET /api/analytics/usage-trends`

**Authorization:** <PERSON><PERSON> (Super Admin only)

**Description:** Returns monthly usage data for questions created and papers generated, perfect for bar chart visualization.

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `year` | number | No | Current year | Year to filter by (e.g., 2024) |
| `startDate` | string (ISO) | No | - | Start date for custom range |
| `endDate` | string (ISO) | No | - | End date for custom range |

#### Example Requests

```bash
# Get current year usage trends
GET /api/analytics/usage-trends
Authorization: Bearer <super_admin_token>

# Get specific year
GET /api/analytics/usage-trends?year=2024
Authorization: Bearer <super_admin_token>

# Custom date range (e.g., Feb 5 - March 6)
GET /api/analytics/usage-trends?startDate=2024-02-05T00:00:00.000Z&endDate=2024-03-06T23:59:59.999Z
Authorization: Bearer <super_admin_token>
```

#### Response Format

```json
{
  "data": [
    {
      "month": "2024-01",
      "monthName": "January 2024",
      "questionsCreated": 1250,
      "papersGenerated": 340,
      "totalUsage": 1590
    },
    {
      "month": "2024-02",
      "monthName": "February 2024",
      "questionsCreated": 1180,
      "papersGenerated": 385,
      "totalUsage": 1565
    }
  ],
  "summary": {
    "totalMonths": 12,
    "totalQuestionsCreated": 15000,
    "totalPapersGenerated": 4080,
    "averageMonthlyQuestions": 1250,
    "averageMonthlyPapers": 340
  },
  "dateRange": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T23:59:59.999Z"
  }
}
```

### 2. College Growth (Line Chart) - `GET /api/analytics/college-growth`

**Authorization:** Bearer Token (Super Admin only)

**Description:** Returns monthly college growth data with target comparisons and multiple data views (Overview, Sales, Revenue).

#### Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `year` | number | No | Current year | Year to filter by (e.g., 2024) |
| `startDate` | string (ISO) | No | - | Start date for custom range |
| `endDate` | string (ISO) | No | - | End date for custom range |
| `view` | string | No | 'overview' | Data view: `overview`, `sales`, `revenue` |

#### Example Requests

```bash
# Get current year overview
GET /api/analytics/college-growth
Authorization: Bearer <super_admin_token>

# Get sales view for specific year
GET /api/analytics/college-growth?year=2024&view=sales
Authorization: Bearer <super_admin_token>

# Custom date range with revenue view
GET /api/analytics/college-growth?startDate=2024-02-05T00:00:00.000Z&endDate=2024-03-06T23:59:59.999Z&view=revenue
Authorization: Bearer <super_admin_token>
```

#### Response Format

```json
{
  "data": [
    {
      "month": "2024-01",
      "monthName": "January 2024",
      "collegesAdded": 5,
      "cumulativeColleges": 25,
      "monthlyTarget": 6,
      "targetAchievement": 83.33,
      "revenue": 25000,
      "salesMetrics": {
        "conversions": 5,
        "leads": 12,
        "conversionRate": 41.67
      }
    }
  ],
  "summary": {
    "totalMonths": 12,
    "totalCollegesAdded": 60,
    "averageMonthlyGrowth": 5,
    "totalTargetAchievement": 85.5,
    "totalRevenue": 300000
  },
  "targets": {
    "monthlyTarget": 6,
    "yearlyTarget": 72,
    "currentProgress": 83.33
  },
  "dateRange": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T23:59:59.999Z"
  }
}
```

## Key Features

### ✅ **Complete Requirements Coverage**

#### **Usage Trends (Bar Chart)**
- ✅ Monthly usage data (questions created + papers generated per month)
- ✅ Year filtering support
- ✅ Custom date range support
- ✅ Chart-ready data format

#### **College Growth (Line Chart)**
- ✅ Monthly college growth data (colleges added per month)
- ✅ Target comparison with achievement percentages
- ✅ Multiple data views (Overview, Sales, Revenue)
- ✅ Custom date range support (e.g., 05 Feb – 06 March)
- ✅ Cumulative college count tracking

### **📊 Data Views**

#### **Overview View** (Default)
- Basic college growth metrics
- Standard revenue calculations
- Target achievement tracking

#### **Sales View**
- Enhanced sales metrics (leads, conversions, conversion rates)
- Sales-focused revenue calculations
- Higher conversion rate assumptions

#### **Revenue View**
- Premium revenue calculations
- Revenue-focused metrics
- Enhanced pricing models

### **🎯 Target Management**
- Monthly targets (configurable, default: 6 colleges/month)
- Yearly targets (calculated automatically)
- Achievement percentage calculations
- Progress tracking against targets

### **⚡ Performance Features**
- Cached results for better performance
- Efficient MongoDB aggregation pipelines
- Optimized date range handling
- Proper error handling and logging

## Use Cases

### **Dashboard Bar Chart - Usage Trends**
```bash
# Current year monthly usage for bar chart
GET /api/analytics/usage-trends

# Specific period analysis
GET /api/analytics/usage-trends?startDate=2024-01-01&endDate=2024-06-30
```

### **Dashboard Line Chart - College Growth**
```bash
# Overview line chart with targets
GET /api/analytics/college-growth

# Sales performance line chart
GET /api/analytics/college-growth?view=sales

# Revenue trends line chart
GET /api/analytics/college-growth?view=revenue
```

### **Custom Date Ranges**
```bash
# Specific period (Feb 5 - March 6)
GET /api/analytics/usage-trends?startDate=2024-02-05T00:00:00.000Z&endDate=2024-03-06T23:59:59.999Z
GET /api/analytics/college-growth?startDate=2024-02-05T00:00:00.000Z&endDate=2024-03-06T23:59:59.999Z
```

## Response Codes

| Code | Description |
|------|-------------|
| 200 | Success - Dashboard data returned |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions (not super admin) |
| 500 | Internal Server Error |

## Implementation Notes

### **Data Sources**
- **Questions Collection**: For monthly question creation trends
- **QuestionPapers Collection**: For monthly paper generation trends
- **Colleges Collection**: For monthly college growth data
- **Configurable Targets**: Monthly/yearly targets (currently hardcoded, can be made configurable)

### **Caching**
- Results cached for 1 hour (3600 seconds)
- Cache keys include all filter parameters
- Automatic cache invalidation on data updates

### **Mock Data**
- Revenue and sales metrics are currently generated using mock calculations
- In production, these should be replaced with actual revenue/sales data sources
- Target values are configurable and can be moved to database configuration

This implementation fully satisfies all Super Admin Dashboard Overview API requirements with comprehensive chart-ready data, flexible filtering, and multiple data views.
