import {
  IsString,
  IsNotEmpty,
  IsArray,
  IsOptional,
  IsEnum,
  IsMongoId,
  ArrayMinSize,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';

export enum QuestionDifficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
}

export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple-choice',
  TRUE_FALSE = 'true-false',
  DESCRIPTIVE = 'descriptive',
}

export class CreateQuestionDto {
  @ApiProperty({
    description: 'Question content/text',
    example: 'What is the capital of France?',
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    description: 'Answer options for the question',
    example: ['Paris', 'London', 'Berlin', 'Madrid'],
    type: [String],
  })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return [value];
      }
    }
    return value;
  })
  @IsArray()
  @ArrayMinSize(1, { message: 'options must contain at least one option' })
  @IsNotEmpty()
  options: string[];

  @ApiProperty({
    description: 'Correct answer to the question',
    example: 'Paris',
  })
  @IsString()
  @IsNotEmpty()
  answer: string;

  @ApiPropertyOptional({
    description:
      'URLs to images associated with this question (will be populated automatically when images are uploaded)',
    example: ['http://localhost:3000/uploads/images/abc123-def456.jpg'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  imageUrls?: string[];

  @ApiProperty({
    description: 'Subject ID this question belongs to',
    example: '60d21b4667d0d8992e610c85',
  })
  @IsMongoId()
  @IsNotEmpty()
  subjectId: string;

  @ApiPropertyOptional({
    description: 'Topic ID this question belongs to',
    example: '60d21b4667d0d8992e610c86',
  })
  @IsMongoId()
  @IsOptional()
  topicId?: string;

  @ApiProperty({
    description: 'Question difficulty level',
    example: 'medium',
    enum: QuestionDifficulty,
  })
  @IsEnum(QuestionDifficulty)
  @IsNotEmpty()
  difficulty: QuestionDifficulty;

  @ApiProperty({
    description: 'Question type',
    example: 'multiple-choice',
    enum: QuestionType,
  })
  @IsEnum(QuestionType)
  @IsNotEmpty()
  type: QuestionType;

  @ApiPropertyOptional({
    description: 'Tags for categorizing the question',
    example: ['calculus', 'derivatives', 'mathematics'],
    type: [String],
  })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return [value];
      }
    }
    return value;
  })
  @IsArray()
  @IsOptional()
  tags?: string[];

  // Allow images property but don't validate it - handled by FilesInterceptor
  @IsOptional()
  images?: any;
}
