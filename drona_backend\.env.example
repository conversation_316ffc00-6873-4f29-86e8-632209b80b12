# =============================================================================
# DRONA BACKEND - ENVIRONMENT CONFIGURATION (EXAMPLE)
# =============================================================================
# Copy this file to .env and update the values according to your environment
# This file contains only the environment variables that are actively used

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
MONGODB_URI=mongodb://localhost:27017/drona

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT Configuration - Use a strong secret key in production
JWT_SECRET=your_jwt_secret_key_here

# Firebase Configuration (for user authentication)
# Path to Firebase service account JSON file
FIREBASE_SERVICE_ACCOUNT_PATH=path/to/your/firebase-service-account.json

# =============================================================================
# AWS SERVICES CONFIGURATION
# =============================================================================
# AWS S3 for file storage (question papers, images)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name
AWS_S3_IMAGES_PREFIX=questions/images
ENABLE_S3_UPLOAD=true

# =============================================================================
# MISTRAL AI CONFIGURATION
# =============================================================================
# Mistral AI OCR API for PDF question extraction
# Get your API key from: https://console.mistral.ai/
MISTRAL_API_KEY=your_mistral_api_key

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Server Configuration
PORT=3000
API_PREFIX=/api

# CORS Configuration
ENABLE_CORS=true
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# Request Size Limits (for large base64 images - allows large uploads, compresses to 2MB)
MAX_JSON_SIZE=50mb
MAX_URL_ENCODED_SIZE=50mb

# =============================================================================
# PERFORMANCE & SECURITY
# =============================================================================
# Rate Limiting
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration (for analytics)
CACHE_TTL=3600