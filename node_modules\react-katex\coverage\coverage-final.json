{"/Users/<USER>/Development/react-katex/packages/react-katex/src/index.jsx": {"path": "/Users/<USER>/Development/react-katex/packages/react-katex/src/index.jsx", "statementMap": {"0": {"start": {"line": 90, "column": 13}, "end": {"line": 90, "column": 22}}, "1": {"start": {"line": 91, "column": 13}, "end": {"line": 91, "column": 23}}, "2": {"start": {"line": 1, "column": 31}, "end": {"line": 1, "column": null}}, "3": {"start": {"line": 2, "column": 22}, "end": {"line": 2, "column": null}}, "4": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "5": {"start": {"line": 30, "column": 28}, "end": {"line": 72, "column": 1}}, "6": {"start": {"line": 36, "column": 24}, "end": {"line": 62, "column": 3}}, "7": {"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 36}}, "8": {"start": {"line": 39, "column": 28}, "end": {"line": 55, "column": 42}}, "9": {"start": {"line": 40, "column": 6}, "end": {"line": 54, "column": null}}, "10": {"start": {"line": 41, "column": 21}, "end": {"line": 45, "column": 10}}, "11": {"start": {"line": 47, "column": 8}, "end": {"line": 47, "column": 42}}, "12": {"start": {"line": 49, "column": 8}, "end": {"line": 51, "column": null}}, "13": {"start": {"line": 50, "column": 10}, "end": {"line": 50, "column": 27}}, "14": {"start": {"line": 53, "column": 8}, "end": {"line": 53, "column": 20}}, "15": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": null}}, "16": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 88}}, "17": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 37}}, "18": {"start": {"line": 64, "column": 2}, "end": {"line": 69, "column": 4}}, "19": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 23}}, "20": {"start": {"line": 74, "column": 39}, "end": {"line": 76, "column": 1}}, "21": {"start": {"line": 78, "column": 26}, "end": {"line": 80, "column": 1}}, "22": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 86}}, "23": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 61}}, "24": {"start": {"line": 84, "column": 27}, "end": {"line": 86, "column": 1}}, "25": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 87}}, "26": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 62}}, "27": {"start": {"line": 90, "column": 25}, "end": {"line": 90, "column": 86}}, "28": {"start": {"line": 91, "column": 26}, "end": {"line": 91, "column": 89}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 90, "column": 22}, "end": {"line": 90, "column": 25}}, "loc": {"start": {"line": 90, "column": 13}, "end": {"line": 90, "column": 22}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 91, "column": 23}, "end": {"line": 91, "column": 26}}, "loc": {"start": {"line": 91, "column": 13}, "end": {"line": 91, "column": 23}}}, "2": {"name": "(anonymous_7)", "decl": {"start": {"line": 30, "column": 28}, "end": {"line": 30, "column": 29}}, "loc": {"start": {"line": 30, "column": 60}, "end": {"line": 72, "column": 1}}}, "3": {"name": "(anonymous_8)", "decl": {"start": {"line": 36, "column": 24}, "end": {"line": 36, "column": 25}}, "loc": {"start": {"line": 36, "column": 73}, "end": {"line": 62, "column": 3}}}, "4": {"name": "(anonymous_9)", "decl": {"start": {"line": 39, "column": 36}, "end": {"line": 39, "column": 42}}, "loc": {"start": {"line": 39, "column": 42}, "end": {"line": 55, "column": 7}}}, "5": {"name": "(anonymous_10)", "decl": {"start": {"line": 78, "column": 26}, "end": {"line": 78, "column": 27}}, "loc": {"start": {"line": 78, "column": 40}, "end": {"line": 80, "column": 1}}}, "6": {"name": "(anonymous_11)", "decl": {"start": {"line": 84, "column": 27}, "end": {"line": 84, "column": 28}}, "loc": {"start": {"line": 84, "column": 41}, "end": {"line": 86, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 36}}, "type": "cond-expr", "locations": [{"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 24}}, {"start": {"line": 37, "column": 28}, "end": {"line": 37, "column": 36}}]}, "1": {"loc": {"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 28}}, "type": "binary-expr", "locations": [{"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 28}}, {"start": {"line": 37, "column": 20}, "end": {"line": 37, "column": 28}}]}, "2": {"loc": {"start": {"line": 49, "column": 8}, "end": {"line": 51, "column": null}}, "type": "if", "locations": [{"start": {"line": 49, "column": 8}, "end": {"line": 51, "column": null}}]}, "3": {"loc": {"start": {"line": 49, "column": 12}, "end": {"line": 49, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 49, "column": 12}, "end": {"line": 49, "column": 45}}, {"start": {"line": 49, "column": 49}, "end": {"line": 49, "column": 75}}]}, "4": {"loc": {"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": null}}, "type": "if", "locations": [{"start": {"line": 57, "column": 4}, "end": {"line": 59, "column": null}}]}, "5": {"loc": {"start": {"line": 58, "column": 13}, "end": {"line": 58, "column": 88}}, "type": "cond-expr", "locations": [{"start": {"line": 58, "column": 27}, "end": {"line": 58, "column": 45}}, {"start": {"line": 58, "column": 48}, "end": {"line": 58, "column": 88}}]}}, "s": {"0": 1, "1": 1, "2": 2, "3": 2, "4": 2, "5": 2, "6": 4, "7": 32, "8": 32, "9": 32, "10": 32, "11": 22, "12": 10, "13": 10, "14": 0, "15": 32, "16": 10, "17": 22, "18": 4, "19": 4, "20": 2, "21": 2, "22": 12, "23": 2, "24": 2, "25": 12, "26": 2, "27": 2, "28": 2}, "f": {"0": 1, "1": 1, "2": 4, "3": 32, "4": 32, "5": 12, "6": 12}, "b": {"0": [26, 6], "1": [32, 32], "2": [10], "3": [10, 2], "4": [10], "5": [8, 2]}}, "/Users/<USER>/Development/react-katex/packages/react-katex/tests/sharedExamples.js": {"path": "/Users/<USER>/Development/react-katex/packages/react-katex/tests/sharedExamples.js", "statementMap": {"0": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 15}}, "1": {"start": {"line": 1, "column": 7}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 2, "column": 31}, "end": {"line": 2, "column": null}}, "3": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "4": {"start": {"line": 4, "column": 18}, "end": {"line": 4, "column": null}}, "5": {"start": {"line": 6, "column": 15}, "end": {"line": 124, "column": 2}}, "6": {"start": {"line": 7, "column": 21}, "end": {"line": 7, "column": 38}}, "7": {"start": {"line": 8, "column": 26}, "end": {"line": 8, "column": 50}}, "8": {"start": {"line": 9, "column": 32}, "end": {"line": 9, "column": 40}}, "9": {"start": {"line": 10, "column": 28}, "end": {"line": 10, "column": 37}}, "10": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 109}}, "11": {"start": {"line": 11, "column": 33}, "end": {"line": 11, "column": 109}}, "12": {"start": {"line": 13, "column": 31}, "end": {"line": 13, "column": 70}}, "13": {"start": {"line": 13, "column": 37}, "end": {"line": 13, "column": 70}}, "14": {"start": {"line": 15, "column": 37}, "end": {"line": 16, "column": 72}}, "15": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 72}}, "16": {"start": {"line": 18, "column": 26}, "end": {"line": 18, "column": 92}}, "17": {"start": {"line": 18, "column": 39}, "end": {"line": 18, "column": 92}}, "18": {"start": {"line": 20, "column": 33}, "end": {"line": 20, "column": 109}}, "19": {"start": {"line": 20, "column": 46}, "end": {"line": 20, "column": 109}}, "20": {"start": {"line": 22, "column": 2}, "end": {"line": 36, "column": 5}}, "21": {"start": {"line": 23, "column": 4}, "end": {"line": 27, "column": 7}}, "22": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 46}}, "23": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 41}}, "24": {"start": {"line": 29, "column": 4}, "end": {"line": 35, "column": 7}}, "25": {"start": {"line": 30, "column": 27}, "end": {"line": 30, "column": 66}}, "26": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": 53}}, "27": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 63}}, "28": {"start": {"line": 38, "column": 2}, "end": {"line": 52, "column": 5}}, "29": {"start": {"line": 39, "column": 4}, "end": {"line": 43, "column": 7}}, "30": {"start": {"line": 40, "column": 6}, "end": {"line": 40, "column": 55}}, "31": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 46}}, "32": {"start": {"line": 45, "column": 4}, "end": {"line": 51, "column": 7}}, "33": {"start": {"line": 46, "column": 27}, "end": {"line": 46, "column": 75}}, "34": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 52}}, "35": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 41}}, "36": {"start": {"line": 54, "column": 2}, "end": {"line": 123, "column": 5}}, "37": {"start": {"line": 55, "column": 4}, "end": {"line": 61, "column": 7}}, "38": {"start": {"line": 56, "column": 27}, "end": {"line": 56, "column": 103}}, "39": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": 79}}, "40": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": 46}}, "41": {"start": {"line": 63, "column": 4}, "end": {"line": 69, "column": 7}}, "42": {"start": {"line": 64, "column": 38}, "end": {"line": 64, "column": 108}}, "43": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 85}}, "44": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 113}}, "45": {"start": {"line": 71, "column": 4}, "end": {"line": 106, "column": 7}}, "46": {"start": {"line": 72, "column": 6}, "end": {"line": 76, "column": 9}}, "47": {"start": {"line": 73, "column": 8}, "end": {"line": 73, "column": 59}}, "48": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": 77}}, "49": {"start": {"line": 78, "column": 6}, "end": {"line": 84, "column": 9}}, "50": {"start": {"line": 79, "column": 8}, "end": {"line": 83, "column": 11}}, "51": {"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": 81}}, "52": {"start": {"line": 82, "column": 10}, "end": {"line": 82, "column": 76}}, "53": {"start": {"line": 86, "column": 6}, "end": {"line": 97, "column": 9}}, "54": {"start": {"line": 87, "column": 8}, "end": {"line": 90, "column": 11}}, "55": {"start": {"line": 89, "column": 10}, "end": {"line": 89, "column": 70}}, "56": {"start": {"line": 89, "column": 64}, "end": {"line": 89, "column": 68}}, "57": {"start": {"line": 92, "column": 8}, "end": {"line": 96, "column": 11}}, "58": {"start": {"line": 93, "column": 10}, "end": {"line": 93, "column": 55}}, "59": {"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": 74}}, "60": {"start": {"line": 99, "column": 6}, "end": {"line": 105, "column": 9}}, "61": {"start": {"line": 100, "column": 8}, "end": {"line": 104, "column": 11}}, "62": {"start": {"line": 101, "column": 10}, "end": {"line": 101, "column": 57}}, "63": {"start": {"line": 103, "column": 10}, "end": {"line": 103, "column": 97}}, "64": {"start": {"line": 108, "column": 4}, "end": {"line": 122, "column": 7}}, "65": {"start": {"line": 109, "column": 6}, "end": {"line": 113, "column": 9}}, "66": {"start": {"line": 110, "column": 30}, "end": {"line": 110, "column": 106}}, "67": {"start": {"line": 112, "column": 8}, "end": {"line": 112, "column": 115}}, "68": {"start": {"line": 115, "column": 6}, "end": {"line": 121, "column": 9}}, "69": {"start": {"line": 116, "column": 8}, "end": {"line": 120, "column": 11}}, "70": {"start": {"line": 117, "column": 32}, "end": {"line": 117, "column": 104}}, "71": {"start": {"line": 119, "column": 10}, "end": {"line": 119, "column": 117}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 15}}, "loc": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 15}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 16}}, "loc": {"start": {"line": 6, "column": 47}, "end": {"line": 124, "column": 2}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 11, "column": 22}, "end": {"line": 11, "column": 23}}, "loc": {"start": {"line": 11, "column": 33}, "end": {"line": 11, "column": 109}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 13, "column": 31}, "end": {"line": 13, "column": 37}}, "loc": {"start": {"line": 13, "column": 37}, "end": {"line": 13, "column": 70}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 15, "column": 37}, "end": {"line": 15, "column": 38}}, "loc": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 72}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 18, "column": 26}, "end": {"line": 18, "column": 27}}, "loc": {"start": {"line": 18, "column": 39}, "end": {"line": 18, "column": 92}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 20, "column": 33}, "end": {"line": 20, "column": 34}}, "loc": {"start": {"line": 20, "column": 46}, "end": {"line": 20, "column": 109}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 22, "column": 48}, "end": {"line": 22, "column": 54}}, "loc": {"start": {"line": 22, "column": 54}, "end": {"line": 36, "column": 4}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 23, "column": 28}, "end": {"line": 23, "column": 34}}, "loc": {"start": {"line": 23, "column": 34}, "end": {"line": 27, "column": 6}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 29, "column": 42}, "end": {"line": 29, "column": 48}}, "loc": {"start": {"line": 29, "column": 48}, "end": {"line": 35, "column": 6}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 38, "column": 48}, "end": {"line": 38, "column": 54}}, "loc": {"start": {"line": 38, "column": 54}, "end": {"line": 52, "column": 4}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 39, "column": 28}, "end": {"line": 39, "column": 34}}, "loc": {"start": {"line": 39, "column": 34}, "end": {"line": 43, "column": 6}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 45, "column": 42}, "end": {"line": 45, "column": 48}}, "loc": {"start": {"line": 45, "column": 48}, "end": {"line": 51, "column": 6}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 54, "column": 29}, "end": {"line": 54, "column": 35}}, "loc": {"start": {"line": 54, "column": 35}, "end": {"line": 123, "column": 4}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 55, "column": 61}, "end": {"line": 55, "column": 67}}, "loc": {"start": {"line": 55, "column": 67}, "end": {"line": 61, "column": 6}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 63, "column": 61}, "end": {"line": 63, "column": 67}}, "loc": {"start": {"line": 63, "column": 67}, "end": {"line": 69, "column": 6}}}, "16": {"name": "(anonymous_17)", "decl": {"start": {"line": 71, "column": 49}, "end": {"line": 71, "column": 55}}, "loc": {"start": {"line": 71, "column": 55}, "end": {"line": 106, "column": 6}}}, "17": {"name": "(anonymous_18)", "decl": {"start": {"line": 72, "column": 81}, "end": {"line": 72, "column": 87}}, "loc": {"start": {"line": 72, "column": 87}, "end": {"line": 76, "column": 8}}}, "18": {"name": "(anonymous_19)", "decl": {"start": {"line": 78, "column": 50}, "end": {"line": 78, "column": 56}}, "loc": {"start": {"line": 78, "column": 56}, "end": {"line": 84, "column": 8}}}, "19": {"name": "(anonymous_20)", "decl": {"start": {"line": 79, "column": 82}, "end": {"line": 79, "column": 88}}, "loc": {"start": {"line": 79, "column": 88}, "end": {"line": 83, "column": 10}}}, "20": {"name": "(anonymous_21)", "decl": {"start": {"line": 86, "column": 63}, "end": {"line": 86, "column": 69}}, "loc": {"start": {"line": 86, "column": 69}, "end": {"line": 97, "column": 8}}}, "21": {"name": "(anonymous_22)", "decl": {"start": {"line": 87, "column": 19}, "end": {"line": 87, "column": 25}}, "loc": {"start": {"line": 87, "column": 25}, "end": {"line": 90, "column": 10}}}, "22": {"name": "(anonymous_23)", "decl": {"start": {"line": 89, "column": 58}, "end": {"line": 89, "column": 64}}, "loc": {"start": {"line": 89, "column": 64}, "end": {"line": 89, "column": 68}}}, "23": {"name": "(anonymous_24)", "decl": {"start": {"line": 92, "column": 36}, "end": {"line": 92, "column": 42}}, "loc": {"start": {"line": 92, "column": 42}, "end": {"line": 96, "column": 10}}}, "24": {"name": "(anonymous_25)", "decl": {"start": {"line": 99, "column": 69}, "end": {"line": 99, "column": 75}}, "loc": {"start": {"line": 99, "column": 75}, "end": {"line": 105, "column": 8}}}, "25": {"name": "(anonymous_26)", "decl": {"start": {"line": 100, "column": 36}, "end": {"line": 100, "column": 42}}, "loc": {"start": {"line": 100, "column": 42}, "end": {"line": 104, "column": 10}}}, "26": {"name": "(anonymous_27)", "decl": {"start": {"line": 108, "column": 48}, "end": {"line": 108, "column": 54}}, "loc": {"start": {"line": 108, "column": 54}, "end": {"line": 122, "column": 6}}}, "27": {"name": "(anonymous_28)", "decl": {"start": {"line": 109, "column": 63}, "end": {"line": 109, "column": 69}}, "loc": {"start": {"line": 109, "column": 69}, "end": {"line": 113, "column": 8}}}, "28": {"name": "(anonymous_29)", "decl": {"start": {"line": 115, "column": 69}, "end": {"line": 115, "column": 75}}, "loc": {"start": {"line": 115, "column": 75}, "end": {"line": 121, "column": 8}}}, "29": {"name": "(anonymous_30)", "decl": {"start": {"line": 116, "column": 40}, "end": {"line": 116, "column": 46}}, "loc": {"start": {"line": 116, "column": 46}, "end": {"line": 120, "column": 10}}}}, "branchMap": {}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 2, "9": 2, "10": 2, "11": 8, "12": 2, "13": 18, "14": 2, "15": 6, "16": 2, "17": 14, "18": 2, "19": 10, "20": 2, "21": 2, "22": 2, "23": 2, "24": 2, "25": 2, "26": 2, "27": 2, "28": 2, "29": 2, "30": 2, "31": 2, "32": 2, "33": 2, "34": 2, "35": 2, "36": 2, "37": 2, "38": 2, "39": 2, "40": 2, "41": 2, "42": 2, "43": 2, "44": 2, "45": 2, "46": 2, "47": 2, "48": 2, "49": 2, "50": 2, "51": 2, "52": 2, "53": 2, "54": 2, "55": 2, "56": 2, "57": 2, "58": 2, "59": 2, "60": 2, "61": 2, "62": 2, "63": 2, "64": 2, "65": 2, "66": 2, "67": 2, "68": 2, "69": 2, "70": 2, "71": 2}, "f": {"0": 2, "1": 2, "2": 8, "3": 18, "4": 6, "5": 14, "6": 10, "7": 2, "8": 2, "9": 2, "10": 2, "11": 2, "12": 2, "13": 2, "14": 2, "15": 2, "16": 2, "17": 2, "18": 2, "19": 2, "20": 2, "21": 2, "22": 2, "23": 2, "24": 2, "25": 2, "26": 2, "27": 2, "28": 2, "29": 2}, "b": {}}}