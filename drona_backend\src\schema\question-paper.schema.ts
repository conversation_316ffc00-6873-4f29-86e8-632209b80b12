// question-paper.schema.ts
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { College } from './college.schema';
import { User } from './user.schema';
import { Question } from './question.schema';

export class SectionQuestion {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'Question',
    required: true,
  })
  questionId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  order: number;
  
  // This is a virtual property used for populating question data
  question?: any;
}

export class Section {
  @Prop({ required: true })
  name: string;

  @Prop()
  description: string;

  @Prop({ required: true })
  order: number;

  @Prop({ required: true })
  sectionMarks: number;

  @Prop({ type: [Object], required: true })
  questions: SectionQuestion[];
}

export type QuestionPaperDocument = QuestionPaper & Document;

@Schema({ timestamps: true })
export class QuestionPaper {
  @Prop({ required: true })
  title: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true, ref: 'Subject' })
  subjectId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Topic' })
  topicId?: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  totalMarks: number;

  @Prop({ required: true })
  duration: number;

  @Prop()
  instructions?: string;

  @Prop({
    type: [{ type: MongooseSchema.Types.ObjectId, ref: 'Question' }],
    required: true,
  })
  questions: MongooseSchema.Types.ObjectId[];

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true, ref: 'User' })
  generatedBy: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, required: true, ref: 'College' })
  collegeId: MongooseSchema.Types.ObjectId;

  @Prop({ default: 'active' })
  status: string;

  @Prop()
  maxQuestions?: number;

  @Prop()
  maxGeneration?: number;

  @Prop()
  maxDownloads?: number;

  @Prop()
  type?: string; // 'limit' for limit documents, undefined for regular question papers

  @Prop({ default: false })
  withAnswers: boolean;

  @Prop({ type: [Object], required: true })
  sections: Section[];

  @Prop({ enum: ['NEET', 'CET', 'JEE', 'AIIMS', 'JIPMER', 'CUSTOM'] })
  examType?: string; // Exam type for question paper categorization

  @Prop({ enum: ['auto', 'custom'], default: 'custom' })
  difficultyMode?: string; // Auto generation or custom difficulty configuration

  @Prop()
  numberOfQuestions?: number; // Simple question count for auto mode
}

export const QuestionPaperSchema = SchemaFactory.createForClass(QuestionPaper);
