@font-face {
    font-family: 'Open Sans';
    font-weight: normal;
    font-style: normal;
    src: url('../fonts/OpenSans-Regular-webfont.eot');
    src:
        local('Open Sans'),
        local('OpenSans'),
        url('../fonts/OpenSans-Regular-webfont.eot?#iefix') format('embedded-opentype'),
        url('../fonts/OpenSans-Regular-webfont.woff') format('woff'),
        url('../fonts/OpenSans-Regular-webfont.svg#open_sansregular') format('svg');
}

@font-face {
    font-family: 'Open Sans Light';
    font-weight: normal;
    font-style: normal;
    src: url('../fonts/OpenSans-Light-webfont.eot');
    src:
        local('Open Sans Light'),
        local('OpenSans Light'),
        url('../fonts/OpenSans-Light-webfont.eot?#iefix') format('embedded-opentype'),
        url('../fonts/OpenSans-Light-webfont.woff') format('woff'),
        url('../fonts/OpenSans-Light-webfont.svg#open_sanslight') format('svg');
}

html
{
    overflow: auto;
    background-color: #fff;
    font-size: 14px;
}

body
{
    font-family: 'Open Sans', sans-serif;
    line-height: 1.5;
    color: #4d4e53;
    background-color: white;
}

a, a:visited, a:active {
    color: #0095dd;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

header
{
    display: block;
    padding: 0px 4px;
}

tt, code, kbd, samp {
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
}

.class-description {
    font-size: 130%;
    line-height: 140%;
    margin-bottom: 1em;
    margin-top: 1em;
}

.class-description:empty {
    margin: 0;
}

#main {
    float: left;
    width: 70%;
}

article dl {
    margin-bottom: 40px;
}

article img {
  max-width: 100%;
}

section
{
    display: block;
    background-color: #fff;
    padding: 12px 24px;
    border-bottom: 1px solid #ccc;
    margin-right: 30px;
}

.variation {
    display: none;
}

.signature-attributes {
    font-size: 60%;
    color: #aaa;
    font-style: italic;
    font-weight: lighter;
}

nav
{
    display: block;
    float: right;
    margin-top: 28px;
    width: 30%;
    box-sizing: border-box;
    border-left: 1px solid #ccc;
    padding-left: 16px;
}

nav ul {
    font-family: 'Lucida Grande', 'Lucida Sans Unicode', arial, sans-serif;
    font-size: 100%;
    line-height: 17px;
    padding: 0;
    margin: 0;
    list-style-type: none;
}

nav ul a, nav ul a:visited, nav ul a:active {
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    line-height: 18px;
    color: #4D4E53;
}

nav h3 {
    margin-top: 12px;
}

nav li {
    margin-top: 6px;
}

footer {
    display: block;
    padding: 6px;
    margin-top: 12px;
    font-style: italic;
    font-size: 90%;
}

h1, h2, h3, h4 {
    font-weight: 200;
    margin: 0;
}

h1
{
    font-family: 'Open Sans Light', sans-serif;
    font-size: 48px;
    letter-spacing: -2px;
    margin: 12px 24px 20px;
}

h2, h3.subsection-title
{
    font-size: 30px;
    font-weight: 700;
    letter-spacing: -1px;
    margin-bottom: 12px;
}

h3
{
    font-size: 24px;
    letter-spacing: -0.5px;
    margin-bottom: 12px;
}

h4
{
    font-size: 18px;
    letter-spacing: -0.33px;
    margin-bottom: 12px;
    color: #4d4e53;
}

h5, .container-overview .subsection-title
{
    font-size: 120%;
    font-weight: bold;
    letter-spacing: -0.01em;
    margin: 8px 0 3px 0;
}

h6
{
    font-size: 100%;
    letter-spacing: -0.01em;
    margin: 6px 0 3px 0;
    font-style: italic;
}

table
{
    border-spacing: 0;
    border: 0;
    border-collapse: collapse;
}

td, th
{
    border: 1px solid #ddd;
    margin: 0px;
    text-align: left;
    vertical-align: top;
    padding: 4px 6px;
    display: table-cell;
}

thead tr
{
    background-color: #ddd;
    font-weight: bold;
}

th { border-right: 1px solid #aaa; }
tr > th:last-child { border-right: 1px solid #ddd; }

.ancestors, .attribs { color: #999; }
.ancestors a, .attribs a
{
    color: #999 !important;
    text-decoration: none;
}

.clear
{
    clear: both;
}

.important
{
    font-weight: bold;
    color: #950B02;
}

.yes-def {
    text-indent: -1000px;
}

.type-signature {
    color: #aaa;
}

.name, .signature {
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
}

.details { margin-top: 14px; border-left: 2px solid #DDD; }
.details dt { width: 120px; float: left; padding-left: 10px;  padding-top: 6px; }
.details dd { margin-left: 70px; }
.details ul { margin: 0; }
.details ul { list-style-type: none; }
.details li { margin-left: 30px; padding-top: 6px; }
.details pre.prettyprint { margin: 0 }
.details .object-value { padding-top: 0; }

.description {
    margin-bottom: 1em;
    margin-top: 1em;
}

.code-caption
{
    font-style: italic;
    font-size: 107%;
    margin: 0;
}

.source
{
    border: 1px solid #ddd;
    width: 80%;
    overflow: auto;
}

.prettyprint.source {
    width: inherit;
}

.source code
{
    font-size: 100%;
    line-height: 18px;
    display: block;
    padding: 4px 12px;
    margin: 0;
    background-color: #fff;
    color: #4D4E53;
}

.prettyprint code span.line
{
  display: inline-block;
}

.prettyprint.linenums
{
  padding-left: 70px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.prettyprint.linenums ol
{
  padding-left: 0;
}

.prettyprint.linenums li
{
  border-left: 3px #ddd solid;
}

.prettyprint.linenums li.selected,
.prettyprint.linenums li.selected *
{
  background-color: lightyellow;
}

.prettyprint.linenums li *
{
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.params .name, .props .name, .name code {
    color: #4D4E53;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 100%;
}

.params td.description > p:first-child,
.props td.description > p:first-child
{
    margin-top: 0;
    padding-top: 0;
}

.params td.description > p:last-child,
.props td.description > p:last-child
{
    margin-bottom: 0;
    padding-bottom: 0;
}

.disabled {
    color: #454545;
}
