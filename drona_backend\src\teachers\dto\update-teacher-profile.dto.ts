import { IsS<PERSON>, <PERSON><PERSON><PERSON>Empt<PERSON>, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO for teachers to update their own profile
 * Excludes fields that only admins should be able to modify (status, designation)
 */
export class UpdateTeacherProfileDto {
  @ApiPropertyOptional({
    description: 'Teacher full name',
    example: '<PERSON>',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'Teacher email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({
    description: 'Teacher phone number',
    example: '+****************',
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({
    description: 'URL to teacher profile image',
    example: 'https://example.com/images/teacher-profile.jpg',
  })
  @IsString()
  @IsOptional()
  profileImageUrl?: string;
}
