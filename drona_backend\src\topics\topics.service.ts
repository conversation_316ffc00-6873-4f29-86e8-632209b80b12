import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Topic, TopicDocument } from '../schema/topic.schema';
import { Subject, SubjectDocument } from '../schema/subject.schema';
import { CreateTopicDto } from './dto/create-topic.dto';
import { UpdateTopicDto } from './dto/update-topic.dto';

@Injectable()
export class TopicsService {
  private readonly logger = new Logger(TopicsService.name);

  constructor(
    @InjectModel(Topic.name) private topicModel: Model<TopicDocument>,
    @InjectModel(Subject.name) private subjectModel: Model<SubjectDocument>,
  ) {}

  async create(createTopicDto: CreateTopicDto): Promise<Topic> {
    try {
      // Verify that the subject exists
      const subject = await this.subjectModel.findById(
        createTopicDto.subjectId,
      );
      if (!subject) {
        throw new NotFoundException(
          `Subject with ID ${createTopicDto.subjectId} not found`,
        );
      }

      // Check if topic with same name already exists within the same subject
      const existingTopic = await this.topicModel.findOne({
        subjectId: createTopicDto.subjectId,
        name: { $regex: new RegExp(`^${createTopicDto.name}$`, 'i') },
      });

      if (existingTopic) {
        throw new ConflictException(
          `Topic with name '${createTopicDto.name}' already exists in subject '${subject.name}'`,
        );
      }

      const createdTopic = new this.topicModel(createTopicDto);
      const savedTopic = await createdTopic.save();

      this.logger.log(
        `Topic created: ${savedTopic.name} in subject ${subject.name} (ID: ${savedTopic._id})`,
      );
      return savedTopic;
    } catch (error) {
      this.logger.error(
        `Failed to create topic: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findAll(): Promise<Topic[]> {
    try {
      return await this.topicModel
        .find()
        .populate('subjectId', 'name description')
        .sort({ name: 1 })
        .exec();
    } catch (error) {
      this.logger.error(
        `Failed to fetch topics: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findBySubject(subjectId: string): Promise<Topic[]> {
    try {
      // Verify that the subject exists
      const subject = await this.subjectModel.findById(subjectId);
      if (!subject) {
        throw new NotFoundException(`Subject with ID ${subjectId} not found`);
      }

      return await this.topicModel.find({ subjectId }).sort({ name: 1 }).exec();
    } catch (error) {
      this.logger.error(
        `Failed to fetch topics for subject: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findOne(id: string): Promise<Topic> {
    try {
      const topic = await this.topicModel
        .findById(id)
        .populate('subjectId', 'name description')
        .exec();

      if (!topic) {
        throw new NotFoundException(`Topic with ID ${id} not found`);
      }
      return topic;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch topic: ${error.message}`, error.stack);
      throw error;
    }
  }

  async update(id: string, updateTopicDto: UpdateTopicDto): Promise<Topic> {
    try {
      // Check if topic exists
      const existingTopic = await this.findOne(id);

      // If subjectId is being updated, verify the new subject exists
      if (
        updateTopicDto.subjectId &&
        updateTopicDto.subjectId !== existingTopic.subjectId.toString()
      ) {
        const subject = await this.subjectModel.findById(
          updateTopicDto.subjectId,
        );
        if (!subject) {
          throw new NotFoundException(
            `Subject with ID ${updateTopicDto.subjectId} not found`,
          );
        }
      }

      // Check if name is being updated and if it conflicts within the subject
      if (updateTopicDto.name && updateTopicDto.name !== existingTopic.name) {
        const subjectId = updateTopicDto.subjectId || existingTopic.subjectId;
        const conflictingTopic = await this.topicModel.findOne({
          _id: { $ne: id },
          subjectId: subjectId,
          name: { $regex: new RegExp(`^${updateTopicDto.name}$`, 'i') },
        });

        if (conflictingTopic) {
          throw new ConflictException(
            `Topic with name '${updateTopicDto.name}' already exists in this subject`,
          );
        }
      }

      const updatedTopic = await this.topicModel
        .findByIdAndUpdate(id, updateTopicDto, {
          new: true,
          runValidators: true,
        })
        .populate('subjectId', 'name description')
        .exec();

      if (!updatedTopic) {
        throw new NotFoundException(`Topic with ID ${id} not found`);
      }

      this.logger.log(`Topic updated: ${updatedTopic.name} (ID: ${id})`);
      return updatedTopic;
    } catch (error) {
      this.logger.error(
        `Failed to update topic: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      // Check if topic exists
      await this.findOne(id);

      await this.topicModel.findByIdAndDelete(id).exec();
      this.logger.log(`Topic deleted: ID ${id}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete topic: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
