// app.module.ts
import {
  Module,
  NestModule,
  MiddlewareConsumer,
  RequestMethod,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { CollegesModule } from './colleges/colleges.module';
import { TeachersModule } from './teachers/teachers.module';
import { QuestionsModule } from './questions/questions.module';
import { QuestionPapersModule } from './question-papers/question-papers.module';

import { SubjectsModule } from './subjects/subjects.module';
import { TopicsModule } from './topics/topics.module';
import { AnalyticsModule as CollegeAnalyticsModule } from './analytics/college/analytics.module';
import { AnalyticsModule as SuperAdminAnalyticsModule } from './analytics/super-admin/analytics.module';
import { RateLimiterMiddleware } from './common/middleware';
import { CommonModule } from './common/common.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const uri = configService.get<string>('MONGODB_URI');
        return {
          uri,
          dbName: 'Medicos', // Explicitly set the database name to Medicos (optional)
          // Connection options for better performance and reliability (optional)
          serverSelectionTimeoutMS: 5000,
          socketTimeoutMS: 45000,
        };
      },
      inject: [ConfigService],
    }),
    CommonModule,
    AuthModule,
    UsersModule,
    CollegesModule,
    TeachersModule,
    QuestionsModule,
    QuestionPapersModule,
    SubjectsModule,
    TopicsModule,
    CollegeAnalyticsModule,
    SuperAdminAnalyticsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply rate limiter middleware to all routes
    consumer
      .apply(RateLimiterMiddleware)
      .forRoutes({ path: '*', method: RequestMethod.ALL });
  }
}
