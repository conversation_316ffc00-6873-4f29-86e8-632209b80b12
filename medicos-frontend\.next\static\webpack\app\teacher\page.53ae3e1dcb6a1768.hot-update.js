"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/lib/api/questionPapers.ts":
/*!***************************************!*\
  !*** ./src/lib/api/questionPapers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestionPaper: () => (/* binding */ createQuestionPaper),\n/* harmony export */   downloadQuestionPaper: () => (/* binding */ downloadQuestionPaper),\n/* harmony export */   getQuestionPaper: () => (/* binding */ getQuestionPaper),\n/* harmony export */   getQuestionPapers: () => (/* binding */ getQuestionPapers)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Get authentication headers with proper token\n */ function getAuthHeaders() {\n    // Try different token storage keys used in the codebase\n    const backendToken = localStorage.getItem(\"backendToken\");\n    const firebaseToken = localStorage.getItem(\"firebaseToken\");\n    const token = localStorage.getItem(\"token\");\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // Prefer backend token, then firebase token, then generic token\n    if (backendToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(backendToken);\n    } else if (firebaseToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(firebaseToken);\n    } else if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    } else {\n        throw new Error(\"Authentication required - Please log in again. No valid authentication token found.\");\n    }\n    return headers;\n}\n/**\n * Create a new question paper\n * @param questionPaperData The question paper data\n * @returns The created question paper or error object\n */ async function createQuestionPaper(questionPaperData) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(questionPaperData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            try {\n                // Try to get error message from response body\n                const errorText = await response.text();\n                if (errorText) {\n                    try {\n                        // Try to parse as JSON first\n                        const errorData = JSON.parse(errorText);\n                        // Extract the message from the parsed JSON\n                        if (errorData && errorData.message) {\n                            errorMessage = errorData.message;\n                        } else if (errorData && errorData.error) {\n                            errorMessage = errorData.error;\n                        } else {\n                            errorMessage = errorText;\n                        }\n                    } catch (jsonError) {\n                        // If not JSON, use the text directly\n                        errorMessage = errorText;\n                    }\n                }\n            } catch (parseError) {\n            // Silently handle parse errors\n            }\n            // Provide more specific error messages based on status code if we don't have a message\n            if (!errorMessage || errorMessage === \"Error: \".concat(response.status, \" - \").concat(response.statusText)) {\n                switch(response.status){\n                    case 401:\n                        errorMessage = \"Authentication required - Please log in again.\";\n                        break;\n                    case 403:\n                        errorMessage = \"Access denied - You don't have permission to perform this action.\";\n                        break;\n                    case 404:\n                        errorMessage = \"Resource not found - The requested item could not be found.\";\n                        break;\n                    case 429:\n                        errorMessage = \"Too many requests - Please wait a moment before trying again.\";\n                        break;\n                    case 500:\n                        errorMessage = \"Server error - Please try again later.\";\n                        break;\n                    case 503:\n                        errorMessage = \"Service unavailable - The server is temporarily down.\";\n                        break;\n                    default:\n                        if (response.status >= 400 && response.status < 500) {\n                            errorMessage = \"Invalid request - Please check your input and try again.\";\n                        } else if (response.status >= 500) {\n                            errorMessage = \"Server error - Please try again later.\";\n                        }\n                }\n            }\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n        const data = await response.json();\n        return {\n            success: true,\n            data\n        };\n    } catch (error) {\n        return {\n            success: false,\n            error: \"Network error - Please check your connection and try again.\"\n        };\n    }\n}\n/**\n * Download a question paper as PDF\n * @param questionPaperId The question paper ID\n * @param format The format (pdf or docx)\n * @returns The file blob\n */ async function downloadQuestionPaper(questionPaperId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'pdf';\n    try {\n        const headers = getAuthHeaders();\n        delete headers[\"Content-Type\"]; // Remove content-type for blob response\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId, \"/download?format=\").concat(format), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            let errorMessage = errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            if (response.status === 401) {\n                errorMessage = \"Authentication required - Please log in again.\";\n            } else if (response.status === 404) {\n                errorMessage = \"Question paper not found.\";\n            } else if (response.status >= 500) {\n                errorMessage = \"Server error - Please try again later.\";\n            }\n            throw new Error(errorMessage);\n        }\n        return await response.blob();\n    } catch (error) {\n        console.error(\"Error downloading question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Get all question papers\n * @returns List of question papers\n */ async function getQuestionPapers() {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question papers:\", error);\n        throw error;\n    }\n}\n/**\n * Get a specific question paper by ID\n * @param questionPaperId The question paper ID\n * @returns The question paper\n */ async function getQuestionPaper(questionPaperId) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questionPapers.ts\n"));

/***/ })

});