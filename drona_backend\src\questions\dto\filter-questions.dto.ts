import {
  IsOptional,
  IsString,
  <PERSON>E<PERSON>,
  ValidateIf,
  IsMongoId,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum QuestionType {
  MCQ = 'mcq',
  MULTIPLE_CHOICE = 'multiple-choice',
  TRUE_FALSE = 'true-false',
  DESCRIPTIVE = 'descriptive',
}

export enum Difficulty {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
}

/**
 * DTO for filtering questions
 */
export class FilterQuestionsDto {
  @ApiPropertyOptional({
    description: 'Filter by subject ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ValidateIf((o) => o.topicId)
  @IsMongoId()
  @IsString()
  subjectId?: string;

  @ApiPropertyOptional({
    description: 'Filter by topic ID',
    example: '60d21b4667d0d8992e610c86',
  })
  @IsOptional()
  @IsMongoId()
  @IsString()
  topicId?: string;

  @ApiPropertyOptional({
    description: 'Filter by difficulty level',
    example: 'medium',
    enum: Difficulty,
  })
  @IsOptional()
  @IsEnum(Difficulty)
  difficulty?: Difficulty;

  @ApiPropertyOptional({
    description: 'Filter by question type',
    example: 'multiple-choice',
    enum: QuestionType,
  })
  @IsOptional()
  @IsEnum(QuestionType)
  type?: QuestionType;

  @ApiPropertyOptional({
    description: 'Search in question content',
    example: 'calculus',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by review status',
    example: 'pending',
    enum: ['pending', 'approved', 'rejected'],
  })
  @IsOptional()
  @IsEnum(['pending', 'approved', 'rejected'])
  reviewStatus?: 'pending' | 'approved' | 'rejected';

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  limit?: number;
}
