import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  <PERSON><PERSON>,
  Req,
  Logger,
} from '@nestjs/common';
import { Response } from 'express';
import * as path from 'path';
import { QuestionPapersService } from './question-papers.service';
import { CreateQuestionPaperDto } from './dto/create-question-paper.dto';
import { UpdateQuestionPaperDto } from './dto/update-question-paper.dto';
import { SetQuestionLimitDto } from './dto/set-question-limit.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Role } from '../auth/enums/role.enum';
import { RequestWithUser } from '../auth/interfaces/request-with-user.interface';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  <PERSON>piParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { TrackingService } from '../common/services';

/**
 * Question paper generation and management endpoints
 *
 * These endpoints allow teachers to create, manage, and download question papers.
 * Super admins can set limits on question paper generation.
 */
@ApiTags('Question Papers')
@ApiBearerAuth('JWT-auth')
@Controller('question-papers')
@UseGuards(JwtAuthGuard, RolesGuard)
export class QuestionPapersController {
  private readonly logger = new Logger(QuestionPapersController.name);

  constructor(
    private readonly questionPapersService: QuestionPapersService,
    private readonly trackingService: TrackingService,
  ) {}

  @Post()
  @Roles(Role.TEACHER, Role.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Create question paper (Unified)',
    description:
      'Create a new question paper with automatic or customized generation. If customise object is provided, generates customized question paper. Otherwise, generates automatic question paper. Questions are global (any college can use any approved question), but reuse prevention is applied per college.',
  })
  @ApiBody({
    type: CreateQuestionPaperDto,
    description:
      'Question paper configuration with automatic or customized generation options',
    examples: {
      'Automatic Question Paper': {
        summary: 'Automatic question paper generation',
        value: {
          title: 'NEET Physics Mock Test 2024',
          subject: 'physics',
          examType: 'NEET',
          totalMarks: 100,
          duration: 180,
          instructions: 'Read all questions carefully before answering',
        },
      },
      'Customized Question Paper': {
        summary: 'Customized question paper with specific configuration',
        value: {
          title: 'Custom Physics Test 2024',
          subject: 'physics',
          examType: 'CUSTOM',
          totalMarks: 100,
          duration: 180,
          instructions: 'Read all questions carefully',
          customise: {
            customDifficulty: {
              easyPercentage: 30,
              mediumPercentage: 50,
              hardPercentage: 20,
            },
            numberOfQuestions: 50,
            totalMarks: 100,
            duration: 180,
            includeAnswers: true,
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Question paper created successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        title: { type: 'string', example: 'NEET Physics Mock Test 2024' },
        description: {
          type: 'string',
          example: 'Final examination for Physics course',
        },
        subjectId: { type: 'string', example: '60d21b4667d0d8992e610c86' },
        topicId: { type: 'string', example: '60d21b4667d0d8992e610c87' },
        totalMarks: { type: 'number', example: 100 },
        duration: { type: 'number', example: 180 },
        withAnswers: { type: 'boolean', example: true },
        instructions: {
          type: 'string',
          example: 'Read all questions carefully before answering',
        },
        examType: { type: 'string', example: 'NEET' },
        difficultyMode: { type: 'string', example: 'custom' },
        questions: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
              content: { type: 'string', example: 'What is the derivative of f(x) = x²?' },
              options: { type: 'array', items: { type: 'string' }, example: ['2x', 'x', '2', '1'] },
              answer: { type: 'string', example: '2x' },
              difficulty: { type: 'string', example: 'medium' },
              type: { type: 'string', example: 'multiple-choice' },
              marks: { type: 'number', example: 4 },
            },
          },
        },
        generatedBy: { type: 'string', example: '60d21b4667d0d8992e610c90' },
        collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
        status: { type: 'string', example: 'active' },
        sections: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string', example: 'Section A' },
              description: { type: 'string', example: 'All Questions' },
              order: { type: 'number', example: 1 },
              sectionMarks: { type: 'number', example: 100 },
              questions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    question: {
                      type: 'object',
                      properties: {
                        _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
                        content: { type: 'string', example: 'What is the derivative of f(x) = x²?' },
                        options: { type: 'array', items: { type: 'string' }, example: ['2x', 'x', '2', '1'] },
                        answer: { type: 'string', example: '2x' },
                        difficulty: { type: 'string', example: 'medium' },
                        type: { type: 'string', example: 'multiple-choice' },
                        marks: { type: 'number', example: 4 },
                      },
                    },
                    order: { type: 'number', example: 1 },
                  },
                },
              },
            },
          },
        },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input or limit exceeded',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  async create(
    @Req() req: RequestWithUser,
    @Body() createQuestionPaperDto: CreateQuestionPaperDto,
  ) {
    const questionPaper = await this.questionPapersService.createUnified(
      createQuestionPaperDto,
      req.user,
    );

    // Track paper generation
    await this.trackingService.trackPaperGeneration({
      userId: req.user._id,
      paperId: (questionPaper as any)._id.toString(),
      collegeId: req.user.collegeId,
      subjectId: (questionPaper as any).subjectId.toString(),
      ipAddress: req.ip,
    });

    return questionPaper;
  }

  @Get()
  @Roles(Role.TEACHER, Role.COLLEGE_ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get all question papers based on user role',
    description:
      'Returns question papers based on user role: Teachers see only their own papers, College Admins see all papers from their college with teacher details, Super Admins see all papers with college-wise grouping.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all question papers',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          title: { type: 'string', example: 'Mathematics Final Exam 2024' },
          subjectId: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
              name: { type: 'string', example: 'Mathematics' },
            },
          },
          totalMarks: { type: 'number', example: 100 },
          duration: { type: 'number', example: 180 },
          generatedBy: { type: 'string', example: '60d21b4667d0d8992e610c90' },
          collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
          status: { type: 'string', example: 'active' },
          createdAt: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  findAll(@Req() req: RequestWithUser) {
    return this.questionPapersService.findAll(req.user);
  }

  @Get(':id')
  @Roles(Role.TEACHER, Role.COLLEGE_ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Get a specific question paper by ID',
    description:
      'Returns a specific question paper by ID with role-based access: Teachers can only access their own papers, College Admins can access papers from their college, Super Admins can access any paper.',
  })
  @ApiParam({
    name: 'id',
    description: 'Question paper ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the question paper',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        title: { type: 'string', example: 'Mathematics Final Exam 2024' },
        description: {
          type: 'string',
          example: 'Final examination for Mathematics course',
        },
        subjectId: {
          type: 'object',
          properties: {
            _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
            name: { type: 'string', example: 'Mathematics' },
          },
        },
        topicId: {
          type: 'object',
          properties: {
            _id: { type: 'string', example: '60d21b4667d0d8992e610c87' },
            name: { type: 'string', example: 'Calculus' },
          },
        },
        totalMarks: { type: 'number', example: 100 },
        duration: { type: 'number', example: 180 },
        instructions: {
          type: 'string',
          example: 'Answer all questions. Each question carries equal marks.',
        },
        questions: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
              content: {
                type: 'string',
                example: 'What is the derivative of f(x) = x²?',
              },
              options: {
                type: 'array',
                items: { type: 'string' },
                example: ['2x', 'x', '2', '1'],
              },
              answer: { type: 'string', example: '2x' },
              difficulty: { type: 'string', example: 'medium' },
            },
          },
        },
        generatedBy: { type: 'string', example: '60d21b4667d0d8992e610c90' },
        collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
        status: { type: 'string', example: 'active' },
        sections: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string', example: 'Section A' },
              description: {
                type: 'string',
                example: 'Multiple Choice Questions',
              },
              order: { type: 'number', example: 1 },
              sectionMarks: { type: 'number', example: 50 },
              questions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    question: {
                      type: 'object',
                      properties: {
                        _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
                        content: { type: 'string', example: 'What is the derivative of f(x) = x²?' },
                        options: { type: 'array', items: { type: 'string' }, example: ['2x', 'x', '2', '1'] },
                        answer: { type: 'string', example: '2x' },
                        difficulty: { type: 'string', example: 'medium' },
                        type: { type: 'string', example: 'multiple-choice' },
                        marks: { type: 'number', example: 4 },
                      },
                    },
                    order: { type: 'number', example: 1 },
                  },
                },
              },
            },
          },
        },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({ status: 404, description: 'Question paper not found' })
  findOne(@Req() req: RequestWithUser, @Param('id') id: string) {
    return this.questionPapersService.findOne(id, req.user);
  }

  @Get(':id/download')
  @Roles(Role.TEACHER, Role.COLLEGE_ADMIN, Role.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Download a question paper in PDF or DOCX format',
    description:
      'Download question papers with role-based access and download limits enforcement. Teachers can download their own papers, College Admins can download papers from their college, Super Admins can download any paper.',
  })
  @ApiParam({ name: 'id', description: 'Question paper ID' })
  @ApiQuery({
    name: 'format',
    enum: ['pdf', 'docx'],
    required: false,
    description: 'File format',
  })
  @ApiResponse({ status: 200, description: 'Returns the file for download' })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid format' })
  @ApiResponse({ status: 404, description: 'Question paper not found' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async download(
    @Param('id') id: string,
    @Query('format') format: 'pdf' | 'docx' = 'pdf',
    @Req() req: RequestWithUser,
    @Res() res: Response,
  ) {
    try {
      // Validate format
      if (format !== 'pdf' && format !== 'docx') {
        return res.status(400).json({
          statusCode: 400,
          message: `Invalid format: ${format}. Supported formats are 'pdf' and 'docx'.`,
        });
      }

      // Generate the file
      const filePath = await this.questionPapersService.download(
        id,
        format,
        req.user,
      );

      // Track the download
      await this.trackingService.trackDownload({
        userId: req.user._id,
        paperId: id,
        collegeId: req.user.collegeId,
        downloadFormat: format,
        ipAddress: req.ip,
      });

      // Send the file
      return res.download(filePath, path.basename(filePath), (err) => {
        if (err) {
          this.logger.error(`Error sending file: ${err.message}`, err.stack);
          // If headers are already sent, we can't send an error response
          if (!res.headersSent) {
            return res.status(500).json({
              statusCode: 500,
              message: 'Error sending file',
            });
          }
        }
      });
    } catch (error) {
      this.logger.error(`Error in download: ${error.message}`, error.stack);

      // If headers are already sent, we can't send an error response
      if (!res.headersSent) {
        const status = error.status || 500;
        const message = error.message || 'Internal server error';
        return res.status(status).json({
          statusCode: status,
          message,
        });
      }
    }
  }

  @Post('limits')
  @Roles(Role.SUPER_ADMIN)
  @ApiOperation({
    summary: 'Set question paper generation and download limits for a college',
    description:
      'Sets the maximum number of question papers that can be generated and downloaded per college. Only super admins can set these limits.',
  })
  @ApiResponse({
    status: 201,
    description: 'Limit set successfully',
    schema: {
      type: 'object',
      properties: {
        collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
        subjectId: { type: 'string', example: '60d21b4667d0d8992e610c86' },
        maxQuestions: { type: 'number', example: 50 },
        message: { type: 'string', example: 'Question limit set successfully' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiResponse({
    status: 404,
    description: 'Not found - College or subject not found',
  })
  setQuestionLimit(@Body() setQuestionLimitDto: SetQuestionLimitDto) {
    return this.questionPapersService.setQuestionLimit(setQuestionLimitDto);
  }

  @Patch(':id')
  @Roles(Role.TEACHER)
  @ApiOperation({
    summary: 'Update a question paper',
    description:
      'Updates an existing question paper. Teachers can only update their own question papers.',
  })
  @ApiParam({
    name: 'id',
    description: 'Question paper ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiResponse({
    status: 200,
    description: 'Question paper updated successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        title: {
          type: 'string',
          example: 'Mathematics Final Exam 2024 (Updated)',
        },
        description: {
          type: 'string',
          example: 'Updated final examination for Mathematics course',
        },
        instructions: {
          type: 'string',
          example: 'Updated instructions. Answer all questions.',
        },
        updatedAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid token' })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions or not the owner',
  })
  @ApiResponse({ status: 404, description: 'Question paper not found' })
  update(
    @Req() req: RequestWithUser,
    @Param('id') id: string,
    @Body() updateQuestionPaperDto: UpdateQuestionPaperDto,
  ) {
    return this.questionPapersService.update(
      id,
      updateQuestionPaperDto,
      req.user,
    );
  }
}
