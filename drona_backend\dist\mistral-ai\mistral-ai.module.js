"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MistralAiModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const config_1 = require("@nestjs/config");
const mistral_ai_service_1 = require("./mistral-ai.service");
const question_schema_1 = require("../schema/question.schema");
let MistralAiModule = class MistralAiModule {
};
exports.MistralAiModule = MistralAiModule;
exports.MistralAiModule = MistralAiModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            mongoose_1.MongooseModule.forFeature([
                { name: question_schema_1.Question.name, schema: question_schema_1.QuestionSchema },
            ]),
        ],
        providers: [mistral_ai_service_1.MistralAiService],
        exports: [mistral_ai_service_1.MistralAiService],
    })
], MistralAiModule);
//# sourceMappingURL=mistral-ai.module.js.map