import { Document, Schema as MongooseSchema } from 'mongoose';
export type QuestionDocument = Question & Document;
export declare class Question {
    content: string;
    options: string[];
    answer: string;
    imageUrls?: string[];
    subjectId: MongooseSchema.Types.ObjectId;
    topicId?: MongooseSchema.Types.ObjectId;
    difficulty: string;
    type: string;
    createdBy: MongooseSchema.Types.ObjectId;
    status: string;
    reviewStatus: string;
    reviewedBy: MongooseSchema.Types.ObjectId;
    reviewDate: Date;
    reviewNotes: string;
    source?: string;
}
export declare const QuestionSchema: MongooseSchema<Question, import("mongoose").Model<Question, any, any, any, Document<unknown, any, Question> & Question & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Question, Document<unknown, {}, import("mongoose").FlatRecord<Question>> & import("mongoose").FlatRecord<Question> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
