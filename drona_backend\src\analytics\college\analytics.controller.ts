import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/guards/roles.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
} from '@nestjs/swagger';

/**
 * Controller for college-level analytics
 * Provides endpoints for college administrators to view analytics data
 */
@ApiTags('Analytics - College')
@ApiBearerAuth('JWT-auth')
@Controller('analytics/college/:collegeId')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('collegeAdmin')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  /**
   * Get a summary of college statistics
   * @param collegeId College ID
   * @returns College summary data
   */
  @Get('summary')
  @ApiOperation({
    summary: 'Get college summary',
    description: 'Returns a summary of college statistics',
  })
  @ApiParam({
    name: 'collegeId',
    description: 'College ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'Returns college summary data',
    schema: {
      type: 'object',
      properties: {
        totalTeachers: { type: 'number', example: 45 },
        activeTeachers: { type: 'number', example: 42 },
        totalQuestionPapers: { type: 'number', example: 156 },
        totalQuestions: { type: 'number', example: 2340 },
        totalDownloads: { type: 'number', example: 320 },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'College not found' })
  getCollegeSummary(@Param('collegeId') collegeId: string) {
    return this.analyticsService.getCollegeSummary(collegeId);
  }

  /**
   * Get teacher activity logs
   * @param collegeId College ID
   * @param page Page number for pagination
   * @param limit Number of items per page
   * @returns Paginated teacher activity logs
   */
  @Get('teachers')
  @ApiOperation({
    summary: 'Get teacher download history',
    description:
      'Returns teachers with their download history organized by subject',
  })
  @ApiParam({
    name: 'collegeId',
    description: 'College ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiQuery({
    name: 'page',
    description: 'Page number',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Items per page',
    required: false,
    type: Number,
    example: 100,
  })
  @ApiQuery({
    name: 'teacherId',
    description: 'Filter by specific teacher ID',
    required: false,
    type: String,
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Filter downloads from this date (ISO string)',
    required: false,
    type: String,
    example: '2023-07-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endDate',
    description: 'Filter downloads until this date (ISO string)',
    required: false,
    type: String,
    example: '2023-07-31T23:59:59.999Z',
  })
  @ApiOkResponse({
    description:
      'Returns teachers with their download history organized by subject',
    schema: {
      type: 'object',
      properties: {
        teachers: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              teacherId: {
                type: 'string',
                example: '60d21b4667d0d8992e610c85',
              },
              teacherName: { type: 'string', example: 'John Doe' },
              teacherEmail: { type: 'string', example: '<EMAIL>' },
              totalDownloads: { type: 'number', example: 25 },
              subjectWiseDownloads: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    subjectId: {
                      type: 'string',
                      example: '60d21b4667d0d8992e610c85',
                    },
                    subjectName: { type: 'string', example: 'Mathematics' },
                    downloadCount: { type: 'number', example: 15 },
                    lastDownload: {
                      type: 'string',
                      format: 'date-time',
                      example: '2023-07-21T15:30:00.000Z',
                    },
                  },
                },
              },
            },
          },
        },
        pagination: {
          type: 'object',
          properties: {
            total: { type: 'number', example: 45 },
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 100 },
            pages: { type: 'number', example: 1 },
          },
        },
        filters: {
          type: 'object',
          description: 'Applied filters',
          properties: {
            teacherId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'College not found' })
  getTeacherActivity(
    @Param('collegeId') collegeId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 100,
    @Query('teacherId') teacherId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.analyticsService.getTeacherActivityLogs(
      collegeId,
      limit,
      page,
      {
        teacherId,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
      },
    );
  }

  /**
   * Get question paper statistics
   * @param collegeId College ID
   * @param startDate Start date for filtering
   * @param endDate End date for filtering
   * @returns Question paper statistics by subject
   */
  @Get('question-papers')
  @ApiOperation({
    summary: 'Get question paper statistics',
    description:
      'Returns daily-wise statistics for question papers generated and downloaded by subject with optional date filtering',
  })
  @ApiParam({
    name: 'collegeId',
    description: 'College ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Filter from this date (ISO string)',
    required: false,
    type: String,
    example: '2023-07-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endDate',
    description: 'Filter until this date (ISO string)',
    required: false,
    type: String,
    example: '2023-07-31T23:59:59.999Z',
  })
  @ApiOkResponse({
    description: 'Returns daily-wise question paper statistics by subject',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              date: {
                type: 'string',
                format: 'date-time',
                example: '2023-07-21T00:00:00.000Z',
              },
              subjects: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    subjectId: {
                      type: 'string',
                      example: '60d21b4667d0d8992e610c85',
                    },
                    subjectName: { type: 'string', example: 'Mathematics' },
                    generated: { type: 'number', example: 5 },
                    downloaded: { type: 'number', example: 3 },
                  },
                },
              },
            },
          },
        },
        totalDays: { type: 'number', example: 31 },
        dateRange: {
          type: 'object',
          properties: {
            startDate: {
              type: 'string',
              format: 'date-time',
              example: '2023-07-01T00:00:00.000Z',
            },
            endDate: {
              type: 'string',
              format: 'date-time',
              example: '2023-07-31T23:59:59.999Z',
            },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'College not found' })
  getQuestionPaperStats(
    @Param('collegeId') collegeId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.analyticsService.getQuestionPaperDownloadStats(collegeId, {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    });
  }

  /**
   * Get subject-wise analytics
   * @param collegeId College ID
   * @returns Subject-wise analytics with question papers generated, questions generated, and downloads per subject
   */
  @Get('subjects')
  @ApiOperation({
    summary: 'Get subject-wise analytics',
    description:
      'Returns analytics data grouped by subject showing question papers generated, questions generated, and question papers downloaded per subject',
  })
  @ApiParam({
    name: 'collegeId',
    description: 'College ID',
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'Returns subject-wise analytics data',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          subjectId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          subjectName: { type: 'string', example: 'Mathematics' },
          questionPapersGenerated: { type: 'number', example: 15 },
          questionsGenerated: { type: 'number', example: 120 },
          questionPapersDownloaded: { type: 'number', example: 8 },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  @ApiNotFoundResponse({ description: 'College not found' })
  getSubjectWiseAnalytics(@Param('collegeId') collegeId: string) {
    return this.analyticsService.getSubjectWiseAnalytics(collegeId);
  }
}
