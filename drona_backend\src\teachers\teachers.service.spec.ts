import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TeachersService } from './teachers.service';
import { User } from '../schema/user.schema';
import { College } from '../schema/college.schema';
import { CreateTeacherDto } from './dto/create-teacher.dto';
import { UpdateTeacherDto } from './dto/update-teacher.dto';
import { UpdateTeacherProfileDto } from './dto/update-teacher-profile.dto';
import { UpdateTeacherAdminDto } from './dto/update-teacher-admin.dto';
import { NotFoundException, BadRequestException } from '@nestjs/common';

describe('TeachersService', () => {
  let service: TeachersService;
  let userModel: Model<User>;
  let collegeModel: Model<College>;

  const mockCollege = {
    _id: 'college123',
    name: 'Test College',
    status: 'active',
    teachers: [],
    save: jest.fn().mockResolvedValue(this),
  };

  const mockUser = {
    _id: 'user123',
    email: '<EMAIL>',
    displayName: '<PERSON>e',
    firstName: 'John',
    lastName: 'Doe',
    phone: '+**********',
    role: 'teacher',
    collegeId: 'college123',
    status: 'active',
    department: 'Computer Science',
    designation: 'Professor',
    save: jest.fn().mockResolvedValue(this),
    populate: jest.fn().mockResolvedValue(this),
  };

  const mockUserModel: any = jest.fn().mockImplementation(() => ({
    ...mockUser,
    save: jest.fn().mockResolvedValue(mockUser),
    populate: jest.fn().mockResolvedValue(mockUser),
  }));

  // Add static methods to the mock constructor
  mockUserModel.findById = jest.fn();
  mockUserModel.findOne = jest.fn();
  mockUserModel.find = jest.fn();
  mockUserModel.findByIdAndUpdate = jest.fn();
  mockUserModel.findByIdAndDelete = jest.fn();

  const mockCollegeModel = {
    findById: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TeachersService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
        {
          provide: getModelToken(College.name),
          useValue: mockCollegeModel,
        },
      ],
    }).compile();

    service = module.get<TeachersService>(TeachersService);
    userModel = module.get<Model<User>>(getModelToken(User.name));
    collegeModel = module.get<Model<College>>(getModelToken(College.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addTeacherToCollege', () => {
    const createTeacherDto: CreateTeacherDto = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+**********',
      department: 'Computer Science',
      designation: 'Professor',
      profileImageUrl: 'https://example.com/profile.jpg',
    };

    it('should create a teacher with proper field mapping', async () => {
      // Mock college exists
      mockCollegeModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCollege),
      });

      // Mock no existing user
      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const result = await service.addTeacherToCollege(
        'college123',
        createTeacherDto,
      );

      // Verify that the constructor was called with proper field mapping
      expect(mockUserModel).toHaveBeenCalledWith({
        email: '<EMAIL>',
        phone: '+**********',
        department: 'Computer Science',
        designation: 'Professor',
        profileImageUrl: 'https://example.com/profile.jpg',
        displayName: 'John Doe',
        firstName: 'John',
        lastName: 'Doe',
        role: 'teacher',
        collegeId: 'college123',
      });
    });

    it('should handle single name correctly', async () => {
      const singleNameDto = { ...createTeacherDto, name: 'John' };

      mockCollegeModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCollege),
      });

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await service.addTeacherToCollege('college123', singleNameDto);

      expect(mockUserModel).toHaveBeenCalledWith({
        email: '<EMAIL>',
        phone: '+**********',
        department: 'Computer Science',
        designation: 'Professor',
        profileImageUrl: 'https://example.com/profile.jpg',
        displayName: 'John',
        firstName: 'John',
        lastName: '',
        role: 'teacher',
        collegeId: 'college123',
      });
    });

    it('should create a teacher with minimal required fields only', async () => {
      const minimalTeacherDto: CreateTeacherDto = {
        name: 'Jane Smith',
        email: '<EMAIL>',
      };

      mockCollegeModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCollege),
      });

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      const result = await service.addTeacherToCollege(
        'college123',
        minimalTeacherDto,
      );

      // Verify that the constructor was called with proper field mapping
      expect(mockUserModel).toHaveBeenCalledWith({
        email: '<EMAIL>',
        displayName: 'Jane Smith',
        firstName: 'Jane',
        lastName: 'Smith',
        role: 'teacher',
        collegeId: 'college123',
      });
    });

    it('should throw NotFoundException when college does not exist', async () => {
      mockCollegeModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(
        service.addTeacherToCollege('nonexistent', createTeacherDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException when email already exists', async () => {
      mockCollegeModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCollege),
      });

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockUser),
      });

      await expect(
        service.addTeacherToCollege('college123', createTeacherDto),
      ).rejects.toThrow(BadRequestException);
    });

    it('should add teacher email to college.teachers array', async () => {
      const collegeWithEmptyTeachers = {
        ...mockCollege,
        teachers: [],
        save: jest.fn().mockResolvedValue(this),
      };

      mockCollegeModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(collegeWithEmptyTeachers),
      });

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await service.addTeacherToCollege('college123', createTeacherDto);

      expect(collegeWithEmptyTeachers.teachers).toContain(
        '<EMAIL>',
      );
      expect(collegeWithEmptyTeachers.save).toHaveBeenCalled();
    });

    it('should not add duplicate email to college.teachers array', async () => {
      const collegeWithExistingTeacher = {
        ...mockCollege,
        teachers: ['<EMAIL>'],
        save: jest.fn().mockResolvedValue(this),
      };

      mockCollegeModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(collegeWithExistingTeacher),
      });

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await service.addTeacherToCollege('college123', createTeacherDto);

      expect(collegeWithExistingTeacher.teachers).toEqual([
        '<EMAIL>',
      ]);
      expect(collegeWithExistingTeacher.save).not.toHaveBeenCalled();
    });
  });

  describe('findAllTeachersInCollege', () => {
    it('should return all teachers in a college', async () => {
      mockCollegeModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockCollege),
      });

      const mockTeachers = [mockUser];
      mockUserModel.find.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue(mockTeachers),
        }),
      });

      const result = await service.findAllTeachersInCollege('college123');

      expect(result).toEqual(mockTeachers);
      expect(mockUserModel.find).toHaveBeenCalledWith({
        collegeId: 'college123',
        role: 'teacher',
      });
    });

    it('should throw NotFoundException when college does not exist', async () => {
      mockCollegeModel.findById.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      });

      await expect(
        service.findAllTeachersInCollege('nonexistent'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('update teacher profile', () => {
    it('should update teacher profile with restricted fields', async () => {
      const updateProfileDto: UpdateTeacherProfileDto = {
        name: 'Jane Smith',
        phone: '+9876543210',
        profileImageUrl: 'https://example.com/new-profile.jpg',
      };

      const existingTeacher = { ...mockUser, _id: 'teacher123' };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingTeacher),
      });

      mockUserModel.findByIdAndUpdate.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue({
            ...existingTeacher,
            displayName: 'Jane Smith',
            firstName: 'Jane',
            lastName: 'Smith',
            phone: '+9876543210',
            profileImageUrl: 'https://example.com/new-profile.jpg',
          }),
        }),
      });

      const result = await service.update('teacher123', updateProfileDto);

      expect(mockUserModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'teacher123',
        {
          phone: '+9876543210',
          profileImageUrl: 'https://example.com/new-profile.jpg',
          displayName: 'Jane Smith',
          firstName: 'Jane',
          lastName: 'Smith',
        },
        { new: true },
      );
    });

    it('should handle profile update with single name', async () => {
      const updateProfileDto: UpdateTeacherProfileDto = {
        name: 'Jane',
        phone: '+9876543210',
      };

      const existingTeacher = { ...mockUser, _id: 'teacher123' };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingTeacher),
      });

      mockUserModel.findByIdAndUpdate.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue({
            ...existingTeacher,
            displayName: 'Jane',
            firstName: 'Jane',
            lastName: '',
            phone: '+9876543210',
          }),
        }),
      });

      await service.update('teacher123', updateProfileDto);

      expect(mockUserModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'teacher123',
        {
          phone: '+9876543210',
          displayName: 'Jane',
          firstName: 'Jane',
          lastName: '',
        },
        { new: true },
      );
    });
  });

  describe('update teacher admin fields', () => {
    it('should update teacher with admin-only fields', async () => {
      const updateAdminDto: UpdateTeacherAdminDto = {
        phone: '+1111111111',
        department: 'Physics',
        designation: 'Professor',
        status: 'inactive',
        role: 'collegeAdmin',
      };

      const existingTeacher = { ...mockUser, _id: 'teacher123' };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingTeacher),
      });

      mockUserModel.findByIdAndUpdate.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue({
            ...existingTeacher,
            phone: '+1111111111',
            department: 'Physics',
            designation: 'Professor',
            status: 'inactive',
            role: 'collegeAdmin',
          }),
        }),
      });

      const result = await service.update('teacher123', updateAdminDto);

      expect(mockUserModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'teacher123',
        {
          phone: '+1111111111',
          department: 'Physics',
          designation: 'Professor',
          status: 'inactive',
          role: 'collegeAdmin',
        },
        { new: true },
      );
    });

    it('should update only provided admin fields', async () => {
      const updateAdminDto: UpdateTeacherAdminDto = {
        designation: 'Senior Professor',
        status: 'active',
      };

      const existingTeacher = { ...mockUser, _id: 'teacher123' };

      mockUserModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(existingTeacher),
      });

      mockUserModel.findByIdAndUpdate.mockReturnValue({
        populate: jest.fn().mockReturnValue({
          exec: jest.fn().mockResolvedValue({
            ...existingTeacher,
            designation: 'Senior Professor',
            status: 'active',
          }),
        }),
      });

      await service.update('teacher123', updateAdminDto);

      expect(mockUserModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'teacher123',
        {
          designation: 'Senior Professor',
          status: 'active',
        },
        { new: true },
      );
    });
  });
});
