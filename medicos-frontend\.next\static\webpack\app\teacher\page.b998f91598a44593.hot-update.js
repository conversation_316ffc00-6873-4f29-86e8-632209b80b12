"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    paperMode: \"single\",\n    course: \"\",\n    subject: \"\",\n    subjects: [],\n    subjectConfigs: {},\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easyPercentage: 30,\n        mediumPercentage: 50,\n        hardPercentage: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const goToFirstStep = ()=>{\n        setCurrentStep(0);\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                setIsGenerating(false);\n                alert(\"Please enter a title for the question paper\");\n                return;\n            }\n            if (!formData.questionType) {\n                setIsGenerating(false);\n                alert(\"Please select an exam type\");\n                return;\n            }\n            if (!formData.subject) {\n                setIsGenerating(false);\n                alert(\"Please select a subject\");\n                return;\n            }\n            // Prepare the API payload\n            const apiPayload = {\n                title: formData.title,\n                description: formData.description,\n                subject: formData.subject,\n                totalMarks: formData.totalMarks,\n                duration: formData.duration,\n                examType: formData.questionType,\n                instructions: formData.instructions,\n                topicId: formData.topicId,\n                maxQuestions: formData.numberOfQuestions\n            };\n            // Add customization if not auto mode\n            if (formData.difficultyMode === \"custom\") {\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: formData.difficultyLevels.easy,\n                        mediumPercentage: formData.difficultyLevels.medium,\n                        hardPercentage: formData.difficultyLevels.hard\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            } else {\n                // For auto mode, still include customization with default values\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: 30,\n                        mediumPercentage: 50,\n                        hardPercentage: 20\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            console.log(\"Calling createQuestionPaper API...\");\n            const result = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"API result:\", result);\n            // Check if the request was successful\n            if (!result.success) {\n                console.log(\"API returned error:\", result.error);\n                setIsGenerating(false);\n                let errorMessage = result.error;\n                // Handle specific error types with better messages\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                    errorMessage = \"Please log in again to continue. Your session may have expired.\";\n                } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                    errorMessage = \"Please check your internet connection and try again.\";\n                } else if (errorMessage.includes(\"unused questions available\")) {\n                    // Extract numbers from the error message for a clearer explanation\n                    const match = errorMessage.match(/Only (\\d+) unused questions available\\. Requested: (\\d+)/);\n                    if (match) {\n                        const available = match[1];\n                        const requested = match[2];\n                        errorMessage = \"Only \".concat(available, \" questions are available for this subject/topic, but you requested \").concat(requested, \" questions. Please reduce the number of questions or add more questions to the database.\");\n                    }\n                }\n                // Show error message in alert\n                alert(\"Error: \".concat(errorMessage));\n                console.log(\"Staying on current step due to error\");\n                return; // Exit early on error\n            }\n            // Success - proceed with download\n            console.log(\"API success! Proceeding with download...\");\n            const questionPaper = result.data;\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaper._id, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            alert(\"Question paper generated and downloaded successfully!\");\n            console.log(\"Success! About to redirect to first step in 1 second...\");\n            // Reset to first step and clear form data after a short delay (only on success)\n            setTimeout(()=>{\n                console.log(\"Redirecting to first step now...\");\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n                console.log(\"Redirect completed. Current step should be 0\");\n            }, 1000) // 1 second delay to ensure alert is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            // Handle any unexpected errors (like network issues)\n            alert(\"Error: An unexpected error occurred. Please try again.\");\n        }\n    };\n    const steps = [\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__.QuestionTypeStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 252,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__.QuestionTitleAndDescriptionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 266,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Course & Subject Selection\",\n            icon: \"BookOpen\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__.CourseSubjectStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Select Difficulty Level\",\n            icon: \"BarChart2\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__.DifficultyLevelStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 294,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Selection Criteria\",\n            icon: \"FileText\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__.QuestionSelectionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 308,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Paper Customization\",\n            icon: \"FileEdit\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__.PaperCustomizationStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 322,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Actions\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_9__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating,\n                onBack: goToFirstStep\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 349,\n                columnNumber: 18\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 356,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"nel0TECL1Zv7CzoM26K8Hca8GSI=\");\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});