// users/schemas/user.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { College } from './college.schema';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export type UserDocument = User & Document;

@Schema({ timestamps: true })
export class User {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    uniqueItems: true,
  })
  @Prop({ required: true, unique: true })
  email: string;

  @ApiPropertyOptional({
    description: 'Hashed password for local authentication',
    example: '$2b$10$X7...',
  })
  @Prop()
  password: string; // Added password field for local authentication

  @ApiPropertyOptional({
    description: 'Firebase user ID for authentication',
    example: 'firebase123',
  })
  @Prop()
  firebaseUid: string; // For Firebase authentication

  @ApiProperty({ description: 'User display name', example: '<PERSON>' })
  @Prop()
  displayName: string;

  @ApiPropertyOptional({ description: 'User first name', example: '<PERSON>' })
  @Prop()
  firstName: string;

  @ApiPropertyOptional({ description: 'User last name', example: 'Doe' })
  @Prop()
  lastName: string;

  @ApiProperty({
    description: 'User role',
    example: 'teacher',
    enum: ['superAdmin', 'collegeAdmin', 'teacher'],
  })
  @Prop({ required: true, enum: ['superAdmin', 'collegeAdmin', 'teacher'] })
  role: string;

  @ApiPropertyOptional({
    description: 'College ID the user belongs to',
    example: '60d21b4667d0d8992e610c85',
    type: 'string',
  })
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'College' })
  collegeId: College;

  @ApiPropertyOptional({
    description: 'Last login timestamp',
    example: '2023-07-21T15:30:00.000Z',
  })
  @Prop()
  lastLogin: Date;

  @ApiProperty({
    description: 'User account status',
    example: 'active',
    enum: ['active', 'inactive'],
    default: 'active',
  })
  @Prop({ default: 'active', enum: ['active', 'inactive'] })
  status: string;

  @ApiPropertyOptional({
    description: 'URL to user profile image',
    example: 'https://example.com/images/profile.jpg',
  })
  @Prop()
  profileImageUrl: string;

  @ApiPropertyOptional({
    description: 'User phone number',
    example: '+****************',
  })
  @Prop()
  phone: string;

  @ApiPropertyOptional({
    description: 'Department the user belongs to (for teachers)',
    example: 'Computer Science',
  })
  @Prop()
  department: string;

  @ApiPropertyOptional({
    description: 'User designation/title (for teachers)',
    example: 'Associate Professor',
  })
  @Prop()
  designation: string;
}

export const UserSchema = SchemaFactory.createForClass(User);
