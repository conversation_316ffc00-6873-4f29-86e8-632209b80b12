// download.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { QuestionPaper } from './question-paper.schema';
import { User } from './user.schema';
import { College } from './college.schema';

export type DownloadDocument = Download & Document;

@Schema()
export class Download {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'QuestionPaper',
    required: true,
  })
  paperId: QuestionPaper;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: User;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'College' })
  collegeId: College;

  @Prop({ required: true })
  downloadDate: Date;

  @Prop({ required: true, enum: ['pdf', 'docx'] })
  downloadFormat: string;
}

export const DownloadSchema = SchemaFactory.createForClass(Download);
