"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx":
/*!****************************************************************!*\
  !*** ./src/components/teacher/steps/difficulty-level-step.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DifficultyLevelStep: () => (/* binding */ DifficultyLevelStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _ui_option_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/option-button */ \"(app-pages-browser)/./src/components/teacher/ui/option-button.tsx\");\n/* harmony import */ var _ui_step_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/step-navigation */ \"(app-pages-browser)/./src/components/teacher/ui/step-navigation.tsx\");\n/* harmony import */ var _ui_info_message__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/info-message */ \"(app-pages-browser)/./src/components/teacher/ui/info-message.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ DifficultyLevelStep auto */ \n\n\n\n\n\nfunction DifficultyLevelStep(param) {\n    let { formData, updateFormData, onNext, onSkip, onBack, backDisabled } = param;\n    const handleModeSelect = (mode)=>{\n        if (mode === \"auto\") {\n            // Set default percentages for auto generation\n            updateFormData({\n                difficultyMode: mode,\n                difficultyLevels: {\n                    easy: 30,\n                    medium: 50,\n                    hard: 20\n                }\n            });\n        } else {\n            updateFormData({\n                difficultyMode: mode\n            });\n        }\n    };\n    const adjustDifficulty = (level, amount)=>{\n        const newValue = Math.max(0, Math.min(100, formData.difficultyLevels[level] + amount));\n        updateFormData({\n            difficultyLevels: {\n                ...formData.difficultyLevels,\n                [level]: newValue\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold\",\n                        children: \"Select Difficulty Level\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Choose the complexity of your questions: Easy, Medium, Hard, or Mixed\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_option_button__WEBPACK_IMPORTED_MODULE_1__.OptionButton, {\n                            selected: formData.difficultyMode === \"auto\",\n                            onClick: ()=>handleModeSelect(\"auto\"),\n                            grouped: true,\n                            position: \"left\",\n                            className: \"rounded-none border-0\",\n                            children: \"Auto generation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-px bg-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_option_button__WEBPACK_IMPORTED_MODULE_1__.OptionButton, {\n                            selected: formData.difficultyMode === \"custom\",\n                            onClick: ()=>handleModeSelect(\"custom\"),\n                            grouped: true,\n                            position: \"right\",\n                            className: \"rounded-none border-0\",\n                            children: \"Customization\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-left font-medium\",\n                                children: \"Easy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"easy\", -10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-[50px] text-center font-medium\",\n                                        children: [\n                                            formData.difficultyLevels.easyPercentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"easy\", 10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-left font-medium\",\n                                children: \"Medium\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"medium\", -10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-[50px] text-center font-medium\",\n                                        children: [\n                                            formData.difficultyLevels.medium,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"medium\", 10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-left font-medium\",\n                                children: \"Hard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"hard\", -10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-[50px] text-center font-medium\",\n                                        children: [\n                                            formData.difficultyLevels.hard,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"hard\", 10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_navigation__WEBPACK_IMPORTED_MODULE_2__.StepNavigation, {\n                onNext: onNext,\n                onSkip: onSkip,\n                onBack: onBack,\n                backDisabled: backDisabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_info_message__WEBPACK_IMPORTED_MODULE_3__.InfoMessage, {\n                message: \"Please select a difficulty level before proceeding. Your choice will determine the complexity of the questions generated.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_c = DifficultyLevelStep;\nvar _c;\n$RefreshReg$(_c, \"DifficultyLevelStep\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\n"));

/***/ })

});