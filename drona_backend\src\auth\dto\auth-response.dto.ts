// auth/dto/auth-response.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * User information returned in authentication response
 */
export class UserInfoDto {
  @ApiProperty({
    description: 'User ID',
    example: '60d21b4667d0d8992e610c85',
  })
  id: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'User display name',
    example: '<PERSON>',
  })
  displayName: string;

  @ApiProperty({
    description: 'User role',
    example: 'teacher',
    enum: ['superAdmin', 'collegeAdmin', 'teacher'],
  })
  role: string;

  @ApiPropertyOptional({
    description: 'College ID (for collegeAdmin and teacher roles)',
    example: '60d21b4667d0d8992e610c85',
  })
  collegeId?: string;

  @ApiPropertyOptional({
    description: 'User first name',
    example: '<PERSON>',
  })
  firstName?: string;

  @ApiPropertyOptional({
    description: 'User last name',
    example: '<PERSON>e',
  })
  lastName?: string;

  @ApiPropertyOptional({
    description: 'User phone number',
    example: '+****************',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: 'Department (for teachers)',
    example: 'Computer Science',
  })
  department?: string;

  @ApiPropertyOptional({
    description: 'Designation/title (for teachers)',
    example: 'Associate Professor',
  })
  designation?: string;

  @ApiPropertyOptional({
    description: 'URL to user profile image',
    example: 'https://example.com/images/profile.jpg',
  })
  profileImageUrl?: string;

  @ApiPropertyOptional({
    description: 'User account status',
    example: 'active',
    enum: ['active', 'inactive'],
  })
  status?: string;
}

/**
 * Authentication response containing JWT token and user information
 */
export class AuthResponseDto {
  @ApiProperty({
    description: 'JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  accessToken: string;

  @ApiProperty({
    description: 'User information',
    type: UserInfoDto,
  })
  user: UserInfoDto;
}
