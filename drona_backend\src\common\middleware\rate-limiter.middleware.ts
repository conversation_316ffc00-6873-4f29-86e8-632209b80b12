import {
  Injectable,
  NestMiddleware,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response, NextFunction } from 'express';

interface RateLimitInfo {
  count: number;
  resetTime: number;
}

@Injectable()
export class RateLimiterMiddleware implements NestMiddleware {
  private readonly requestMap = new Map<string, RateLimitInfo>();
  private readonly windowMs: number;
  private readonly maxRequests: number;

  constructor(private configService: ConfigService) {
    // Parse rate limit window from environment (default: 15 minutes)
    const windowStr =
      this.configService.get<string>('RATE_LIMIT_WINDOW') || '15m';
    this.windowMs = this.parseTimeWindow(windowStr);

    // Parse max requests from environment (default: 100)
    this.maxRequests =
      this.configService.get<number>('RATE_LIMIT_MAX_REQUESTS') || 100;
  }

  use(req: Request, res: Response, next: NextFunction) {
    // Skip rate limiting if disabled
    if (this.configService.get<boolean>('ENABLE_RATE_LIMIT') === false) {
      return next();
    }

    // Get client IP address
    const ip =
      req.ip ||
      (req.headers['x-forwarded-for'] as string)?.split(',')[0].trim() ||
      'unknown';

    // Current timestamp
    const now = Date.now();

    // Get or create rate limit info for this IP
    let rateLimitInfo = this.requestMap.get(ip);

    if (!rateLimitInfo || now > rateLimitInfo.resetTime) {
      // First request or window expired, create new rate limit info
      rateLimitInfo = {
        count: 1,
        resetTime: now + this.windowMs,
      };
    } else {
      // Increment request count
      rateLimitInfo.count++;

      // Check if rate limit exceeded
      if (rateLimitInfo.count > this.maxRequests) {
        const resetTime = new Date(rateLimitInfo.resetTime);

        // Set rate limit headers
        res.setHeader(
          'Retry-After',
          Math.ceil((rateLimitInfo.resetTime - now) / 1000),
        );
        res.setHeader('X-RateLimit-Limit', this.maxRequests);
        res.setHeader('X-RateLimit-Remaining', 0);
        res.setHeader(
          'X-RateLimit-Reset',
          Math.ceil(rateLimitInfo.resetTime / 1000),
        );

        throw new HttpException(
          `Rate limit exceeded. Try again after ${resetTime.toISOString()}`,
          HttpStatus.TOO_MANY_REQUESTS,
        );
      }
    }

    // Update rate limit info
    this.requestMap.set(ip, rateLimitInfo);

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', this.maxRequests);
    res.setHeader(
      'X-RateLimit-Remaining',
      this.maxRequests - rateLimitInfo.count,
    );
    res.setHeader(
      'X-RateLimit-Reset',
      Math.ceil(rateLimitInfo.resetTime / 1000),
    );

    next();
  }

  private parseTimeWindow(timeStr: string): number {
    const match = timeStr.match(/^(\d+)([smhd])$/);
    if (!match) {
      return 15 * 60 * 1000; // Default: 15 minutes
    }

    const value = parseInt(match[1], 10);
    const unit = match[2];

    switch (unit) {
      case 's':
        return value * 1000; // seconds
      case 'm':
        return value * 60 * 1000; // minutes
      case 'h':
        return value * 60 * 60 * 1000; // hours
      case 'd':
        return value * 24 * 60 * 60 * 1000; // days
      default:
        return 15 * 60 * 1000; // Default: 15 minutes
    }
  }
}
