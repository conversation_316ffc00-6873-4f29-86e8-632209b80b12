/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-katex";
exports.ids = ["vendor-chunks/react-katex"];
exports.modules = {

/***/ "(ssr)/../node_modules/react-katex/dist/react-katex.js":
/*!*******************************************************!*\
  !*** ../node_modules/react-katex/dist/react-katex.js ***!
  \*******************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;(function(global, factory) {\n    if ( true && typeof module.exports === \"object\") factory(exports, __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\"), __webpack_require__(/*! prop-types */ \"(ssr)/../node_modules/prop-types/index.js\"), __webpack_require__(/*! katex */ \"(ssr)/../node_modules/katex/dist/katex.js\"));\n    else if (true) !(__WEBPACK_AMD_DEFINE_ARRAY__ = [\n        exports,\n        __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\"),\n        __webpack_require__(/*! prop-types */ \"(ssr)/../node_modules/prop-types/index.js\"),\n        __webpack_require__(/*! katex */ \"(ssr)/../node_modules/katex/dist/katex.js\")\n    ], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n    else {}\n})(this, function(exports, _react, _propTypes, _katex) {\n    \"use strict\";\n    Object.defineProperty(exports, \"__esModule\", {\n        value: true\n    });\n    function _export(target, all) {\n        for(var name in all)Object.defineProperty(target, name, {\n            enumerable: true,\n            get: all[name]\n        });\n    }\n    _export(exports, {\n        BlockMath: ()=>BlockMath,\n        InlineMath: ()=>InlineMath\n    });\n    _react = /*#__PURE__*/ _interopRequireWildcard(_react);\n    _propTypes = /*#__PURE__*/ _interopRequireDefault(_propTypes);\n    _katex = /*#__PURE__*/ _interopRequireDefault(_katex);\n    function _interopRequireDefault(obj) {\n        return obj && obj.__esModule ? obj : {\n            default: obj\n        };\n    }\n    function _getRequireWildcardCache(nodeInterop) {\n        if (typeof WeakMap !== \"function\") return null;\n        var cacheBabelInterop = new WeakMap();\n        var cacheNodeInterop = new WeakMap();\n        return (_getRequireWildcardCache = function(nodeInterop) {\n            return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n        })(nodeInterop);\n    }\n    function _interopRequireWildcard(obj, nodeInterop) {\n        if (!nodeInterop && obj && obj.__esModule) {\n            return obj;\n        }\n        if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n            return {\n                default: obj\n            };\n        }\n        var cache = _getRequireWildcardCache(nodeInterop);\n        if (cache && cache.has(obj)) {\n            return cache.get(obj);\n        }\n        var newObj = {};\n        var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n        for(var key in obj){\n            if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n                var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n                if (desc && (desc.get || desc.set)) {\n                    Object.defineProperty(newObj, key, desc);\n                } else {\n                    newObj[key] = obj[key];\n                }\n            }\n        }\n        newObj.default = obj;\n        if (cache) {\n            cache.set(obj, newObj);\n        }\n        return newObj;\n    }\n    /**\n * @typedef {import(\"react\").ReactNode} ReactNode\n *\n *\n * @callback ErrorRenderer\n * @param {Error} error\n * @returns {ReactNode}\n *\n *\n * @typedef {object} MathComponentPropsWithMath\n * @property {string} math\n * @property {ReactNode=} children\n * @property {string=} errorColor\n * @property {ErrorRenderer=} renderError\n *\n *\n * @typedef {object} MathComponentPropsWithChildren\n * @property {string=} math\n * @property {ReactNode} children\n * @property {string=} errorColor\n * @property {ErrorRenderer=} renderError\n *\n * @typedef {MathComponentPropsWithMath | MathComponentPropsWithChildren} MathComponentProps\n */ const createMathComponent = (Component, { displayMode  })=>{\n        /**\n   *\n   * @param {MathComponentProps} props\n   * @returns {ReactNode}\n   */ const MathComponent = ({ children , errorColor , math , renderError  })=>{\n            const formula = math !== null && math !== void 0 ? math : children;\n            const { html , error  } = (0, _react.useMemo)(()=>{\n                try {\n                    const html = _katex.default.renderToString(formula, {\n                        displayMode,\n                        errorColor,\n                        throwOnError: !!renderError\n                    });\n                    return {\n                        html,\n                        error: undefined\n                    };\n                } catch (error) {\n                    if (error instanceof _katex.default.ParseError || error instanceof TypeError) {\n                        return {\n                            error\n                        };\n                    }\n                    throw error;\n                }\n            }, [\n                formula,\n                errorColor,\n                renderError\n            ]);\n            if (error) {\n                return renderError ? renderError(error) : /*#__PURE__*/ _react.default.createElement(Component, {\n                    html: `${error.message}`\n                });\n            }\n            return /*#__PURE__*/ _react.default.createElement(Component, {\n                html: html\n            });\n        };\n        MathComponent.propTypes = {\n            children: _propTypes.default.string,\n            errorColor: _propTypes.default.string,\n            math: _propTypes.default.string,\n            renderError: _propTypes.default.func\n        };\n        return MathComponent;\n    };\n    const InternalPathComponentPropTypes = {\n        html: _propTypes.default.string.isRequired\n    };\n    const InternalBlockMath = ({ html  })=>{\n        return /*#__PURE__*/ _react.default.createElement(\"div\", {\n            \"data-testid\": \"react-katex\",\n            dangerouslySetInnerHTML: {\n                __html: html\n            }\n        });\n    };\n    InternalBlockMath.propTypes = InternalPathComponentPropTypes;\n    const InternalInlineMath = ({ html  })=>{\n        return /*#__PURE__*/ _react.default.createElement(\"span\", {\n            \"data-testid\": \"react-katex\",\n            dangerouslySetInnerHTML: {\n                __html: html\n            }\n        });\n    };\n    InternalInlineMath.propTypes = InternalPathComponentPropTypes;\n    const BlockMath = createMathComponent(InternalBlockMath, {\n        displayMode: true\n    });\n    const InlineMath = createMathComponent(InternalInlineMath, {\n        displayMode: false\n    });\n});\n\n//# sourceMappingURL=data:application/json;base64,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\n\n//# sourceMappingURL=react-katex.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/react-katex/dist/react-katex.js\n");

/***/ })

};
;