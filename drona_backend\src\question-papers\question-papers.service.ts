import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Document, Types } from 'mongoose';
import { QuestionPaper } from '../schema/question-paper.schema';
import { Question } from '../schema/question.schema';
import { Subject } from '../schema/subject.schema';
import { TrackingService } from '../common/services/tracking.service';
import { QuestionUsageService } from '../question-usage/question-usage.service';
import {
  CreateQuestionPaperDto,
  SubjectShortCode,
  ExamType,
} from './dto/create-question-paper.dto';
import { SetQuestionLimitDto } from './dto/set-question-limit.dto';
import { UpdateQuestionPaperDto } from './dto/update-question-paper.dto';
import * as fs from 'fs';
import * as path from 'path';
import * as PDFDocument from 'pdfkit';
// Note: The docx package needs to be installed with: npm install docx --save
import {
  Document as DocxDocument,
  Packer,
  Paragraph,
  TextRun,
  HeadingLevel,
  AlignmentType,
  BorderStyle,
} from 'docx';

@Injectable()
export class QuestionPapersService {
  private readonly logger = new Logger(QuestionPapersService.name);

  constructor(
    @InjectModel(QuestionPaper.name)
    private questionPaperModel: Model<QuestionPaper>,
    @InjectModel(Question.name) private questionModel: Model<Question>,
    @InjectModel(Subject.name) private subjectModel: Model<Subject>,
    private trackingService: TrackingService,
    private questionUsageService: QuestionUsageService,
  ) {}

  /**
   * Unified question paper creation method
   * Supports both automatic and customized generation modes
   */
  async createUnified(
    createQuestionPaperDto: CreateQuestionPaperDto,
    user: any,
  ): Promise<QuestionPaper> {
    try {
      // Only Teachers and Super Admins can create question papers
      if (user.role !== 'teacher' && user.role !== 'superAdmin') {
        throw new BadRequestException(
          'Only teachers and super admins can create question papers.',
        );
      }

      // For teachers, collegeId is required
      if (user.role === 'teacher' && !user.collegeId) {
        throw new BadRequestException(
          'Teacher must be associated with a college',
        );
      }

      // Determine if this is customized or automatic generation
      const isCustomized = !!createQuestionPaperDto.customise;

      // Resolve subject - can be either ObjectId or shortcode
      const subject = await this.resolveSubject(createQuestionPaperDto.subject);
      if (!subject) {
        throw new BadRequestException('Invalid subject provided');
      }

      // Check generation limits
      await this.checkGenerationLimits(user, subject._id.toString());

      // Build question query - questions are global, not college-specific
      const questionQuery: any = {
        subjectId: subject._id,
        status: 'active',
        reviewStatus: 'approved',
      };

      // Add topic filter if specified
      if (createQuestionPaperDto.topicId) {
        questionQuery.topicId = createQuestionPaperDto.topicId;
      }

      // Note: Questions are not filtered by college - any college can use any question
      // College-specific usage tracking is handled in filterUnusedQuestions method

      // Get available questions
      const availableQuestions = await this.questionModel.find(questionQuery);
      if (availableQuestions.length === 0) {
        throw new BadRequestException(
          'No questions available for the specified criteria',
        );
      }

      // Filter out recently used questions
      const unusedQuestions = await this.filterUnusedQuestions(
        availableQuestions,
        user,
        subject._id.toString(),
        createQuestionPaperDto.topicId?.toString(),
      );

      if (unusedQuestions.length === 0) {
        throw new BadRequestException(
          'No unused questions available. Please add more questions or wait for the duplicate prevention period to expire.',
        );
      }

      let selectedQuestions: any[];
      let sections: any[];
      let totalMarks: number;
      let duration: number;
      let withAnswers: boolean;

      if (isCustomized) {
        // Customized generation mode
        const customConfig = createQuestionPaperDto.customise!;

        if (unusedQuestions.length < customConfig.numberOfQuestions) {
          throw new BadRequestException(
            `Only ${unusedQuestions.length} unused questions available. Requested: ${customConfig.numberOfQuestions}`,
          );
        }

        // Select questions based on custom difficulty
        selectedQuestions = await this.selectQuestionsByDifficulty(
          unusedQuestions,
          customConfig.numberOfQuestions,
          'custom',
          customConfig.customDifficulty,
        );

        // Create a single section for customized papers
        sections = [
          {
            name: 'Section A',
            description: 'All Questions',
            order: 1,
            sectionMarks: customConfig.totalMarks,
            questions: selectedQuestions.map((q, index) => ({
              questionId: q._id,
              order: index + 1,
            })),
          },
        ];

        totalMarks = customConfig.totalMarks;
        duration = customConfig.duration;
        withAnswers = customConfig.includeAnswers;
      } else {
        // Automatic generation mode
        const numberOfQuestions = Math.min(50, unusedQuestions.length);
        selectedQuestions = await this.selectQuestionsByDifficulty(
          unusedQuestions,
          numberOfQuestions,
          'auto',
        );

        sections = [
          {
            name: 'Section A',
            description: 'All Questions',
            order: 1,
            sectionMarks: createQuestionPaperDto.totalMarks,
            questions: selectedQuestions.map((q, index) => ({
              questionId: q._id,
              order: index + 1,
            })),
          },
        ];

        totalMarks = createQuestionPaperDto.totalMarks;
        duration = createQuestionPaperDto.duration;
        withAnswers = false; // Automatic mode never includes answers
      }

      // Create question paper data
      const questionPaperData: any = {
        title: createQuestionPaperDto.title,
        description: createQuestionPaperDto.description,
        subjectId: subject._id,
        topicId: createQuestionPaperDto.topicId,
        totalMarks,
        duration,
        withAnswers,
        instructions: createQuestionPaperDto.instructions,
        sections,
        questions: selectedQuestions.map((q) => q._id),
        generatedBy: user._id,
        status: 'active',
        examType: createQuestionPaperDto.examType || ExamType.CUSTOM,
        difficultyMode: isCustomized ? 'custom' : 'auto',
      };

      // Set collegeId for teachers
      if (user.collegeId) {
        questionPaperData.collegeId = user.collegeId;
      }

      const questionPaper = new this.questionPaperModel(questionPaperData);
      const savedPaper = await questionPaper.save();

      // Record question usage for permanent tracking (only for teachers with collegeId)
      if (user.role === 'teacher' && user.collegeId) {
        await this.recordQuestionUsage(savedPaper, selectedQuestions, user);
      }

      // Populate the saved paper with full question details before returning
      const populatedPaper = await this.questionPaperModel
        .findById(savedPaper._id)
        .populate('subjectId', 'name description')
        .populate('topicId', 'name description')
        .populate('questions', 'content options answer difficulty type marks explanation imageUrls')
        .exec();

      // Transform sections to include full question objects instead of just IDs
      if (populatedPaper && populatedPaper.sections) {
        populatedPaper.sections = populatedPaper.sections.map(section => {
          const mappedQuestions = section.questions.map(sectionQuestion => {
            const fullQuestion = populatedPaper.questions.find(
              q => {
                // Add proper type checking and handling
                const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
                const qIdStr = qId ? qId.toString() : '';
                const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
                return qIdStr === sectionQuestionIdStr;
              }
            );
            return {
              questionId: sectionQuestion.questionId,
              order: sectionQuestion.order,
              question: fullQuestion // Add this as an additional property
            };
          });
          
          return {
            ...section,
            questions: mappedQuestions
          };
        });
      }

      this.logger.log(
        `Unified question paper created: ${savedPaper.title} (ID: ${savedPaper._id})`,
      );
      return populatedPaper as QuestionPaper;
    } catch (error) {
      this.logger.error(
        `Error creating unified question paper: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findAll(user: any) {
    let query: any = {};
    const populateFields = ['subjectId', 'topicId', 'questions'];

    switch (user.role) {
      case 'superAdmin':
        // Super admins can see all question papers with college information
        query = {};
        populateFields.push('collegeId', 'generatedBy');
        break;

      case 'collegeAdmin':
        // College admins can see all papers from their college with teacher information
        query = { collegeId: user.collegeId };
        populateFields.push('generatedBy');
        break;

      case 'teacher':
        // Teachers can only see their own papers
        query = { generatedBy: user._id };
        break;

      default:
        throw new BadRequestException('Invalid user role');
    }

    const questionPapers = await this.questionPaperModel.find(query).populate(populateFields).exec();

    // Transform sections to include full question objects instead of just IDs
    return questionPapers.map(questionPaper => {
      if (questionPaper && questionPaper.sections) {
        questionPaper.sections = questionPaper.sections.map(section => {
          const mappedQuestions = section.questions.map(sectionQuestion => {
            const fullQuestion = questionPaper.questions.find(
              q => {
                // Add proper type checking and handling
                const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
                const qIdStr = qId ? qId.toString() : '';
                const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
                return qIdStr === sectionQuestionIdStr;
              }
            );
            return {
              questionId: sectionQuestion.questionId,
              order: sectionQuestion.order,
              question: fullQuestion // Add this as an additional property
            };
          });
          
          return {
            ...section,
            questions: mappedQuestions
          };
        });
      }
      return questionPaper;
    });
  }

  async findOne(id: string, user: any) {
    const query: any = { _id: id };
    const populateFields = ['subjectId', 'topicId', 'questions'];

    switch (user.role) {
      case 'superAdmin':
        // Super admins can access any question paper
        populateFields.push('collegeId', 'generatedBy');
        break;

      case 'collegeAdmin':
        // College admins can access papers from their college
        query.collegeId = user.collegeId;
        populateFields.push('generatedBy');
        break;

      case 'teacher':
        // Teachers can only access their own papers
        query.generatedBy = user._id;
        break;

      default:
        throw new BadRequestException('Invalid user role');
    }

    const questionPaper = await this.questionPaperModel
      .findOne(query)
      .populate(populateFields)
      .exec();

    if (!questionPaper) {
      throw new NotFoundException(
        `Question paper with ID ${id} not found or access denied`,
      );
    }

    // Transform sections to include full question objects instead of just IDs
    if (questionPaper && questionPaper.sections) {
      questionPaper.sections = questionPaper.sections.map(section => {
        const mappedQuestions = section.questions.map(sectionQuestion => {
          const fullQuestion = questionPaper.questions.find(
            q => {
              // Add proper type checking and handling
              const qId = q && typeof q === 'object' && '_id' in q ? q._id : q;
              const qIdStr = qId ? qId.toString() : '';
              const sectionQuestionIdStr = sectionQuestion.questionId ? sectionQuestion.questionId.toString() : '';
              return qIdStr === sectionQuestionIdStr;
            }
          );
          return {
            questionId: sectionQuestion.questionId,
            order: sectionQuestion.order,
            question: fullQuestion // Add this as an additional property
          };
        });
        
        return {
          ...section,
          questions: mappedQuestions
        };
      });
    }

    return questionPaper;
  }

  async update(
    id: string,
    updateQuestionPaperDto: UpdateQuestionPaperDto,
    user: any,
  ) {
    const questionPaper = await this.findOne(id, user);

    // Teachers can only update the title of their own question papers
    if (user.role === 'teacher') {
      // Only allow title updates for teachers
      const allowedFields = ['title'];
      const updateData: any = {};

      for (const field of allowedFields) {
        if (updateQuestionPaperDto[field] !== undefined) {
          updateData[field] = updateQuestionPaperDto[field];
        }
      }

      if (Object.keys(updateData).length === 0) {
        throw new BadRequestException(
          'Teachers can only update the title of question papers',
        );
      }

      return await this.questionPaperModel
        .findByIdAndUpdate(id, updateData, { new: true })
        .exec();
    }

    // Super admins can update any field
    return await this.questionPaperModel
      .findByIdAndUpdate(id, updateQuestionPaperDto, { new: true })
      .exec();
  }

  async checkDownloadLimits(user: any, questionPaper: any): Promise<void> {
    // Super admins are not subject to download limits
    if (user.role === 'superAdmin') {
      return;
    }

    // Check download limits for teachers and college admins
    if (user.collegeId) {
      // Check subject-specific limits first
      let limitDoc = await this.questionPaperModel
        .findOne({
          collegeId: user.collegeId,
          subjectId: questionPaper.subjectId,
          type: 'limit',
        })
        .exec();

      // If no subject-specific limits, check college-wide limits
      if (!limitDoc) {
        limitDoc = await this.questionPaperModel
          .findOne({
            collegeId: user.collegeId,
            type: 'limit',
            subjectId: { $exists: false },
          })
          .exec();
      }

      if (limitDoc?.maxDownloads) {
        // Use TrackingService to check download count
        const downloadStats =
          await this.trackingService.getTeacherDownloadStats(user._id, {
            startDate: new Date(
              new Date().getFullYear(),
              new Date().getMonth(),
              1,
            ), // Start of current month
            endDate: new Date(), // Current date
          });

        const totalDownloads = downloadStats.reduce(
          (sum, stat) => sum + stat.totalDownloads,
          0,
        );

        if (totalDownloads >= limitDoc.maxDownloads) {
          throw new BadRequestException(
            `You have reached the maximum number of downloads (${limitDoc.maxDownloads}) allowed for this month`,
          );
        }
      }
    }
  }

  async download(
    id: string,
    format: 'pdf' | 'docx' = 'pdf',
    user?: any,
  ): Promise<string> {
    try {
      // First, check if user has access to this question paper
      const questionPaper = await this.findOne(id, user);

      // Check download limits
      if (user) {
        await this.checkDownloadLimits(user, questionPaper);
      }

      // Ensure uploads directory exists
      const uploadsDir = path.join(process.cwd(), 'uploads');
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }

      // Generate the file
      const fileName = `${questionPaper.title.replace(/\s+/g, '_')}_${Date.now()}.${format}`;
      const filePath = path.join(uploadsDir, fileName);

      if (format === 'pdf') {
        await this.generatePDF(questionPaper, filePath);
      } else if (format === 'docx') {
        await this.generateDOCX(questionPaper, filePath);
      } else {
        throw new BadRequestException(`Unsupported format: ${format}`);
      }

      this.logger.log(`Generated ${format.toUpperCase()} file at: ${filePath}`);
      return filePath;
    } catch (error) {
      this.logger.error(
        `Error generating ${format} file: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async setQuestionLimit(
    setQuestionLimitDto: SetQuestionLimitDto,
  ): Promise<void> {
    const updateData: any = {};

    // Handle backward compatibility
    if (setQuestionLimitDto.maxQuestions) {
      updateData.maxQuestions = setQuestionLimitDto.maxQuestions;
    }

    if (setQuestionLimitDto.maxGeneration) {
      updateData.maxGeneration = setQuestionLimitDto.maxGeneration;
    }

    if (setQuestionLimitDto.maxDownloads) {
      updateData.maxDownloads = setQuestionLimitDto.maxDownloads;
    }

    const query: any = {
      collegeId: setQuestionLimitDto.collegeId,
      type: 'limit',
    };

    // If subjectId is provided, set subject-specific limits, otherwise college-wide limits
    if (setQuestionLimitDto.subjectId) {
      query.subjectId = setQuestionLimitDto.subjectId;
    }

    await this.questionPaperModel
      .updateOne(query, { $set: updateData }, { upsert: true })
      .exec();
  }

  /**
   * Helper method to detect if a string is a base64 image
   */
  private isBase64Image(str: string): boolean {
    if (!str || typeof str !== 'string') return false;

    // Check for data URL format
    const dataUrlPattern = /^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i;
    if (dataUrlPattern.test(str)) return true;

    // Check for raw base64 (without data URL prefix)
    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
    return str.length > 100 && base64Pattern.test(str);
  }

  /**
   * Helper method to extract images from text content
   */
  private extractImagesFromText(text: string, addPlaceholders: boolean = true): {
    cleanText: string;
    images: Array<{ id: string; data: Buffer; format: string }>;
  } {
    if (!text) return { cleanText: text, images: [] };

    const images: Array<{ id: string; data: Buffer; format: string }> = [];
    let cleanText = text;
    let imageCounter = 0;

    // Look for data URLs
    const dataUrlPattern = /data:image\/([^;]+);base64,([A-Za-z0-9+/]+=*)/g;
    let match;

    while ((match = dataUrlPattern.exec(text)) !== null) {
      const [fullMatch, format, base64Data] = match;
      try {
        const buffer = Buffer.from(base64Data, 'base64');
        images.push({
          id: `image_${imageCounter++}`,
          data: buffer,
          format: format.toLowerCase()
        });

        // Replace the base64 string with a placeholder or remove it
        if (addPlaceholders) {
          cleanText = cleanText.replace(fullMatch, `[Image ${imageCounter}]`);
        } else {
          cleanText = cleanText.replace(fullMatch, '');
        }
      } catch (error) {
        this.logger.warn(`Failed to process base64 image: ${error.message}`);
      }
    }

    // Look for standalone base64 strings (without data URL prefix)
    const base64Pattern = /[A-Za-z0-9+/]{100,}={0,2}/g;
    const base64Matches = cleanText.match(base64Pattern);

    if (base64Matches) {
      base64Matches.forEach((base64String) => {
        if (this.isBase64Image(base64String)) {
          try {
            const buffer = Buffer.from(base64String, 'base64');
            // Try to determine format from buffer header
            let format = 'jpeg'; // default
            if (buffer[0] === 0x89 && buffer[1] === 0x50) format = 'png';
            else if (buffer[0] === 0xFF && buffer[1] === 0xD8) format = 'jpeg';
            else if (buffer[0] === 0x47 && buffer[1] === 0x49) format = 'gif';

            images.push({
              id: `image_${imageCounter++}`,
              data: buffer,
              format
            });

            // Replace the base64 string with a placeholder or remove it
            if (addPlaceholders) {
              cleanText = cleanText.replace(base64String, `[Image ${imageCounter}]`);
            } else {
              cleanText = cleanText.replace(base64String, '');
            }
          } catch (error) {
            this.logger.warn(`Failed to process standalone base64 image: ${error.message}`);
          }
        }
      });
    }

    // Clean up extra whitespace
    cleanText = cleanText.replace(/\s+/g, ' ').trim();

    return { cleanText, images };
  }

  private async generatePDF(
    questionPaper: QuestionPaper,
    filePath: string,
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument();
        const stream = fs.createWriteStream(filePath);

        doc.pipe(stream);

        // Add title
        doc.fontSize(20).text(questionPaper.title, { align: 'center' });
        doc.moveDown();

        // Add subject information if available
        if (
          questionPaper.subjectId &&
          typeof questionPaper.subjectId === 'object' &&
          'name' in questionPaper.subjectId
        ) {
          doc
            .fontSize(14)
            .text(`Subject: ${(questionPaper.subjectId as any).name}`, {
              align: 'center',
            });
          doc.moveDown();
        }

        // Add instructions if any
        if (questionPaper.instructions) {
          doc.fontSize(12).text('Instructions:', { underline: true });
          doc.fontSize(10).text(questionPaper.instructions);
          doc.moveDown();
        }

        // Add duration and total marks
        doc.fontSize(12).text(`Duration: ${questionPaper.duration} minutes`);
        doc.text(`Total Marks: ${questionPaper.totalMarks}`);
        doc.moveDown();

        // Add sections and questions
        doc.fontSize(14).text('Questions:');
        doc.moveDown();

        // Add questions
        questionPaper.questions.forEach((question: any, index: number) => {
          // Process question content for images (no placeholders for PDF)
          const questionResult = this.extractImagesFromText(question.content, false);

          // Add question text
          doc.fontSize(10).text(`${index + 1}. ${questionResult.cleanText}`);

          // Add question images if any
          questionResult.images.forEach((image) => {
            try {
              doc.moveDown(0.5);
              // Check if we need a new page for the image
              if (doc.y + 80 > doc.page.height - 50) {
                doc.addPage();
              }

              const currentY = doc.y;
              doc.image(image.data, doc.x, currentY, {
                fit: [120, 60]
              });

              // Move cursor below the image
              doc.y = currentY + 70; // Image height + padding
              doc.moveDown(0.5);
            } catch (error) {
              this.logger.warn(`Failed to add question image: ${error.message}`);
              doc.fontSize(8).fillColor('red').text(`[Image could not be displayed]`).fillColor('black');
            }
          });

          // Also handle imageUrls field if present
          if (question.imageUrls && Array.isArray(question.imageUrls) && question.imageUrls.length > 0) {
            question.imageUrls.forEach((imageUrl: string) => {
              if (this.isBase64Image(imageUrl)) {
                try {
                  const imageResult = this.extractImagesFromText(imageUrl, false);
                  imageResult.images.forEach((image) => {
                    doc.moveDown(0.5);
                    // Check if we need a new page for the image
                    if (doc.y + 80 > doc.page.height - 50) {
                      doc.addPage();
                    }

                    const currentY = doc.y;
                    doc.image(image.data, doc.x, currentY, {
                      fit: [120, 60]
                    });

                    // Move cursor below the image
                    doc.y = currentY + 70; // Image height + padding
                    doc.moveDown(0.5);
                  });
                } catch (error) {
                  this.logger.warn(`Failed to add imageUrl image: ${error.message}`);
                  doc.fontSize(8).fillColor('red').text(`[Image could not be displayed]`).fillColor('black');
                }
              } else {
                // For regular URLs, just show the URL
                doc.fontSize(8).fillColor('blue').text(`Image: ${imageUrl}`).fillColor('black');
                doc.moveDown(0.2);
              }
            });
          }

          // Add options if available
          if (
            question.options &&
            Array.isArray(question.options) &&
            question.options.length > 0
          ) {
            doc.moveDown(0.5);
            question.options.forEach((option: string, optIndex: number) => {
              const optionLabel = String.fromCharCode(97 + optIndex); // a, b, c, d...

              // Process option content for images (no placeholders for PDF)
              const optionResult = this.extractImagesFromText(option, false);

              // Add option text
              doc.fontSize(9).text(`    ${optionLabel}) ${optionResult.cleanText}`);

              // Add option images if any
              optionResult.images.forEach((image) => {
                try {
                  doc.moveDown(0.3);
                  // Check if we need a new page for the image
                  if (doc.y + 70 > doc.page.height - 50) {
                    doc.addPage();
                  }

                  // Calculate position with indent
                  const currentX = doc.x + 20;
                  const currentY = doc.y;

                  doc.image(image.data, currentX, currentY, {
                    fit: [100, 50]
                  });

                  // Move cursor below the image
                  doc.y = currentY + 60; // Image height + padding
                  doc.moveDown(0.3);
                } catch (error) {
                  this.logger.warn(`Failed to add option image: ${error.message}`);
                  doc.fontSize(8).fillColor('red').text(`        [Image could not be displayed]`).fillColor('black');
                }
              });
            });
          }

          // Add answer if withAnswers is true
          if (questionPaper.withAnswers && question.answer) {
            doc.moveDown(0.3);

            // Process answer content for images
            const answerResult = this.extractImagesFromText(question.answer);

            doc
              .fillColor('blue')
              .fontSize(9)
              .text(`    Answer: ${answerResult.cleanText}`)
              .fillColor('black');

            // Add answer images if any
            answerResult.images.forEach((image) => {
              try {
                doc.moveDown(0.3);
                // Check if we need a new page for the image
                if (doc.y + 70 > doc.page.height - 50) {
                  doc.addPage();
                }

                // Calculate position with indent
                const currentX = doc.x + 20;
                const currentY = doc.y;

                doc.image(image.data, currentX, currentY, {
                  fit: [100, 50]
                });

                // Move cursor below the image
                doc.y = currentY + 60; // Image height + padding
                doc.moveDown(0.3);
              } catch (error) {
                this.logger.warn(`Failed to add answer image: ${error.message}`);
                doc.fontSize(8).fillColor('red').text(`        [Image could not be displayed]`).fillColor('black');
              }
            });
          }

          doc.moveDown();
        });

        doc.end();

        stream.on('finish', () => {
          resolve();
        });

        stream.on('error', (err) => {
          reject(err);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  private async generateDOCX(
    questionPaper: QuestionPaper,
    filePath: string,
  ): Promise<void> {
    try {
      // Create document sections
      const sections: Array<{
        properties: Record<string, unknown>;
        children: Paragraph[];
      }> = [];

      // Title section
      sections.push({
        properties: {},
        children: [
          new Paragraph({
            text: questionPaper.title,
            heading: HeadingLevel.HEADING_1,
            alignment: AlignmentType.CENTER,
          }),
        ],
      });

      // Subject information if available
      if (
        questionPaper.subjectId &&
        typeof questionPaper.subjectId === 'object' &&
        'name' in questionPaper.subjectId
      ) {
        sections[0].children.push(
          new Paragraph({
            text: `Subject: ${(questionPaper.subjectId as any).name}`,
            alignment: AlignmentType.CENTER,
          }),
        );
      }

      // Metadata section (duration, marks)
      sections[0].children.push(
        new Paragraph({
          text: `Duration: ${questionPaper.duration} minutes | Total Marks: ${questionPaper.totalMarks}`,
          alignment: AlignmentType.LEFT,
          spacing: {
            before: 400,
            after: 200,
          },
        }),
      );

      // Instructions section if available
      if (questionPaper.instructions) {
        sections[0].children.push(
          new Paragraph({
            text: 'Instructions:',
            heading: HeadingLevel.HEADING_2,
          }),
          new Paragraph({
            text: questionPaper.instructions,
            spacing: {
              after: 200,
            },
          }),
        );
      }

      // Questions section
      sections[0].children.push(
        new Paragraph({
          text: 'Questions:',
          heading: HeadingLevel.HEADING_2,
          spacing: {
            before: 200,
            after: 200,
          },
        }),
      );

      // Add each question
      questionPaper.questions.forEach((question: any, index: number) => {
        // Process question content for images
        const questionResult = this.extractImagesFromText(question.content);

        // Question text
        sections[0].children.push(
          new Paragraph({
            text: `${index + 1}. ${questionResult.cleanText}`,
            spacing: {
              before: 200,
              after: 100,
            },
          }),
        );

        // Add note about images if any were found
        if (questionResult.images.length > 0) {
          sections[0].children.push(
            new Paragraph({
              text: `    [${questionResult.images.length} image(s) - see PDF version for images]`,
              spacing: {
                after: 100,
              },
            }),
          );
        }

        // Add options if available
        if (
          question.options &&
          Array.isArray(question.options) &&
          question.options.length > 0
        ) {
          question.options.forEach((option: string, optIndex: number) => {
            const optionLabel = String.fromCharCode(97 + optIndex); // a, b, c, d...

            // Process option content for images
            const optionResult = this.extractImagesFromText(option);

            sections[0].children.push(
              new Paragraph({
                text: `    ${optionLabel}) ${optionResult.cleanText}`,
                indent: {
                  left: 720, // 0.5 inches in twips
                },
              }),
            );

            // Add note about images if any were found
            if (optionResult.images.length > 0) {
              sections[0].children.push(
                new Paragraph({
                  text: `        [${optionResult.images.length} image(s) - see PDF version for images]`,
                  indent: {
                    left: 720,
                  },
                }),
              );
            }
          });
        }

        // Add answer if withAnswers is true
        if (questionPaper.withAnswers && question.answer) {
          // Process answer content for images (no placeholders for PDF)
          const answerResult = this.extractImagesFromText(question.answer, false);

          sections[0].children.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: `    Answer: ${answerResult.cleanText}`,
                  color: '0000FF', // Blue color
                  bold: true,
                }),
              ],
              indent: {
                left: 720, // 0.5 inches in twips
              },
            }),
          );

          // Add note about images if any were found
          if (answerResult.images.length > 0) {
            sections[0].children.push(
              new Paragraph({
                text: `        [${answerResult.images.length} image(s) - see PDF version for images]`,
                indent: {
                  left: 720,
                },
              }),
            );
          }
        }
      });

      // Create the document
      const doc = new DocxDocument({
        sections,
      });

      // Write the document to a file
      const buffer = await Packer.toBuffer(doc);
      fs.writeFileSync(filePath, buffer);
    } catch (error) {
      this.logger.error(`Error generating DOCX: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Resolve subject from either ObjectId or shortcode
   */
  private async resolveSubject(subjectInput: string): Promise<any> {
    // First try to find by ObjectId (if it's a valid ObjectId)
    if (Types.ObjectId.isValid(subjectInput)) {
      const subject = await this.subjectModel.findById(subjectInput);
      if (subject) return subject;
    }

    // Try to find by shortcode
    const subject = await this.findSubjectByShortCode(
      subjectInput as SubjectShortCode,
    );
    if (subject) return subject;

    // Try to find by exact name match
    const exactMatch = await this.subjectModel.findOne({
      name: { $regex: new RegExp(`^${subjectInput}$`, 'i') },
    });
    if (exactMatch) return exactMatch;

    return null;
  }

  /**
   * Find subject by short code (phy, chem, bio, math, etc.)
   */
  private async findSubjectByShortCode(
    shortCode: SubjectShortCode,
  ): Promise<any> {
    const subjectMappings = {
      [SubjectShortCode.PHYSICS]: ['physics', 'phy'],
      [SubjectShortCode.CHEMISTRY]: ['chemistry', 'chem'],
      [SubjectShortCode.BIOLOGY]: ['biology', 'bio'],
      [SubjectShortCode.MATHEMATICS]: ['mathematics', 'math'],
      [SubjectShortCode.MATH]: ['mathematics', 'math'],
      [SubjectShortCode.PHY]: ['physics', 'phy'],
      [SubjectShortCode.CHEM]: ['chemistry', 'chem'],
      [SubjectShortCode.BIO]: ['biology', 'bio'],
    };

    const searchTerms = subjectMappings[shortCode] || [shortCode];

    for (const term of searchTerms) {
      const subject = await this.subjectModel.findOne({
        name: { $regex: new RegExp(term, 'i') },
      });
      if (subject) return subject;
    }

    return null;
  }

  /**
   * Record question usage for permanent tracking
   */
  private async recordQuestionUsage(
    questionPaper: any,
    selectedQuestions: any[],
    user: any,
  ): Promise<void> {
    try {
      const usageData = selectedQuestions.map((question) => ({
        collegeId: user.collegeId,
        questionId: question._id.toString(),
        questionPaperId: questionPaper._id.toString(),
        usedBy: user._id,
        subjectId: questionPaper.subjectId.toString(),
        topicId: questionPaper.topicId?.toString(),
        metadata: {
          examType: questionPaper.examType,
          difficulty: question.difficulty,
          marks: question.marks || 1,
        },
      }));

      const result =
        await this.questionUsageService.recordMultipleQuestionUsage(usageData);
      this.logger.log(
        `Recorded question usage: ${result.recorded} recorded, ${result.skipped} skipped for paper ${questionPaper._id}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to record question usage for paper ${questionPaper._id}: ${error.message}`,
        error.stack,
      );
      // Don't throw error here to avoid breaking question paper creation
    }
  }

  /**
   * Check generation limits for teachers
   */
  private async checkGenerationLimits(
    user: any,
    subjectId: string,
  ): Promise<void> {
    const limitDoc = await this.questionPaperModel
      .findOne({
        collegeId: user.collegeId,
        subjectId,
        type: 'limit',
      })
      .exec();

    if (limitDoc?.maxQuestions) {
      const existingPapers = await this.questionPaperModel.countDocuments({
        generatedBy: user._id,
        collegeId: user.collegeId,
        subjectId,
        type: { $ne: 'limit' },
      });

      if (existingPapers >= limitDoc.maxQuestions) {
        throw new BadRequestException(
          `You have reached the maximum number of question papers (${limitDoc.maxQuestions}) allowed for this subject`,
        );
      }
    }
  }

  /**
   * Filter out permanently used questions for the college
   */
  private async filterUnusedQuestions(
    questions: any[],
    user: any,
    subjectId: string,
    topicId?: string,
  ): Promise<any[]> {
    // For super admins, don't apply permanent filtering (they can reuse questions)
    if (user.role === 'superAdmin') {
      return questions;
    }

    // For teachers and college admins, use permanent question usage tracking
    if (user.collegeId) {
      const availableQuestionIds = questions.map((q) => q._id.toString());

      const unusedQuestionIds =
        await this.questionUsageService.getUnusedQuestions(
          user.collegeId,
          availableQuestionIds,
          {
            subjectId,
            ...(topicId && { topicId }),
          },
        );

      return questions.filter((q) =>
        unusedQuestionIds.includes(q._id.toString()),
      );
    }

    // Fallback to old logic if no collegeId (shouldn't happen for teachers)
    const usedQuestions = await this.questionPaperModel.distinct('questions', {
      collegeId: user.collegeId,
      subjectId,
      ...(topicId && { topicId }),
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
    });

    return questions.filter(
      (q) =>
        !usedQuestions.map((id) => id.toString()).includes(q._id.toString()),
    );
  }

  /**
   * Select questions based on difficulty mode
   */
  private async selectQuestionsByDifficulty(
    questions: any[],
    numberOfQuestions: number,
    difficultyMode: string,
    customDifficulty?: any,
  ): Promise<any[]> {
    let easyPercentage = 30;
    let mediumPercentage = 50;
    let hardPercentage = 20;

    if (difficultyMode === 'custom' && customDifficulty) {
      easyPercentage = customDifficulty.easyPercentage;
      mediumPercentage = customDifficulty.mediumPercentage;
      hardPercentage = customDifficulty.hardPercentage;
    }

    // Calculate number of questions for each difficulty
    const easyCount = Math.round((numberOfQuestions * easyPercentage) / 100);
    const mediumCount = Math.round(
      (numberOfQuestions * mediumPercentage) / 100,
    );
    const hardCount = numberOfQuestions - easyCount - mediumCount;

    // Separate questions by difficulty
    const easyQuestions = questions.filter(
      (q) => (q.difficulty || 'medium') === 'easy',
    );
    const mediumQuestions = questions.filter(
      (q) => (q.difficulty || 'medium') === 'medium',
    );
    const hardQuestions = questions.filter(
      (q) => (q.difficulty || 'medium') === 'hard',
    );

    // Randomly select questions from each difficulty level
    const selectedQuestions: any[] = [];

    // Select easy questions
    const shuffledEasy = easyQuestions.sort(() => Math.random() - 0.5);
    selectedQuestions.push(
      ...shuffledEasy.slice(0, Math.min(easyCount, shuffledEasy.length)),
    );

    // Select medium questions
    const shuffledMedium = mediumQuestions.sort(() => Math.random() - 0.5);
    selectedQuestions.push(
      ...shuffledMedium.slice(0, Math.min(mediumCount, shuffledMedium.length)),
    );

    // Select hard questions
    const shuffledHard = hardQuestions.sort(() => Math.random() - 0.5);
    selectedQuestions.push(
      ...shuffledHard.slice(0, Math.min(hardCount, shuffledHard.length)),
    );

    // If we don't have enough questions of specific difficulties, fill with any available
    if (selectedQuestions.length < numberOfQuestions) {
      const remainingQuestions = questions.filter(
        (q) =>
          !selectedQuestions
            .map((sq) => sq._id.toString())
            .includes(q._id.toString()),
      );
      const shuffledRemaining = remainingQuestions.sort(
        () => Math.random() - 0.5,
      );
      selectedQuestions.push(
        ...shuffledRemaining.slice(
          0,
          numberOfQuestions - selectedQuestions.length,
        ),
      );
    }

    return selectedQuestions.slice(0, numberOfQuestions);
  }
}
