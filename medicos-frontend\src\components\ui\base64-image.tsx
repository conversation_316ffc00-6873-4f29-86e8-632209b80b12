import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { validateBase64ImageSrc } from '@/utils/imageUtils';

interface Base64ImageProps {
  src: string;
  alt?: string;
  className?: string;
  maxWidth?: number;
  maxHeight?: number;
  onClick?: () => void;
}

export function Base64Image({ 
  src, 
  alt = 'Image', 
  className, 
  maxWidth = 300, 
  maxHeight = 200,
  onClick 
}: Base64ImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const validatedSrc = validateBase64ImageSrc(src);

  if (!validatedSrc || imageError) {
    return (
      <div 
        className={cn(
          "flex items-center justify-center bg-gray-100 border border-gray-200 rounded-md text-gray-500 text-sm",
          className
        )}
        style={{ maxWidth, maxHeight: Math.min(maxHeight, 100) }}
      >
        {imageError ? 'Failed to load image' : 'Invalid image'}
      </div>
    );
  }

  return (
    <div className={cn("relative inline-block", className)}>
      {isLoading && (
        <div 
          className="absolute inset-0 flex items-center justify-center bg-gray-100 border border-gray-200 rounded-md"
          style={{ maxWidth, maxHeight }}
        >
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        </div>
      )}
      <img
        src={validatedSrc}
        alt={alt}
        className={cn(
          "rounded-md border border-gray-200 object-contain",
          onClick && "cursor-pointer hover:opacity-80 transition-opacity",
          isLoading && "opacity-0"
        )}
        style={{ 
          maxWidth, 
          maxHeight,
          display: isLoading ? 'none' : 'block'
        }}
        onLoad={() => setIsLoading(false)}
        onError={() => {
          setImageError(true);
          setIsLoading(false);
        }}
        onClick={onClick}
      />
    </div>
  );
}
