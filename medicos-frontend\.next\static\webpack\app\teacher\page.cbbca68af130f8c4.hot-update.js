"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* harmony import */ var _steps_multi_subject_config_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./steps/multi-subject-config-step */ \"(app-pages-browser)/./src/components/teacher/steps/multi-subject-config-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    paperMode: \"single\",\n    course: \"\",\n    subject: \"\",\n    subjects: [],\n    subjectConfigs: {},\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easyPercentage: 30,\n        mediumPercentage: 50,\n        hardPercentage: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const goToFirstStep = ()=>{\n        setCurrentStep(0);\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                setIsGenerating(false);\n                alert(\"Please enter a title for the question paper\");\n                return;\n            }\n            if (!formData.questionType) {\n                setIsGenerating(false);\n                alert(\"Please select an exam type\");\n                return;\n            }\n            // Validate based on paper mode\n            if (formData.paperMode === \"single\") {\n                if (!formData.subject) {\n                    setIsGenerating(false);\n                    alert(\"Please select a subject\");\n                    return;\n                }\n            } else {\n                if (formData.subjects.length === 0) {\n                    setIsGenerating(false);\n                    alert(\"Please select at least one subject\");\n                    return;\n                }\n            }\n            // Prepare the API payload\n            let apiPayload;\n            if (formData.paperMode === \"single\") {\n                // Single subject mode\n                apiPayload = {\n                    title: formData.title,\n                    description: formData.description,\n                    subject: formData.subject,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    examType: formData.questionType,\n                    instructions: formData.instructions,\n                    topicId: formData.topicId\n                };\n                // Add customization if not auto mode\n                if (formData.difficultyMode === \"custom\") {\n                    apiPayload.customise = {\n                        customDifficulty: formData.difficultyLevels,\n                        numberOfQuestions: formData.numberOfQuestions,\n                        totalMarks: formData.totalMarks,\n                        duration: formData.duration,\n                        includeAnswers: formData.includeAnswers\n                    };\n                } else {\n                    // For auto mode, still include customization with default values\n                    apiPayload.customise = {\n                        customDifficulty: {\n                            easyPercentage: 30,\n                            mediumPercentage: 50,\n                            hardPercentage: 20\n                        },\n                        numberOfQuestions: formData.numberOfQuestions,\n                        totalMarks: formData.totalMarks,\n                        duration: formData.duration,\n                        includeAnswers: formData.includeAnswers\n                    };\n                }\n            } else {\n                // Multi-subject mode\n                const subjects = formData.subjects.map((subjectName)=>{\n                    const config = formData.subjectConfigs[subjectName];\n                    return {\n                        subject: subjectName,\n                        numberOfQuestions: config.numberOfQuestions,\n                        totalMarks: config.totalMarks,\n                        customDifficulty: config.difficultyLevels,\n                        topicId: config.topicId\n                    };\n                });\n                apiPayload = {\n                    title: formData.title,\n                    description: formData.description,\n                    duration: formData.duration,\n                    examType: formData.questionType,\n                    instructions: formData.instructions,\n                    subjects: subjects,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            console.log(\"Calling createQuestionPaper API...\");\n            const result = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"API result:\", result);\n            // Check if the request was successful\n            if (!result.success) {\n                console.log(\"API returned error:\", result.error);\n                setIsGenerating(false);\n                let errorMessage = result.error;\n                // Handle specific error types with better messages\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                    errorMessage = \"Please log in again to continue. Your session may have expired.\";\n                } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                    errorMessage = \"Please check your internet connection and try again.\";\n                } else if (errorMessage.includes(\"unused questions available\")) {\n                    // Extract numbers from the error message for a clearer explanation\n                    const match = errorMessage.match(/Only (\\d+) unused questions available\\. Requested: (\\d+)/);\n                    if (match) {\n                        const available = match[1];\n                        const requested = match[2];\n                        errorMessage = \"Only \".concat(available, \" questions are available for this subject/topic, but you requested \").concat(requested, \" questions. Please reduce the number of questions or add more questions to the database.\");\n                    }\n                }\n                // Show error message in alert\n                alert(\"Error: \".concat(errorMessage));\n                console.log(\"Staying on current step due to error\");\n                return; // Exit early on error\n            }\n            // Success - proceed with download\n            console.log(\"API success! Proceeding with download...\");\n            console.log(\"Full API response:\", result);\n            // The API response might have different structures, let's handle both\n            let questionPaper;\n            let questionPaperId;\n            if (result.data && result.data.questionPaper) {\n                // Structure: { data: { questionPaper: { _id: \"...\" } } }\n                questionPaper = result.data.questionPaper;\n                questionPaperId = questionPaper._id;\n                console.log(\"Using questionPaper from result.data.questionPaper\");\n            } else if (result.data && result.data._id) {\n                // Structure: { data: { _id: \"...\" } }\n                questionPaper = result.data;\n                questionPaperId = questionPaper._id;\n                console.log(\"Using questionPaper from result.data\");\n            } else if (result.questionPaper) {\n                // Structure: { questionPaper: { _id: \"...\" } }\n                questionPaper = result.questionPaper;\n                questionPaperId = questionPaper._id;\n                console.log(\"Using questionPaper from result.questionPaper\");\n            } else if (result._id) {\n                // Structure: { _id: \"...\" }\n                questionPaper = result;\n                questionPaperId = result._id;\n                console.log(\"Using questionPaper from result directly\");\n            }\n            console.log(\"Question paper object:\", questionPaper);\n            console.log(\"Question paper ID:\", questionPaperId);\n            // Validate that we have a question paper ID\n            if (!questionPaperId) {\n                console.error(\"No question paper ID found in response. Full response:\", result);\n                throw new Error(\"Question paper was created but no ID was returned. Please try again.\");\n            }\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaperId, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            alert(\"Question paper generated and downloaded successfully!\");\n            console.log(\"Success! About to redirect to first step in 1 second...\");\n            // Reset to first step and clear form data after a short delay (only on success)\n            setTimeout(()=>{\n                console.log(\"Redirecting to first step now...\");\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n                console.log(\"Redirect completed. Current step should be 0\");\n            }, 1000) // 1 second delay to ensure alert is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            // Handle any unexpected errors (like network issues)\n            alert(\"Error: An unexpected error occurred. Please try again.\");\n        }\n    };\n    // Build steps array dynamically based on paper mode\n    const buildSteps = ()=>{\n        const baseSteps = [\n            {\n                title: \"Question Type\",\n                icon: \"HelpCircle\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__.QuestionTypeStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 11\n                }, this)\n            },\n            {\n                title: \"Paper Details\",\n                icon: \"FileText\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__.QuestionTitleAndDescriptionStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 11\n                }, this)\n            },\n            {\n                title: \"Course & Subject Selection\",\n                icon: \"BookOpen\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__.CourseSubjectStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 11\n                }, this)\n            }\n        ];\n        // Add multi-subject configuration step if in multi-subject mode\n        if (formData.paperMode === \"multi\") {\n            baseSteps.push({\n                title: \"Configure Subjects\",\n                icon: \"Settings\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_multi_subject_config_step__WEBPACK_IMPORTED_MODULE_12__.MultiSubjectConfigStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 11\n                }, this)\n            });\n        }\n        // Add remaining steps only for single subject mode\n        if (formData.paperMode === \"single\") {\n            baseSteps.push({\n                title: \"Select Difficulty Level\",\n                icon: \"BarChart2\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__.DifficultyLevelStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 13\n                }, this)\n            }, {\n                title: \"Question Selection Criteria\",\n                icon: \"FileText\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__.QuestionSelectionStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 13\n                }, this)\n            }, {\n                title: \"Paper Customization\",\n                icon: \"FileEdit\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__.PaperCustomizationStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 414,\n                    columnNumber: 13\n                }, this)\n            });\n        }\n        // Add final steps for both modes\n        baseSteps.push({\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 433,\n                columnNumber: 11\n            }, this)\n        }, {\n            title: \"Generate Paper\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_9__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating,\n                onBack: goToFirstStep\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 446,\n                columnNumber: 20\n            }, this)\n        });\n        return baseSteps;\n    };\n    const steps = buildSteps();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 459,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 458,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"nel0TECL1Zv7CzoM26K8Hca8GSI=\");\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});