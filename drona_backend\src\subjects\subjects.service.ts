import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Subject, SubjectDocument } from '../schema/subject.schema';
import { Topic, TopicDocument } from '../schema/topic.schema';
import { CreateSubjectDto } from './dto/create-subject.dto';
import { UpdateSubjectDto } from './dto/update-subject.dto';

@Injectable()
export class SubjectsService {
  private readonly logger = new Logger(SubjectsService.name);

  constructor(
    @InjectModel(Subject.name) private subjectModel: Model<SubjectDocument>,
    @InjectModel(Topic.name) private topicModel: Model<TopicDocument>,
  ) {}

  async create(createSubjectDto: CreateSubjectDto): Promise<Subject> {
    try {
      // Check if subject with same name already exists
      const existingSubject = await this.subjectModel.findOne({
        name: { $regex: new RegExp(`^${createSubjectDto.name}$`, 'i') },
      });

      if (existingSubject) {
        throw new ConflictException(
          `Subject with name '${createSubjectDto.name}' already exists`,
        );
      }

      const createdSubject = new this.subjectModel(createSubjectDto);
      const savedSubject = await createdSubject.save();

      this.logger.log(
        `Subject created: ${savedSubject.name} (ID: ${savedSubject._id})`,
      );
      return savedSubject;
    } catch (error) {
      this.logger.error(
        `Failed to create subject: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findAll(): Promise<Subject[]> {
    try {
      return await this.subjectModel.find().sort({ name: 1 }).exec();
    } catch (error) {
      this.logger.error(
        `Failed to fetch subjects: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findAllWithTopics(): Promise<any[]> {
    try {
      const subjects = await this.subjectModel
        .aggregate([
          {
            $lookup: {
              from: 'topics',
              localField: '_id',
              foreignField: 'subjectId',
              as: 'topics',
            },
          },
          {
            $project: {
              _id: 1,
              name: 1,
              description: 1,
              topics: {
                $map: {
                  input: {
                    $sortArray: {
                      input: '$topics',
                      sortBy: { name: 1 },
                    },
                  },
                  as: 'topic',
                  in: {
                    _id: '$$topic._id',
                    name: '$$topic.name',
                    description: '$$topic.description',
                  },
                },
              },
            },
          },
          { $sort: { name: 1 } },
        ])
        .exec();

      return subjects;
    } catch (error) {
      this.logger.error(
        `Failed to fetch subjects with topics: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findOne(id: string): Promise<Subject> {
    try {
      const subject = await this.subjectModel.findById(id).exec();
      if (!subject) {
        throw new NotFoundException(`Subject with ID ${id} not found`);
      }
      return subject;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to fetch subject: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async update(
    id: string,
    updateSubjectDto: UpdateSubjectDto,
  ): Promise<Subject> {
    try {
      // Check if subject exists
      const existingSubject = await this.findOne(id);

      // Check if name is being updated and if it conflicts with another subject
      if (
        updateSubjectDto.name &&
        updateSubjectDto.name !== existingSubject.name
      ) {
        const conflictingSubject = await this.subjectModel.findOne({
          _id: { $ne: id },
          name: { $regex: new RegExp(`^${updateSubjectDto.name}$`, 'i') },
        });

        if (conflictingSubject) {
          throw new ConflictException(
            `Subject with name '${updateSubjectDto.name}' already exists`,
          );
        }
      }

      const updatedSubject = await this.subjectModel
        .findByIdAndUpdate(id, updateSubjectDto, {
          new: true,
          runValidators: true,
        })
        .exec();

      if (!updatedSubject) {
        throw new NotFoundException(`Subject with ID ${id} not found`);
      }

      this.logger.log(`Subject updated: ${updatedSubject.name} (ID: ${id})`);
      return updatedSubject;
    } catch (error) {
      this.logger.error(
        `Failed to update subject: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      // Check if subject exists
      await this.findOne(id);

      // Check if subject has associated topics
      const topicsCount = await this.topicModel.countDocuments({
        subjectId: id,
      });
      if (topicsCount > 0) {
        throw new ConflictException(
          `Cannot delete subject. It has ${topicsCount} associated topics. Please delete the topics first.`,
        );
      }

      await this.subjectModel.findByIdAndDelete(id).exec();
      this.logger.log(`Subject deleted: ID ${id}`);
    } catch (error) {
      this.logger.error(
        `Failed to delete subject: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
