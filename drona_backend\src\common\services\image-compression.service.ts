import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as sharp from 'sharp';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

export interface ImageCompressionOptions {
  maxSizeBytes?: number;
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
  format?: 'jpeg' | 'png' | 'webp';
  preserveAspectRatio?: boolean;
}

export interface CompressedImageResult {
  filename: string;
  path: string;
  url: string;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  width: number;
  height: number;
  format: string;
}

@Injectable()
export class ImageCompressionService {
  private readonly logger = new Logger(ImageCompressionService.name);
  private readonly uploadsDir: string;
  private readonly baseUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.uploadsDir = path.join(process.cwd(), 'uploads', 'images');
    this.baseUrl =
      this.configService.get<string>('BASE_URL') || 'http://localhost:3000';

    // Ensure uploads directory exists
    this.ensureUploadsDirectory();
  }

  /**
   * Compress and save image with target size of 2MB max
   */
  async compressAndSaveImage(
    file: Express.Multer.File,
    options: ImageCompressionOptions = {},
  ): Promise<CompressedImageResult> {
    try {
      // Default options for 2MB max compression with good quality
      const defaultOptions: ImageCompressionOptions = {
        maxSizeBytes: options.maxSizeBytes || 2 * 1024 * 1024, // 2MB max compression target
        quality: 85, // Good quality for 2MB target
        maxWidth: 1920, // Standard HD dimensions
        maxHeight: 1080,
        format: 'jpeg',
        preserveAspectRatio: true,
        ...options,
      };

      // Validate file type
      if (!this.isValidImageType(file.mimetype)) {
        throw new BadRequestException(
          'Invalid image type. Only JPEG, PNG, and WebP are allowed.',
        );
      }

      // Generate unique filename
      const fileExtension = this.getFileExtension(defaultOptions.format!);
      const filename = `${uuidv4()}${fileExtension}`;
      const outputPath = path.join(this.uploadsDir, filename);

      // Get original image info
      const originalSize = file.buffer.length;
      const imageInfo = await sharp(file.buffer).metadata();

      this.logger.log(
        `Processing image: ${file.originalname} (${this.formatBytes(originalSize)})`,
      );

      // Start with the original buffer
      let processedBuffer = file.buffer;
      let currentQuality = defaultOptions.quality!;
      let attempts = 0;
      const maxAttempts = 10;

      // Iteratively compress until we reach target size
      while (
        processedBuffer.length > defaultOptions.maxSizeBytes! &&
        attempts < maxAttempts
      ) {
        attempts++;

        let sharpInstance = sharp(file.buffer);

        // Resize if needed
        if (
          (imageInfo.width || 0) > defaultOptions.maxWidth! ||
          (imageInfo.height || 0) > defaultOptions.maxHeight!
        ) {
          sharpInstance = sharpInstance.resize(
            defaultOptions.maxWidth,
            defaultOptions.maxHeight,
            {
              fit: 'inside',
              withoutEnlargement: true,
            },
          );
        }

        // Apply format and quality
        switch (defaultOptions.format) {
          case 'jpeg':
            sharpInstance = sharpInstance.jpeg({ quality: currentQuality });
            break;
          case 'png':
            sharpInstance = sharpInstance.png({
              quality: currentQuality,
              compressionLevel: 9,
            });
            break;
          case 'webp':
            sharpInstance = sharpInstance.webp({ quality: currentQuality });
            break;
        }

        processedBuffer = await sharpInstance.toBuffer();

        // If still too large, reduce quality
        if (processedBuffer.length > defaultOptions.maxSizeBytes!) {
          currentQuality = Math.max(20, currentQuality - 10);
          this.logger.log(
            `Attempt ${attempts}: Size ${this.formatBytes(processedBuffer.length)}, reducing quality to ${currentQuality}`,
          );
        }
      }

      // Save the compressed image
      await fs.promises.writeFile(outputPath, processedBuffer);

      // Get final image metadata
      const finalMetadata = await sharp(processedBuffer).metadata();
      const compressedSize = processedBuffer.length;
      const compressionRatio =
        ((originalSize - compressedSize) / originalSize) * 100;

      const result: CompressedImageResult = {
        filename,
        path: outputPath,
        url: `${this.baseUrl}/uploads/images/${filename}`,
        originalSize,
        compressedSize,
        compressionRatio: Math.round(compressionRatio * 100) / 100,
        width: finalMetadata.width || 0,
        height: finalMetadata.height || 0,
        format: defaultOptions.format!,
      };

      this.logger.log(
        `Image compressed successfully: ${file.originalname} -> ${filename} ` +
          `(${this.formatBytes(originalSize)} -> ${this.formatBytes(compressedSize)}, ` +
          `${result.compressionRatio}% reduction)`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error compressing image: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Image compression failed: ${error.message}`,
      );
    }
  }

  /**
   * Compress image from base64 string
   */
  async compressBase64Image(
    base64Data: string,
    options: ImageCompressionOptions = {},
  ): Promise<CompressedImageResult> {
    try {
      // Remove data URL prefix if present
      const base64Clean = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
      const buffer = Buffer.from(base64Clean, 'base64');

      // Create a mock file object
      const mockFile: Express.Multer.File = {
        buffer,
        originalname: 'base64-image',
        mimetype: 'image/jpeg',
        size: buffer.length,
        fieldname: '',
        encoding: '',
        filename: '',
        path: '',
        destination: '',
        stream: null as any,
      };

      return this.compressAndSaveImage(mockFile, options);
    } catch (error) {
      this.logger.error(
        `Error compressing base64 image: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Base64 image compression failed: ${error.message}`,
      );
    }
  }

  /**
   * Delete an image file
   */
  async deleteImage(filename: string): Promise<boolean> {
    try {
      const filePath = path.join(this.uploadsDir, filename);

      if (await this.fileExists(filePath)) {
        await fs.promises.unlink(filePath);
        this.logger.log(`Deleted image: ${filename}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error deleting image ${filename}: ${error.message}`);
      return false;
    }
  }

  /**
   * Get image info without processing
   */
  async getImageInfo(file: Express.Multer.File): Promise<sharp.Metadata> {
    try {
      return await sharp(file.buffer).metadata();
    } catch (error) {
      throw new BadRequestException('Invalid image file');
    }
  }

  /**
   * Check if file type is valid image
   */
  private isValidImageType(mimetype: string): boolean {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    return validTypes.includes(mimetype.toLowerCase());
  }

  /**
   * Get file extension based on format
   */
  private getFileExtension(format: string): string {
    switch (format) {
      case 'jpeg':
        return '.jpg';
      case 'png':
        return '.png';
      case 'webp':
        return '.webp';
      default:
        return '.jpg';
    }
  }

  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Check if file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.promises.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Ensure uploads directory exists
   */
  private ensureUploadsDirectory(): void {
    try {
      if (!fs.existsSync(this.uploadsDir)) {
        fs.mkdirSync(this.uploadsDir, { recursive: true });
        this.logger.log(`Created uploads directory: ${this.uploadsDir}`);
      }
    } catch (error) {
      this.logger.error(`Error creating uploads directory: ${error.message}`);
    }
  }
}
