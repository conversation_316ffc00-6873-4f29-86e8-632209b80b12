import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Download, DownloadDocument } from '../../schema/download.schema';
import {
  UserActivity,
  UserActivityDocument,
} from '../../schema/user-activity.schema';
import {
  QuestionPaper,
  QuestionPaperDocument,
} from '../../schema/question-paper.schema';

export interface TrackDownloadDto {
  userId: string;
  paperId: string;
  collegeId?: string;
  downloadFormat: 'pdf' | 'docx';
  ipAddress?: string;
}

export interface TrackPaperGenerationDto {
  userId: string;
  paperId: string;
  collegeId?: string;
  subjectId: string;
  ipAddress?: string;
}

export interface TrackUserActivityDto {
  userId: string;
  collegeId?: string;
  activityType: 'login' | 'question_creation' | 'paper_generation';
  activityDetails?: any;
  ipAddress?: string;
}

@Injectable()
export class TrackingService {
  private readonly logger = new Logger(TrackingService.name);

  constructor(
    @InjectModel(Download.name) private downloadModel: Model<DownloadDocument>,
    @InjectModel(UserActivity.name)
    private userActivityModel: Model<UserActivityDocument>,
    @InjectModel(QuestionPaper.name)
    private questionPaperModel: Model<QuestionPaperDocument>,
  ) {}

  /**
   * Track a question paper download
   */
  async trackDownload(trackDownloadDto: TrackDownloadDto): Promise<void> {
    try {
      // Validate ObjectIds
      if (!Types.ObjectId.isValid(trackDownloadDto.userId)) {
        throw new Error('Invalid userId');
      }
      if (!Types.ObjectId.isValid(trackDownloadDto.paperId)) {
        throw new Error('Invalid paperId');
      }
      if (
        trackDownloadDto.collegeId &&
        !Types.ObjectId.isValid(trackDownloadDto.collegeId)
      ) {
        throw new Error('Invalid collegeId');
      }

      // Create download record
      const downloadData: any = {
        userId: new Types.ObjectId(trackDownloadDto.userId),
        paperId: new Types.ObjectId(trackDownloadDto.paperId),
        downloadFormat: trackDownloadDto.downloadFormat,
        downloadDate: new Date(),
      };

      if (trackDownloadDto.collegeId) {
        downloadData.collegeId = new Types.ObjectId(trackDownloadDto.collegeId);
      }

      const download = new this.downloadModel(downloadData);

      await download.save();

      this.logger.log(
        `Download tracked: User ${trackDownloadDto.userId} downloaded paper ${trackDownloadDto.paperId} in ${trackDownloadDto.downloadFormat} format`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to track download: ${error.message}`,
        error.stack,
      );
      // Don't throw error to avoid breaking the download process
    }
  }

  /**
   * Track question paper generation
   */
  async trackPaperGeneration(
    trackPaperGenerationDto: TrackPaperGenerationDto,
  ): Promise<void> {
    try {
      // Validate ObjectIds
      if (!Types.ObjectId.isValid(trackPaperGenerationDto.userId)) {
        throw new Error('Invalid userId');
      }
      if (
        trackPaperGenerationDto.collegeId &&
        !Types.ObjectId.isValid(trackPaperGenerationDto.collegeId)
      ) {
        throw new Error('Invalid collegeId');
      }

      // Create user activity record for paper generation
      const activityData: any = {
        userId: new Types.ObjectId(trackPaperGenerationDto.userId),
        activityType: 'paper_generation',
        activityDetails: {
          paperId: trackPaperGenerationDto.paperId,
          subjectId: trackPaperGenerationDto.subjectId,
        },
        timestamp: new Date(),
        ipAddress: trackPaperGenerationDto.ipAddress,
      };

      if (trackPaperGenerationDto.collegeId) {
        activityData.collegeId = new Types.ObjectId(
          trackPaperGenerationDto.collegeId,
        );
      }

      const activity = new this.userActivityModel(activityData);

      await activity.save();

      this.logger.log(
        `Paper generation tracked: User ${trackPaperGenerationDto.userId} generated paper ${trackPaperGenerationDto.paperId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to track paper generation: ${error.message}`,
        error.stack,
      );
      // Don't throw error to avoid breaking the paper generation process
    }
  }

  /**
   * Track general user activity
   */
  async trackUserActivity(
    trackUserActivityDto: TrackUserActivityDto,
  ): Promise<void> {
    try {
      // Validate ObjectIds
      if (!Types.ObjectId.isValid(trackUserActivityDto.userId)) {
        throw new Error('Invalid userId');
      }
      if (
        trackUserActivityDto.collegeId &&
        !Types.ObjectId.isValid(trackUserActivityDto.collegeId)
      ) {
        throw new Error('Invalid collegeId');
      }

      // Create user activity record
      const activityData: any = {
        userId: new Types.ObjectId(trackUserActivityDto.userId),
        activityType: trackUserActivityDto.activityType,
        activityDetails: trackUserActivityDto.activityDetails,
        timestamp: new Date(),
        ipAddress: trackUserActivityDto.ipAddress,
      };

      if (trackUserActivityDto.collegeId) {
        activityData.collegeId = new Types.ObjectId(
          trackUserActivityDto.collegeId,
        );
      }

      const activity = new this.userActivityModel(activityData);

      await activity.save();

      this.logger.log(
        `User activity tracked: User ${trackUserActivityDto.userId} performed ${trackUserActivityDto.activityType}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to track user activity: ${error.message}`,
        error.stack,
      );
      // Don't throw error to avoid breaking the main process
    }
  }

  /**
   * Get download statistics for a teacher
   */
  async getTeacherDownloadStats(
    teacherId: string,
    filters: {
      startDate?: Date;
      endDate?: Date;
      subjectId?: string;
    } = {},
  ) {
    try {
      if (!Types.ObjectId.isValid(teacherId)) {
        throw new Error('Invalid teacherId');
      }

      const matchQuery: any = {
        userId: new Types.ObjectId(teacherId),
      };

      // Add date filters
      if (filters.startDate || filters.endDate) {
        matchQuery.downloadDate = {};
        if (filters.startDate) {
          matchQuery.downloadDate.$gte = filters.startDate;
        }
        if (filters.endDate) {
          matchQuery.downloadDate.$lte = filters.endDate;
        }
      }

      const pipeline: any[] = [
        { $match: matchQuery },
        {
          $lookup: {
            from: 'questionpapers',
            localField: 'paperId',
            foreignField: '_id',
            as: 'questionPaper',
          },
        },
        { $unwind: '$questionPaper' },
      ];

      // Add subject filter if provided
      if (filters.subjectId && Types.ObjectId.isValid(filters.subjectId)) {
        pipeline.push({
          $match: {
            'questionPaper.subjectId': new Types.ObjectId(filters.subjectId),
          },
        });
      }

      pipeline.push(
        {
          $lookup: {
            from: 'subjects',
            localField: 'questionPaper.subjectId',
            foreignField: '_id',
            as: 'subject',
          },
        },
        { $unwind: '$subject' },
        {
          $group: {
            _id: {
              subjectId: '$subject._id',
              subjectName: '$subject.name',
              format: '$downloadFormat',
            },
            count: { $sum: 1 },
            lastDownload: { $max: '$downloadDate' },
          },
        },
        {
          $group: {
            _id: {
              subjectId: '$_id.subjectId',
              subjectName: '$_id.subjectName',
            },
            totalDownloads: { $sum: '$count' },
            formatBreakdown: {
              $push: {
                format: '$_id.format',
                count: '$count',
              },
            },
            lastDownload: { $max: '$lastDownload' },
          },
        },
        {
          $project: {
            _id: 0,
            subjectId: '$_id.subjectId',
            subjectName: '$_id.subjectName',
            totalDownloads: 1,
            formatBreakdown: 1,
            lastDownload: 1,
          },
        },
        { $sort: { subjectName: 1 } },
      );

      return await this.downloadModel.aggregate(pipeline).exec();
    } catch (error) {
      this.logger.error(
        `Failed to get teacher download stats: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get paper generation statistics for a teacher
   */
  async getTeacherPaperGenerationStats(
    teacherId: string,
    filters: {
      startDate?: Date;
      endDate?: Date;
      subjectId?: string;
    } = {},
  ) {
    try {
      if (!Types.ObjectId.isValid(teacherId)) {
        throw new Error('Invalid teacherId');
      }

      const matchQuery: any = {
        generatedBy: new Types.ObjectId(teacherId),
      };

      // Add date filters
      if (filters.startDate || filters.endDate) {
        matchQuery.createdAt = {};
        if (filters.startDate) {
          matchQuery.createdAt.$gte = filters.startDate;
        }
        if (filters.endDate) {
          matchQuery.createdAt.$lte = filters.endDate;
        }
      }

      // Add subject filter
      if (filters.subjectId && Types.ObjectId.isValid(filters.subjectId)) {
        matchQuery.subjectId = new Types.ObjectId(filters.subjectId);
      }

      const pipeline: any[] = [
        { $match: matchQuery },
        {
          $lookup: {
            from: 'subjects',
            localField: 'subjectId',
            foreignField: '_id',
            as: 'subject',
          },
        },
        { $unwind: '$subject' },
        {
          $group: {
            _id: {
              subjectId: '$subject._id',
              subjectName: '$subject.name',
            },
            totalPapers: { $sum: 1 },
            lastGenerated: { $max: '$createdAt' },
          },
        },
        {
          $project: {
            _id: 0,
            subjectId: '$_id.subjectId',
            subjectName: '$_id.subjectName',
            totalPapers: 1,
            lastGenerated: 1,
          },
        },
        { $sort: { subjectName: 1 } },
      ];

      return await this.questionPaperModel.aggregate(pipeline).exec();
    } catch (error) {
      this.logger.error(
        `Failed to get teacher paper generation stats: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
