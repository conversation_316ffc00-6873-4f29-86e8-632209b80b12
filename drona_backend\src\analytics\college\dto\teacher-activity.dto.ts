import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for teacher activity information
 */
export class TeacherActivityDto {
  @ApiProperty({
    description: 'Teacher display name',
    example: '<PERSON>',
  })
  displayName: string;

  @ApiProperty({
    description: 'Teacher email address',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Last login timestamp',
    example: '2023-07-21T15:30:00.000Z',
  })
  lastLogin: Date;

  @ApiProperty({
    description: 'Account creation timestamp',
    example: '2023-01-15T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Account status',
    example: 'active',
    enum: ['active', 'inactive'],
  })
  status: string;
}
