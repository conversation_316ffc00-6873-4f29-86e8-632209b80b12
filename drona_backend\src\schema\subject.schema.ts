// subject.schema.ts
import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export type SubjectDocument = Subject & Document;

@Schema()
export class Subject {
  @ApiProperty({
    description: 'Subject name',
    example: 'Mathematics',
    required: true,
  })
  @Prop({ required: true })
  name: string;

  @ApiPropertyOptional({
    description: 'Subject description',
    example: 'Study of numbers, quantities, and shapes',
  })
  @Prop()
  description: string;
}

export const SubjectSchema = SchemaFactory.createForClass(Subject);
