import { apiCall } from '../api';

export interface PlatformSummary {
  totalColleges: number;
  totalTeachers: number;
  totalQuestions: number;
  totalPapers: number;
  totalDownloads: number;
  recentActivity: {
    logins: number;
    paperGenerations: number;
    downloads: number;
  };
}

/**
 * Get platform summary statistics
 * @returns Platform summary data
 */
export const getPlatformSummary = async (): Promise<PlatformSummary> => {
  try {
    return await apiCall('/analytics/platform-summary');
  } catch (error: any) {
    console.error('Error fetching platform summary:', error);
    throw new Error(error.message || 'Failed to fetch platform summary');
  }
};

/**
 * Get top colleges by various metrics
 * @returns Top colleges data
 */
export const getTopColleges = async () => {
  try {
    return await apiCall('/analytics/top-colleges');
  } catch (error: any) {
    console.error('Error fetching top colleges:', error);
    throw new Error(error.message || 'Failed to fetch top colleges data');
  }
};

/**
 * Get question usage statistics
 * @returns Question usage data
 */
export const getQuestionUsage = async () => {
  try {
    return await apiCall('/analytics/question-usage');
  } catch (error: any) {
    console.error('Error fetching question usage:', error);
    throw new Error(error.message || 'Failed to fetch question usage data');
  }
};

/**
 * Get question statistics
 * @returns Question statistics data
 */
export const getQuestionStats = async () => {
  try {
    return await apiCall('/analytics/question-stats');
  } catch (error: any) {
    console.error('Error fetching question stats:', error);
    throw new Error(error.message || 'Failed to fetch question statistics');
  }
};