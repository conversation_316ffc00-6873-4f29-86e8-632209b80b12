// auth/auth.controller.ts
import {
  Controller,
  Post,
  Body,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { RegisterDto, UserRole } from './dto/register.dto';
import { AuthResponseDto, UserInfoDto } from './dto/auth-response.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';
import { Roles } from '../auth/guards/roles.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiUnauthorizedResponse,
  ApiConflictResponse,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiBadRequestResponse,
  ApiForbiddenResponse,
} from '@nestjs/swagger';

@ApiTags('Authentication')
// @ApiBearerAuth('JWT-auth')
@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  /**
   * Login endpoint
   * @param loginDto Login credentials
   * @returns JWT token and user information
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'User login',
    description:
      'Authenticate a user and return a JWT token. Either email/password or Firebase token must be provided.',
  })
  @ApiBody({ type: LoginDto })
  @ApiOkResponse({
    description: 'User successfully logged in',
    type: AuthResponseDto,
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized - Invalid credentials',
  })
  @ApiBadRequestResponse({
    description: 'Bad request - Invalid input data',
  })
  async login(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
    return this.authService.login(loginDto);
  }

  /**
   * Registration endpoint
   * @param registerDto Registration data
   * @returns JWT token and user information
   */
  @Post('register')
  @ApiOperation({
    summary: 'User registration',
    description:
      "Register a new user. Email must be pre-authorized in a college's teachers array. Returns 400 if email not authorized, 409 if user already registered.",
  })
  @ApiBody({ type: RegisterDto })
  @ApiCreatedResponse({
    description: 'User successfully registered',
    type: AuthResponseDto,
  })
  @ApiBadRequestResponse({
    description:
      'Bad request - Email not authorized as teacher or invalid input data',
  })
  @ApiConflictResponse({
    description: 'Conflict - User is already registered',
  })
  async register(@Body() registerDto: RegisterDto): Promise<AuthResponseDto> {
    return this.authService.register(registerDto);
  }

  /**
   * Register teacher endpoint (admin only)
   * @param registerDto Registration data for teacher
   * @returns JWT token and user information
   */
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('superAdmin', 'collegeAdmin')
  @Post('register-teacher')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Register a teacher',
    description:
      "Register a new teacher (admin only). Role will be automatically set to teacher. Email must be pre-authorized in a college's teachers array.",
  })
  @ApiBody({ type: RegisterDto })
  @ApiCreatedResponse({
    description: 'Teacher successfully registered',
    type: AuthResponseDto,
  })
  @ApiBadRequestResponse({
    description:
      'Bad request - Email not authorized as teacher or invalid input data',
  })
  @ApiForbiddenResponse({
    description: 'Forbidden - Insufficient permissions',
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized - Invalid token',
  })
  @ApiConflictResponse({
    description: 'Conflict - User is already registered',
  })
  async registerTeacher(
    @Body() registerDto: RegisterDto,
  ): Promise<AuthResponseDto> {
    // Force role to be teacher, only admins can register teachers
    registerDto.role = UserRole.TEACHER;
    return this.authService.register(registerDto);
  }

  /**
   * Verify JWT token endpoint
   * @param req Request with user information from JWT token
   * @returns User information from token
   */
  @UseGuards(JwtAuthGuard)
  @Post('verify')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Verify JWT token',
    description:
      'Verify the validity of a JWT token and return the user information',
  })
  @ApiOkResponse({
    description: 'Token is valid',
    schema: {
      type: 'object',
      properties: {
        user: {
          type: 'object',
          properties: {
            sub: { type: 'string', example: '60d21b4667d0d8992e610c85' },
            email: { type: 'string', example: '<EMAIL>' },
            role: { type: 'string', example: 'teacher' },
            collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({
    description: 'Unauthorized - Invalid token',
  })
  async verifyToken(@Req() req): Promise<any> {
    return { user: req.user };
  }
}
