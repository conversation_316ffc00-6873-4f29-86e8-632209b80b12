<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Global</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Global</h1>

    




<section>

<header>
    
        <h2></h2>
        
    
</header>

<article>
    <div class="container-overview">
    
        

        


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
    
    </div>

    

    

    

    

    

    

    

    

    
        <h3 class="subsection-title">Type Definitions</h3>

        
                

    

    
    <h4 class="name" id="MathComponentProps"><span class="type-signature"></span>MathComponentProps<span class="signature">(error)</span><span class="type-signature"> &rarr; {ReactNode}</span></h4>
    

    











    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>error</code></td>
            

            <td class="type">
            
                
<span class="param-type">Error</span>


            
            </td>

            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>






    <h5 class="subsection-title">Properties:</h5>

    

<table class="props">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        
        <th>Attributes</th>
        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>math</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>children</code></td>
            

            <td class="type">
            
                
<span class="param-type">ReactNode</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>errorColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>renderError</code></td>
            

            <td class="type">
            
                
<span class="param-type">ErrorRenderer</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>math</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>children</code></td>
            

            <td class="type">
            
                
<span class="param-type">ReactNode</span>


            
            </td>

            
                <td class="attributes">
                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>errorColor</code></td>
            

            <td class="type">
            
                
<span class="param-type">string</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    

        <tr>
            
                <td class="name"><code>renderError</code></td>
            

            <td class="type">
            
                
<span class="param-type">ErrorRenderer</span>


            
            </td>

            
                <td class="attributes">
                
                    &lt;optional><br>
                

                
                </td>
            

            

            <td class="description last"></td>
        </tr>

    
    </tbody>
</table>




<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="index.jsx.html">index.jsx</a>, <a href="index.jsx.html#line5">line 5</a>
    </li></ul></dd>
    

    

    

    
</dl>















<h5>Returns:</h5>

        


<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">ReactNode</span>


    </dd>
</dl>

    





            
    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3><a href="global.html">Global</a></h3>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc/jsdoc">JSDoc 3.6.11</a> on Wed Aug 10 2022 20:21:11 GMT-0300 (Brasilia Standard Time)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>