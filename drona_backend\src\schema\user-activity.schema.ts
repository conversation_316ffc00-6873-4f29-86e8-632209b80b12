// user-activity.schema.ts
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from './user.schema';
import { College } from './college.schema';

export type UserActivityDocument = UserActivity & Document;

@Schema()
export class UserActivity {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User', required: true })
  userId: User;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'College' })
  collegeId: College;

  @Prop({
    required: true,
    enum: ['login', 'question_creation', 'paper_generation'],
  })
  activityType: string;

  @Prop({ type: MongooseSchema.Types.Mixed })
  activityDetails: any;

  @Prop({ required: true, default: Date.now })
  timestamp: Date;

  @Prop()
  ipAddress: string;
}

export const UserActivitySchema = SchemaFactory.createForClass(UserActivity);
