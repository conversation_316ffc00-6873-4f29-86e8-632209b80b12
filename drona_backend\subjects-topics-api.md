# Subjects and Topics API Documentation

## Overview
Complete implementation of Subjects and Topics management API for the Drona backend system. These endpoints provide the necessary data for question creation, as questions require both `subjectId` and `topicId` fields.

## Key Features
- **Hierarchical Structure**: Topics belong to subjects (cannot create topic without subject)
- **Nested Data Retrieval**: Get subjects with their associated topics in a single API call
- **Data Validation**: Prevents duplicate names within the same scope
- **Role-based Access**: Super admin only for creation/modification, read access for all authenticated users
- **Comprehensive CRUD**: Full Create, Read, Update, Delete operations

## Subjects API

### **GET /api/subjects**
Get all subjects (simple list)

**Authorization:** Bearer <PERSON>ken (Any authenticated user)

**Response:**
```json
[
  {
    "_id": "60d21b4667d0d8992e610c85",
    "name": "Mathematics",
    "description": "Study of numbers, quantities, and shapes"
  },
  {
    "_id": "60d21b4667d0d8992e610c86",
    "name": "Physics",
    "description": "Study of matter, energy, and their interactions"
  }
]
```

### **GET /api/subjects/with-topics**
Get all subjects with their nested topics (recommended for question creation forms)

**Authorization:** Bearer Token (Any authenticated user)

**Response:**
```json
[
  {
    "_id": "60d21b4667d0d8992e610c85",
    "name": "Mathematics",
    "description": "Study of numbers, quantities, and shapes",
    "topics": [
      {
        "_id": "60d21b4667d0d8992e610c87",
        "name": "Calculus",
        "description": "Branch of mathematics dealing with limits"
      },
      {
        "_id": "60d21b4667d0d8992e610c88",
        "name": "Algebra",
        "description": "Study of mathematical symbols and rules"
      }
    ]
  },
  {
    "_id": "60d21b4667d0d8992e610c86",
    "name": "Physics",
    "description": "Study of matter, energy, and their interactions",
    "topics": [
      {
        "_id": "60d21b4667d0d8992e610c89",
        "name": "Mechanics",
        "description": "Study of motion and forces"
      }
    ]
  }
]
```

### **GET /api/subjects/:id**
Get a specific subject by ID

**Authorization:** Bearer Token (Any authenticated user)

**Parameters:**
- `id` (string): Subject ID

**Response:**
```json
{
  "_id": "60d21b4667d0d8992e610c85",
  "name": "Mathematics",
  "description": "Study of numbers, quantities, and shapes"
}
```

### **POST /api/subjects**
Create a new subject

**Authorization:** Bearer Token (Super Admin only)

**Request Body:**
```json
{
  "name": "Chemistry",
  "description": "Study of matter and chemical reactions"
}
```

**Response:**
```json
{
  "_id": "60d21b4667d0d8992e610c90",
  "name": "Chemistry",
  "description": "Study of matter and chemical reactions"
}
```

### **PATCH /api/subjects/:id**
Update a subject

**Authorization:** Bearer Token (Super Admin only)

**Parameters:**
- `id` (string): Subject ID

**Request Body:**
```json
{
  "name": "Advanced Mathematics",
  "description": "Advanced study of mathematical concepts"
}
```

### **DELETE /api/subjects/:id**
Delete a subject

**Authorization:** Bearer Token (Super Admin only)

**Parameters:**
- `id` (string): Subject ID

**Note:** Cannot delete a subject that has associated topics. Delete topics first.

## Topics API

### **GET /api/topics**
Get all topics (with subject information populated)

**Authorization:** Bearer Token (Any authenticated user)

**Query Parameters:**
- `subjectId` (optional): Filter topics by subject ID

**Examples:**
```bash
# Get all topics
GET /api/topics

# Get topics for a specific subject
GET /api/topics?subjectId=60d21b4667d0d8992e610c85
```

**Response:**
```json
[
  {
    "_id": "60d21b4667d0d8992e610c87",
    "name": "Calculus",
    "description": "Branch of mathematics dealing with limits",
    "subjectId": {
      "_id": "60d21b4667d0d8992e610c85",
      "name": "Mathematics",
      "description": "Study of numbers, quantities, and shapes"
    }
  }
]
```

### **GET /api/topics/:id**
Get a specific topic by ID

**Authorization:** Bearer Token (Any authenticated user)

**Parameters:**
- `id` (string): Topic ID

**Response:**
```json
{
  "_id": "60d21b4667d0d8992e610c87",
  "name": "Calculus",
  "description": "Branch of mathematics dealing with limits",
  "subjectId": {
    "_id": "60d21b4667d0d8992e610c85",
    "name": "Mathematics",
    "description": "Study of numbers, quantities, and shapes"
  }
}
```

### **POST /api/topics**
Create a new topic

**Authorization:** Bearer Token (Super Admin only)

**Request Body:**
```json
{
  "name": "Differential Equations",
  "subjectId": "60d21b4667d0d8992e610c85",
  "description": "Mathematical equations involving derivatives"
}
```

**Response:**
```json
{
  "_id": "60d21b4667d0d8992e610c91",
  "name": "Differential Equations",
  "subjectId": "60d21b4667d0d8992e610c85",
  "description": "Mathematical equations involving derivatives"
}
```

### **PATCH /api/topics/:id**
Update a topic

**Authorization:** Bearer Token (Super Admin only)

**Parameters:**
- `id` (string): Topic ID

**Request Body:**
```json
{
  "name": "Advanced Calculus",
  "description": "Advanced concepts in calculus"
}
```

### **DELETE /api/topics/:id**
Delete a topic

**Authorization:** Bearer Token (Super Admin only)

**Parameters:**
- `id` (string): Topic ID

## Integration with Questions API

### Question Creation Flow
1. **Fetch Subjects with Topics**: Use `GET /api/subjects/with-topics` to populate dropdown/selection UI
2. **Create Question**: Use the `subjectId` and `topicId` in the question creation payload

**Example Question Creation:**
```json
{
  "content": "What is the derivative of x²?",
  "options": ["2x", "x", "2", "x²"],
  "answer": "2x",
  "subjectId": "60d21b4667d0d8992e610c85",
  "topicId": "60d21b4667d0d8992e610c87",
  "difficulty": "medium",
  "type": "multiple-choice"
}
```

## Data Validation Rules

### Subjects
- **Name**: Required, unique (case-insensitive), max 100 characters
- **Description**: Optional, max 500 characters

### Topics
- **Name**: Required, unique within subject (case-insensitive), max 100 characters
- **SubjectId**: Required, must reference existing subject
- **Description**: Optional, max 500 characters

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "statusCode": 400,
  "message": ["name should not be empty", "name must be a string"],
  "error": "Bad Request"
}
```

**404 Not Found:**
```json
{
  "statusCode": 404,
  "message": "Subject with ID 60d21b4667d0d8992e610c85 not found",
  "error": "Not Found"
}
```

**409 Conflict:**
```json
{
  "statusCode": 409,
  "message": "Subject with name 'Mathematics' already exists",
  "error": "Conflict"
}
```

## Frontend Integration Examples

### React/JavaScript Example
```javascript
// Fetch subjects with topics for question form
const fetchSubjectsWithTopics = async () => {
  const response = await fetch('/api/subjects/with-topics', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// Create a new subject
const createSubject = async (subjectData) => {
  const response = await fetch('/api/subjects', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(subjectData)
  });
  return response.json();
};
```

## Database Schema

### Subject Schema
```typescript
{
  _id: ObjectId,
  name: string (required, unique),
  description: string (optional)
}
```

### Topic Schema
```typescript
{
  _id: ObjectId,
  name: string (required),
  subjectId: ObjectId (required, ref: 'Subject'),
  description: string (optional)
}
```

## Implementation Summary

### ✅ **REQUIREMENTS FULFILLED**

1. **✅ Subject and Topic Data API**: Created endpoints to fetch subjects and topics for question creation
2. **✅ Hierarchical Structure**: Topics are always related to subjects, cannot create topic without subject
3. **✅ Nested Data Format**: `/subjects/with-topics` returns subjects with nested topics array
4. **✅ CRUD Operations**: Complete Create, Read, Update, Delete for both subjects and topics
5. **✅ Data Validation**: Prevents duplicates, validates relationships
6. **✅ Role-based Access**: Super admin for modifications, authenticated users for reading

### **📁 Files Created**
- `src/subjects/subjects.controller.ts` - Subject API endpoints
- `src/subjects/subjects.service.ts` - Subject business logic
- `src/subjects/subjects.module.ts` - Subject module configuration
- `src/subjects/dto/create-subject.dto.ts` - Subject creation validation
- `src/subjects/dto/update-subject.dto.ts` - Subject update validation
- `src/topics/topics.controller.ts` - Topic API endpoints
- `src/topics/topics.service.ts` - Topic business logic
- `src/topics/topics.module.ts` - Topic module configuration
- `src/topics/dto/create-topic.dto.ts` - Topic creation validation
- `src/topics/dto/update-topic.dto.ts` - Topic update validation

### **🔧 Files Modified**
- `src/app.module.ts` - Added SubjectsModule and TopicsModule

### **🚀 Ready for Production**
- ✅ Build successful
- ✅ TypeScript compilation passed
- ✅ Complete API documentation
- ✅ Error handling implemented
- ✅ Data validation in place

This implementation provides a complete foundation for managing subjects and topics, enabling proper categorization of questions and question papers in the Drona system.
