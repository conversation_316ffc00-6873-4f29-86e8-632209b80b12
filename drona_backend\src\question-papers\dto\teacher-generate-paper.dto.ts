import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>E<PERSON>y,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsEnum,
  IsBoolean,
  Min,
  Max,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum ExamType {
  NEET = 'NEET',
  CET = 'CET',
  JEE = 'JEE',
  AIIMS = 'AIIMS',
  JIPMER = 'JIPMER',
  CUSTOM = 'CUSTOM',
}

export enum SubjectShortCode {
  PHYSICS = 'physics',
  CHEMISTRY = 'chemistry',
  BIOLOGY = 'biology',
  MATHEMATICS = 'mathematics',
  MATH = 'math',
  PHY = 'phy',
  CHEM = 'chem',
  BIO = 'bio',
}

export enum DifficultyMode {
  AUTO = 'auto',
  CUSTOM = 'custom',
}

export class CustomDifficultyConfig {
  @ApiProperty({
    description: 'Percentage of easy questions',
    minimum: 0,
    maximum: 100,
    example: 30,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  easyPercentage: number;

  @ApiProperty({
    description: 'Percentage of medium questions',
    minimum: 0,
    maximum: 100,
    example: 50,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  mediumPercentage: number;

  @ApiProperty({
    description: 'Percentage of hard questions',
    minimum: 0,
    maximum: 100,
    example: 20,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  hardPercentage: number;
}

export class TeacherGeneratePaperDto {
  @ApiProperty({
    description: 'Question paper title',
    example: 'NEET Physics Mock Test 2024',
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Exam type',
    enum: ExamType,
    example: ExamType.NEET,
  })
  @IsEnum(ExamType)
  @IsNotEmpty()
  examType: ExamType;

  @ApiProperty({
    description: 'Subject selection',
    enum: SubjectShortCode,
    example: SubjectShortCode.PHYSICS,
  })
  @IsEnum(SubjectShortCode)
  @IsNotEmpty()
  subject: SubjectShortCode;

  @ApiProperty({
    description: 'Difficulty level mode',
    enum: DifficultyMode,
    example: DifficultyMode.AUTO,
  })
  @IsEnum(DifficultyMode)
  @IsNotEmpty()
  difficultyMode: DifficultyMode;

  @ApiPropertyOptional({
    description:
      'Custom difficulty configuration (required if difficultyMode is custom)',
    type: CustomDifficultyConfig,
  })
  @IsOptional()
  customDifficulty?: CustomDifficultyConfig;

  @ApiProperty({
    description: 'Number of questions',
    minimum: 1,
    maximum: 200,
    example: 50,
  })
  @IsNumber()
  @Min(1)
  @Max(200)
  numberOfQuestions: number;

  @ApiProperty({
    description: 'Total marks for the paper',
    minimum: 1,
    example: 100,
  })
  @IsNumber()
  @Min(1)
  totalMarks: number;

  @ApiProperty({
    description: 'Duration in minutes',
    minimum: 1,
    example: 180,
  })
  @IsNumber()
  @Min(1)
  duration: number;

  @ApiProperty({
    description: 'Include answers in the generated paper',
    example: true,
  })
  @IsBoolean()
  includeAnswers: boolean;

  @ApiPropertyOptional({
    description: 'Additional instructions for the paper',
    example: 'Read all questions carefully before answering',
  })
  @IsOptional()
  @IsString()
  instructions?: string;

  @ApiPropertyOptional({
    description: 'Topic ID for specific topic filtering (optional)',
    example: '60d21b4667d0d8992e610c86',
  })
  @IsOptional()
  @IsString()
  topicId?: string;
}
