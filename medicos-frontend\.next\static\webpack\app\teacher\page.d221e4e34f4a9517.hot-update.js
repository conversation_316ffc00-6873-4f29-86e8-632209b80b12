"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* harmony import */ var _steps_multi_subject_config_step__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./steps/multi-subject-config-step */ \"(app-pages-browser)/./src/components/teacher/steps/multi-subject-config-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    paperMode: \"single\",\n    course: \"\",\n    subject: \"\",\n    subjects: [],\n    subjectConfigs: {},\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easyPercentage: 30,\n        mediumPercentage: 50,\n        hardPercentage: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const goToFirstStep = ()=>{\n        setCurrentStep(0);\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                setIsGenerating(false);\n                alert(\"Please enter a title for the question paper\");\n                return;\n            }\n            if (!formData.questionType) {\n                setIsGenerating(false);\n                alert(\"Please select an exam type\");\n                return;\n            }\n            // Validate based on paper mode\n            if (formData.paperMode === \"single\") {\n                if (!formData.subject) {\n                    setIsGenerating(false);\n                    alert(\"Please select a subject\");\n                    return;\n                }\n            } else {\n                if (formData.subjects.length === 0) {\n                    setIsGenerating(false);\n                    alert(\"Please select at least one subject\");\n                    return;\n                }\n            }\n            // Prepare the API payload\n            let apiPayload;\n            if (formData.paperMode === \"single\") {\n                // Single subject mode\n                apiPayload = {\n                    title: formData.title,\n                    description: formData.description,\n                    subject: formData.subject,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    examType: formData.questionType,\n                    instructions: formData.instructions,\n                    topicId: formData.topicId\n                };\n                // Add customization if not auto mode\n                if (formData.difficultyMode === \"custom\") {\n                    apiPayload.customise = {\n                        customDifficulty: formData.difficultyLevels,\n                        numberOfQuestions: formData.numberOfQuestions,\n                        totalMarks: formData.totalMarks,\n                        duration: formData.duration,\n                        includeAnswers: formData.includeAnswers\n                    };\n                } else {\n                    // For auto mode, still include customization with default values\n                    apiPayload.customise = {\n                        customDifficulty: {\n                            easyPercentage: 30,\n                            mediumPercentage: 50,\n                            hardPercentage: 20\n                        },\n                        numberOfQuestions: formData.numberOfQuestions,\n                        totalMarks: formData.totalMarks,\n                        duration: formData.duration,\n                        includeAnswers: formData.includeAnswers\n                    };\n                }\n            } else {\n                // Multi-subject mode\n                const subjects = formData.subjects.map((subjectName)=>{\n                    const config = formData.subjectConfigs[subjectName];\n                    return {\n                        subject: subjectName,\n                        numberOfQuestions: config.numberOfQuestions,\n                        totalMarks: config.totalMarks,\n                        customDifficulty: config.difficultyLevels,\n                        topicId: config.topicId\n                    };\n                });\n                apiPayload = {\n                    title: formData.title,\n                    description: formData.description,\n                    duration: formData.duration,\n                    examType: formData.questionType,\n                    instructions: formData.instructions,\n                    subjects: subjects,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            console.log(\"Calling createQuestionPaper API...\");\n            const result = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"API result:\", result);\n            // Check if the request was successful\n            if (!result.success) {\n                console.log(\"API returned error:\", result.error);\n                setIsGenerating(false);\n                let errorMessage = result.error;\n                // Handle specific error types with better messages\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                    errorMessage = \"Please log in again to continue. Your session may have expired.\";\n                } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                    errorMessage = \"Please check your internet connection and try again.\";\n                } else if (errorMessage.includes(\"unused questions available\")) {\n                    // Extract numbers from the error message for a clearer explanation\n                    const match = errorMessage.match(/Only (\\d+) unused questions available\\. Requested: (\\d+)/);\n                    if (match) {\n                        const available = match[1];\n                        const requested = match[2];\n                        errorMessage = \"Only \".concat(available, \" questions are available for this subject/topic, but you requested \").concat(requested, \" questions. Please reduce the number of questions or add more questions to the database.\");\n                    }\n                }\n                // Show error message in alert\n                alert(\"Error: \".concat(errorMessage));\n                console.log(\"Staying on current step due to error\");\n                return; // Exit early on error\n            }\n            // Success - proceed with download\n            console.log(\"API success! Proceeding with download...\");\n            console.log(\"Full API response:\", result);\n            // The API response structure should be { success: true, data: QuestionPaperResponse }\n            const questionPaper = result.data;\n            console.log(\"Question paper data:\", questionPaper);\n            // Check if the response has the expected structure\n            const responseData = result.data; // Use any to handle different possible structures\n            let questionPaperId;\n            if (responseData && responseData.questionPaper && responseData.questionPaper._id) {\n                // Structure: { data: { questionPaper: { _id: \"...\" } } }\n                questionPaperId = responseData.questionPaper._id;\n                console.log(\"Found ID in questionPaper object:\", questionPaperId);\n            } else if (responseData && responseData._id) {\n                // Structure: { data: { _id: \"...\" } }\n                questionPaperId = responseData._id;\n                console.log(\"Found ID in data object:\", questionPaperId);\n            } else {\n                // Try to find _id anywhere in the response\n                const findId = (obj)=>{\n                    if (obj && typeof obj === 'object') {\n                        if (obj._id && typeof obj._id === 'string') {\n                            return obj._id;\n                        }\n                        for(const key in obj){\n                            const found = findId(obj[key]);\n                            if (found) return found;\n                        }\n                    }\n                    return undefined;\n                };\n                questionPaperId = findId(result);\n                console.log(\"Found ID through deep search:\", questionPaperId);\n            }\n            // Validate that we have a question paper ID\n            if (!questionPaperId) {\n                console.error(\"No question paper ID found in response. Full response:\", result);\n                throw new Error(\"Question paper was created but no ID was returned. Please check the console for details and try again.\");\n            }\n            console.log(\"Using question paper ID for download:\", questionPaperId);\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaperId, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            alert(\"Question paper generated and downloaded successfully!\");\n            console.log(\"Success! About to redirect to first step in 1 second...\");\n            // Reset to first step and clear form data after a short delay (only on success)\n            setTimeout(()=>{\n                console.log(\"Redirecting to first step now...\");\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n                console.log(\"Redirect completed. Current step should be 0\");\n            }, 1000) // 1 second delay to ensure alert is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            // Handle any unexpected errors (like network issues)\n            alert(\"Error: An unexpected error occurred. Please try again.\");\n        }\n    };\n    // Build steps array dynamically based on paper mode\n    const buildSteps = ()=>{\n        const baseSteps = [\n            {\n                title: \"Question Type\",\n                icon: \"HelpCircle\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__.QuestionTypeStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 329,\n                    columnNumber: 11\n                }, this)\n            },\n            {\n                title: \"Paper Details\",\n                icon: \"FileText\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__.QuestionTitleAndDescriptionStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 11\n                }, this)\n            },\n            {\n                title: \"Course & Subject Selection\",\n                icon: \"BookOpen\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__.CourseSubjectStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 11\n                }, this)\n            }\n        ];\n        // Add multi-subject configuration step if in multi-subject mode\n        if (formData.paperMode === \"multi\") {\n            baseSteps.push({\n                title: \"Configure Subjects\",\n                icon: \"Settings\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_multi_subject_config_step__WEBPACK_IMPORTED_MODULE_12__.MultiSubjectConfigStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 11\n                }, this)\n            });\n        }\n        // Add remaining steps only for single subject mode\n        if (formData.paperMode === \"single\") {\n            baseSteps.push({\n                title: \"Select Difficulty Level\",\n                icon: \"BarChart2\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__.DifficultyLevelStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 13\n                }, this)\n            }, {\n                title: \"Question Selection Criteria\",\n                icon: \"FileText\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__.QuestionSelectionStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 13\n                }, this)\n            }, {\n                title: \"Paper Customization\",\n                icon: \"FileEdit\",\n                component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__.PaperCustomizationStep, {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onSkip: skipStep,\n                    onBack: prevStep,\n                    backDisabled: currentStep === 0\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                    lineNumber: 422,\n                    columnNumber: 13\n                }, this)\n            });\n        }\n        // Add final steps for both modes\n        baseSteps.push({\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 441,\n                columnNumber: 11\n            }, this)\n        }, {\n            title: \"Generate Paper\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_9__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating,\n                onBack: goToFirstStep\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 454,\n                columnNumber: 20\n            }, this)\n        });\n        return baseSteps;\n    };\n    const steps = buildSteps();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 467,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 466,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"nel0TECL1Zv7CzoM26K8Hca8GSI=\");\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});