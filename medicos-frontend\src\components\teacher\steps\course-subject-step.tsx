"use client"

import type { FormData } from "../question-paper-wizard"
import { OptionButton } from "../ui/option-button"
import { StepNavigation } from "../ui/step-navigation"
import { InfoMessage } from "../ui/info-message"

type CourseSubjectStepProps = {
  formData: FormData
  updateFormData: (data: Partial<FormData>) => void
  onNext: () => void
  onSkip: () => void
  onBack: () => void
  backDisabled: boolean
}

export function CourseSubjectStep({ formData, updateFormData, onNext, onSkip, onBack, backDisabled }: CourseSubjectStepProps) {
  const handleSelectSubject = (subject: string) => {
    updateFormData({ subject })
  }

  const isNextDisabled = !formData.subject

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Course & Subject Selection</h2>
        <p className="text-gray-500">Choose your course and subject to generate relevant questions.</p>
      </div>

      {/* <div className="flex justify-center py-4">
        <OptionButton selected={formData.subject === "Physics"} onClick={() => handleSelectSubject("Physics")}>
          Physics
        </OptionButton>
        <OptionButton selected={formData.subject === "Math"} onClick={() => handleSelectSubject("Math")}>
          Math
        </OptionButton>
        <OptionButton selected={formData.subject === "Chemistry"} onClick={() => handleSelectSubject("Chemistry")}>
          Chemistry
        </OptionButton>
        <OptionButton selected={formData.subject === "Biology"} onClick={() => handleSelectSubject("Biology")}>
          Biology
        </OptionButton>
      </div> */}
            <div className="flex justify-center py-4">
        <div className="inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]">
          <OptionButton
            selected={formData.subject === "Physics"}
            onClick={() => handleSelectSubject("Physics")}
            grouped={true}
            position="left"
            className="rounded-none border-0"
          >
            Physics
          </OptionButton>
          <div className="w-px bg-gray-200"></div>
          <OptionButton
            selected={formData.subject === "Math"}
            onClick={() => handleSelectSubject("Math")}
            grouped={true}
            position="right"
            className="rounded-none border-0"
          >
            Math
          </OptionButton>
          <div className="w-px bg-gray-200"></div>

          <OptionButton
            selected={formData.subject === "Chemistry"}
            onClick={() => handleSelectSubject("Chemistry")}
            grouped={true}
            position="left"
            className="rounded-none border-0"
          >
            Chemistry
          </OptionButton>
          <div className="w-px bg-gray-200"></div>
          <OptionButton
            selected={formData.subject === "Biology"}
            onClick={() => handleSelectSubject("Biology")}
            grouped={true}
            position="right"
            className="rounded-none border-0"
          >
            Biology
          </OptionButton>
        </div>
      </div>

      <StepNavigation 
        onNext={onNext} 
        onSkip={onSkip} 
        onBack={onBack}
        backDisabled={backDisabled}
        nextDisabled={isNextDisabled} 
      />

      {isNextDisabled && <InfoMessage message="Please select a course and subject before proceeding." />}
    </div>
  )
}
