{"name": "react-katex", "version": "3.1.0", "description": "Display math in TeX with KaTeX and ReactJS", "keywords": ["react", "tex", "latex", "math", "katex"], "homepage": "https://github.com/talyssonoc/react-katex", "main": "dist/react-katex.js", "scripts": {"build": "NODE_ENV=production swc ./src/index.jsx -o dist/react-katex.js", "test": "jest", "report-coverage": "jest && cat ./coverage/lcov.info | coveralls", "lint": "eslint src/**/*.jsx"}, "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git://github.com/talyssonoc/react-katex.git"}, "bugs": {"url": "https://github.com/talyssonoc/react-katex/issues"}, "license": "MIT", "dependencies": {"katex": "^0.16.0"}, "peerDependencies": {"prop-types": "^15.8.1", "react": ">=15.3.2 <20"}, "devDependencies": {"@babel/eslint-parser": "^7.18.9", "@babel/preset-react": "^7.18.6", "@swc/cli": "^0.1.57", "@swc/core": "^1.2.224", "@swc/jest": "^0.2.22", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.3.0", "@types/katex": "^0.14.0", "coveralls": "^3.1.1", "eslint": "^8.21.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-react": "^7.30.1", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "prop-types": "^15.8.1", "react": ">=15.3.2 <=18", "react-dom": ">=15.3.2 <=18"}}