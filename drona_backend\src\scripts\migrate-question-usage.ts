import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { QuestionUsageService } from '../question-usage/question-usage.service';
import { Model } from 'mongoose';
import { getModelToken } from '@nestjs/mongoose';
import { QuestionPaper } from '../schema/question-paper.schema';
import { Logger } from '@nestjs/common';

/**
 * Migration script to populate existing question paper data into the new question usage tracking system
 *
 * Usage: npm run migration:question-usage
 */
async function migrateQuestionUsage() {
  const logger = new Logger('QuestionUsageMigration');

  try {
    // Create NestJS application context
    const app = await NestFactory.createApplicationContext(AppModule);

    // Get services
    const questionUsageService = app.get(QuestionUsageService);
    const questionPaperModel = app.get<Model<QuestionPaper>>(
      getModelToken(QuestionPaper.name),
    );

    logger.log('Starting question usage migration...');

    // Get all existing question papers with questions
    const questionPapers = await questionPaperModel
      .find({
        type: { $ne: 'limit' }, // Exclude limit documents
        questions: { $exists: true, $not: { $size: 0 } }, // Only papers with questions
        collegeId: { $exists: true }, // Only papers with college association
      })
      .populate('questions')
      .exec();

    logger.log(`Found ${questionPapers.length} question papers to migrate`);

    let totalProcessed = 0;
    let totalRecorded = 0;
    let totalSkipped = 0;

    // Process each question paper
    for (const paper of questionPapers) {
      try {
        if (
          !paper.collegeId ||
          !paper.questions ||
          paper.questions.length === 0
        ) {
          logger.warn(
            `Skipping paper ${paper._id}: Missing collegeId or questions`,
          );
          continue;
        }

        // Prepare usage data for all questions in this paper
        const usageData = paper.questions.map((question: any) => ({
          collegeId: paper.collegeId.toString(),
          questionId: question._id.toString(),
          questionPaperId: paper._id.toString(),
          usedBy: paper.generatedBy.toString(),
          subjectId: paper.subjectId.toString(),
          topicId: paper.topicId?.toString(),
          metadata: {
            examType: paper.examType,
            difficulty: question.difficulty,
            marks: question.marks || 1,
            migrated: true, // Mark as migrated data
          },
        }));

        // Record question usage
        const result =
          await questionUsageService.recordMultipleQuestionUsage(usageData);

        totalProcessed++;
        totalRecorded += result.recorded;
        totalSkipped += result.skipped;

        if (totalProcessed % 10 === 0) {
          logger.log(
            `Processed ${totalProcessed}/${questionPapers.length} papers...`,
          );
        }
      } catch (error) {
        logger.error(`Error processing paper ${paper._id}: ${error.message}`);
      }
    }

    logger.log('Migration completed successfully!');
    logger.log(`Summary:`);
    logger.log(`- Question papers processed: ${totalProcessed}`);
    logger.log(`- Question usage records created: ${totalRecorded}`);
    logger.log(`- Duplicate records skipped: ${totalSkipped}`);

    // Close the application context
    await app.close();
  } catch (error) {
    logger.error(`Migration failed: ${error.message}`, error.stack);
    process.exit(1);
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateQuestionUsage()
    .then(() => {
      console.log('Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}

export { migrateQuestionUsage };
