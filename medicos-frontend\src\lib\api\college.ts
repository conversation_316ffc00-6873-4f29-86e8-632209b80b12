/**
 * Interface for college data
 */
export interface CollegeData {
  name: string;
  address: string;
  contactPhone: string;
  contactEmail: string;
  logoUrl?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  website?: string;
}

/**
 * Create a new college
 * @param collegeData The college data
 * @returns The created college
 */
export async function createCollege(collegeData: CollegeData) {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }
  
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${baseUrl}/colleges`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      },
      body: JSON.stringify(collegeData)
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error creating college:", error);
    throw error;
  }
}

/**
 * Get all colleges
 * @returns List of colleges
 */
export async function getColleges() {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }
  
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${baseUrl}/colleges`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error fetching colleges:", error);
    throw error;
  }
}

/**
 * Delete a college
 * @param id College ID
 * @returns The deleted college
 */
export async function deleteCollege(id: string) {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }
  
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${baseUrl}/colleges/${id}`, {
      method: "DELETE",
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error deleting college:", error);
    throw error;
  }
}

/**
 * Get a college by ID
 * @param id College ID
 * @returns The college
 */
export async function getCollegeById(id: string) {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }
  
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${baseUrl}/colleges/${id}`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error fetching college:", error);
    throw error;
  }
}

/**
 * Update a college
 * @param id College ID
 * @param collegeData The updated college data
 * @returns The updated college
 */
export async function updateCollege(id: string, collegeData: CollegeData) {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }
  
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${baseUrl}/colleges/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      },
      body: JSON.stringify(collegeData)
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error updating college:", error);
    throw error;
  }
}
/**
 * Get a college by ID
 * @param id College ID
 * @returns The college
 */
export async function getCollegeAnalytics(id: string) {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }
  
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    const response = await fetch(`${baseUrl}/analytics/college/${id}/summary`, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error fetching college:", error);
    throw error;
  }
}

/**
 * Get daily-wise question paper statistics by subject for a college
 * @param id College ID
 * @param startDate ISO start date string
 * @param endDate ISO end date string
 * @returns Statistics data
 */
export async function getQuestionPaperStatsByDateRange(id: string, startDate: string, endDate: string) {
  const token = localStorage.getItem("backendToken");
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
    const url = `${baseUrl}/analytics/college/${id}/question-papers?startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Authorization": `Bearer ${token}`,
        "Content-Type": "application/json"
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching question paper stats:", error);
    throw error;
  }
}
