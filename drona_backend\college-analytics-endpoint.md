# Super Admin College Analytics Endpoint

## Overview
A comprehensive analytics endpoint for super admins to get college-wise questions and usage statistics across the entire platform.

## Endpoint Details

### **GET** `/api/analytics/colleges/analytics`

**Authorization:** <PERSON><PERSON> (Super Admin only)

**Description:** Returns detailed analytics for each college including questions, usage, downloads, and activity metrics.

## Query Parameters

| Parameter | Type | Required | Default | Description |
|-----------|------|----------|---------|-------------|
| `startDate` | string (ISO) | No | - | Start date for filtering analytics data |
| `endDate` | string (ISO) | No | - | End date for filtering analytics data |
| `limit` | number | No | 50 | Number of colleges to return |
| `sortBy` | string | No | 'totalActivity' | Sort criteria: `totalActivity`, `questionCount`, `downloadCount`, `teacherCount`, `paperCount` |

## Example Request

```bash
GET /api/analytics/colleges/analytics?startDate=2024-01-01T00:00:00.000Z&endDate=2024-12-31T23:59:59.999Z&limit=25&sortBy=questionCount
Authorization: Bearer <super_admin_token>
```

## Response Format

```json
{
  "summary": {
    "totalColleges": 25,
    "totalTeachers": 450,
    "totalQuestions": 23400,
    "totalPapers": 1560,
    "totalDownloads": 3200
  },
  "colleges": [
    {
      "collegeId": "60d21b4667d0d8992e610c85",
      "collegeName": "Harvard University",
      "status": "active",
      "metrics": {
        "teacherCount": 45,
        "activeTeachers": 42,
        "questionCount": 2340,
        "approvedQuestions": 2106,
        "pendingQuestions": 117,
        "rejectedQuestions": 117,
        "paperCount": 156,
        "downloadCount": 320,
        "totalActivity": 2822
      },
      "subjectBreakdown": [
        {
          "subjectId": "60d21b4667d0d8992e610c86",
          "subjectName": "Mathematics",
          "questionCount": 585,
          "paperCount": 39,
          "downloadCount": 80
        },
        {
          "subjectId": "60d21b4667d0d8992e610c87",
          "subjectName": "Physics",
          "questionCount": 468,
          "paperCount": 31,
          "downloadCount": 65
        }
      ],
      "recentActivity": {
        "last30Days": {
          "questionsCreated": 45,
          "papersGenerated": 12,
          "downloads": 67,
          "activeTeachers": 38
        }
      }
    }
  ],
  "filters": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T23:59:59.999Z",
    "limit": 25,
    "sortBy": "questionCount"
  }
}
```

## Key Features

### **📊 Comprehensive Metrics**
- **Teacher Statistics**: Total teachers, active teachers
- **Question Analytics**: Total questions, approved/pending/rejected breakdown
- **Paper Generation**: Total papers generated per college
- **Download Tracking**: Total downloads per college
- **Activity Score**: Combined activity metric for ranking

### **📈 Subject-wise Breakdown**
- Questions created per subject
- Papers generated per subject  
- Downloads per subject
- Helps identify popular subjects and usage patterns

### **⏰ Recent Activity (Last 30 Days)**
- Questions created in last 30 days
- Papers generated in last 30 days
- Downloads in last 30 days
- Active teachers in last 30 days

### **🔍 Flexible Filtering**
- **Date Range**: Filter by start and end dates
- **Sorting**: Sort by different metrics (activity, questions, downloads, etc.)
- **Pagination**: Limit number of results returned

### **⚡ Performance Optimized**
- Uses MongoDB aggregation pipelines
- Cached results for better performance
- Efficient database queries with proper indexing

## Use Cases

### **1. Platform Overview**
```bash
GET /api/analytics/colleges/analytics?limit=10&sortBy=totalActivity
```
Get top 10 most active colleges

### **2. Question Creation Analysis**
```bash
GET /api/analytics/colleges/analytics?sortBy=questionCount&limit=20
```
Find colleges creating the most questions

### **3. Usage Trends**
```bash
GET /api/analytics/colleges/analytics?startDate=2024-01-01&endDate=2024-03-31&sortBy=downloadCount
```
Analyze Q1 2024 download patterns

### **4. Teacher Engagement**
```bash
GET /api/analytics/colleges/analytics?sortBy=teacherCount&limit=15
```
Identify colleges with most teachers

## Response Codes

| Code | Description |
|------|-------------|
| 200 | Success - Analytics data returned |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Insufficient permissions (not super admin) |
| 500 | Internal Server Error |

## Data Sources

The endpoint aggregates data from:
- **Users Collection**: Teacher counts and activity
- **Questions Collection**: Question creation and review status
- **QuestionPapers Collection**: Paper generation statistics
- **Downloads Collection**: Download tracking data
- **UserActivities Collection**: Recent activity metrics
- **Subjects Collection**: Subject information for breakdowns

## Caching

- Results are cached for 1 hour (3600 seconds)
- Cache key includes all filter parameters
- Automatic cache invalidation on data updates

This endpoint provides super admins with comprehensive insights into college performance, usage patterns, and engagement metrics across the entire platform.
