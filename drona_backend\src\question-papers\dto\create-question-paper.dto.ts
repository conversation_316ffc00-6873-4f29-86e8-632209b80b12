import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON>ber,
  IsArray,
  IsO<PERSON>al,
  Min,
  Max,
  IsBoolean,
  IsEnum,
  ValidateNested,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { Type } from 'class-transformer';

export enum ExamType {
  NEET = 'NEET',
  CET = 'CET',
  JEE = 'JEE',
  AIIMS = 'AIIMS',
  JIPMER = 'JIPMER',
  CUSTOM = 'CUSTOM',
}

export enum SubjectShortCode {
  PHYSICS = 'physics',
  CHEMISTRY = 'chemistry',
  BIOLOGY = 'biology',
  MATHEMATICS = 'mathematics',
  MATH = 'math',
  PHY = 'phy',
  CHEM = 'chem',
  BIO = 'bio',
}

export class CustomDifficultyConfig {
  @ApiProperty({
    description: 'Percentage of easy questions (0-100)',
    minimum: 0,
    maximum: 100,
    example: 30,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  easyPercentage: number;

  @ApiProperty({
    description: 'Percentage of medium questions (0-100)',
    minimum: 0,
    maximum: 100,
    example: 50,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  mediumPercentage: number;

  @ApiProperty({
    description: 'Percentage of hard questions (0-100)',
    minimum: 0,
    maximum: 100,
    example: 20,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  hardPercentage: number;
}

export class CustomiseConfig {
  @ApiProperty({
    description: 'Custom difficulty configuration',
    type: CustomDifficultyConfig,
  })
  @ValidateNested()
  @Type(() => CustomDifficultyConfig)
  customDifficulty: CustomDifficultyConfig;

  @ApiProperty({
    description: 'Number of questions',
    minimum: 1,
    maximum: 200,
    example: 50,
  })
  @IsNumber()
  @Min(1)
  @Max(200)
  numberOfQuestions: number;

  @ApiProperty({
    description: 'Total marks for the paper',
    minimum: 1,
    example: 100,
  })
  @IsNumber()
  @Min(1)
  totalMarks: number;

  @ApiProperty({
    description: 'Duration in minutes',
    minimum: 1,
    example: 180,
  })
  @IsNumber()
  @Min(1)
  duration: number;

  @ApiProperty({
    description: 'Include answers in the generated paper',
    example: true,
  })
  @IsBoolean()
  includeAnswers: boolean;
}

export class CreateQuestionPaperDto {
  @ApiProperty({
    description: 'The title of the question paper',
    example: 'NEET Physics Mock Test 2024',
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'The description of the question paper',
    example: 'Final examination for Mathematics course',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'The maximum number of questions allowed in the paper',
    example: 50,
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxQuestions?: number;

  @ApiProperty({
    description:
      'Subject shortcode (physics/chemistry/biology/mathematics) or ObjectId',
    example: 'physics',
  })
  @IsString()
  @IsNotEmpty()
  subject: string;

  @ApiPropertyOptional({ description: 'ID of the topic (optional)' })
  @IsOptional()
  @IsString()
  topicId?: Types.ObjectId;

  @ApiProperty({ description: 'Total marks for the paper', minimum: 1 })
  @IsNumber()
  @Min(1)
  totalMarks: number;

  @ApiProperty({ description: 'Duration in minutes', minimum: 1 })
  @IsNumber()
  @Min(1)
  duration: number;

  @ApiPropertyOptional({ description: 'Instructions for the question paper' })
  @IsString()
  @IsOptional()
  instructions?: string;

  // New fields for unified generation
  @ApiPropertyOptional({
    description: 'Exam type for categorization',
    enum: ExamType,
    example: ExamType.NEET,
  })
  @IsOptional()
  @IsEnum(ExamType)
  examType?: ExamType;

  @ApiPropertyOptional({
    description:
      'Customization configuration. If provided, generates customized question paper. If not provided, generates automatic question paper.',
    type: CustomiseConfig,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomiseConfig)
  customise?: CustomiseConfig;
}
