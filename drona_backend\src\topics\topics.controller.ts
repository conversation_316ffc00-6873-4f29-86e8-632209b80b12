import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { TopicsService } from './topics.service';
import { CreateTopicDto } from './dto/create-topic.dto';
import { UpdateTopicDto } from './dto/update-topic.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

/**
 * Topic management endpoints
 *
 * These endpoints allow super admins to create, manage, and delete topics.
 * Topics belong to subjects and are used to further categorize questions.
 */
@ApiTags('Topics')
@ApiBearerAuth('JWT-auth')
@Controller('topics')
@UseGuards(JwtAuthGuard, RolesGuard)
export class TopicsController {
  private readonly logger = new Logger(TopicsController.name);

  constructor(private readonly topicsService: TopicsService) {}

  /**
   * Create a new topic
   * @param createTopicDto Topic data
   * @returns The created topic
   */
  @Post()
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Create a new topic',
    description: 'Creates a new topic under a subject (super admin only)',
  })
  @ApiCreatedResponse({
    description: 'Topic created successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
        name: { type: 'string', example: 'Calculus' },
        subjectId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
        description: {
          type: 'string',
          example: 'Branch of mathematics dealing with limits',
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Bad request - Invalid input data' })
  @ApiConflictResponse({
    description:
      'Conflict - Topic with this name already exists in the subject',
  })
  @ApiNotFoundResponse({ description: 'Subject not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  create(@Body() createTopicDto: CreateTopicDto) {
    return this.topicsService.create(createTopicDto);
  }

  /**
   * Get all topics
   * @param subjectId Optional subject ID to filter topics
   * @returns List of topics
   */
  @Get()
  @ApiOperation({
    summary: 'Get all topics',
    description: 'Returns all topics, optionally filtered by subject',
  })
  @ApiQuery({
    name: 'subjectId',
    description: 'Subject ID to filter topics',
    required: false,
    example: '60d21b4667d0d8992e610c85',
  })
  @ApiOkResponse({
    description: 'Returns list of topics',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
          name: { type: 'string', example: 'Calculus' },
          subjectId: {
            type: 'object',
            properties: {
              _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
              name: { type: 'string', example: 'Mathematics' },
              description: { type: 'string', example: 'Study of numbers' },
            },
          },
          description: {
            type: 'string',
            example: 'Branch of mathematics dealing with limits',
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  findAll(@Query('subjectId') subjectId?: string) {
    if (subjectId) {
      return this.topicsService.findBySubject(subjectId);
    }
    return this.topicsService.findAll();
  }

  /**
   * Get a specific topic by ID
   * @param id Topic ID
   * @returns The topic
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get topic by ID',
    description: 'Returns a specific topic by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Topic ID',
    example: '60d21b4667d0d8992e610c86',
  })
  @ApiOkResponse({
    description: 'Returns the topic',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
        name: { type: 'string', example: 'Calculus' },
        subjectId: {
          type: 'object',
          properties: {
            _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
            name: { type: 'string', example: 'Mathematics' },
            description: { type: 'string', example: 'Study of numbers' },
          },
        },
        description: {
          type: 'string',
          example: 'Branch of mathematics dealing with limits',
        },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'Topic not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  findOne(@Param('id') id: string) {
    return this.topicsService.findOne(id);
  }

  /**
   * Update a topic
   * @param id Topic ID
   * @param updateTopicDto Updated topic data
   * @returns The updated topic
   */
  @Patch(':id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Update topic',
    description: 'Updates a topic (super admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'Topic ID',
    example: '60d21b4667d0d8992e610c86',
  })
  @ApiOkResponse({
    description: 'Topic updated successfully',
    schema: {
      type: 'object',
      properties: {
        _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
        name: { type: 'string', example: 'Calculus' },
        subjectId: {
          type: 'object',
          properties: {
            _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
            name: { type: 'string', example: 'Mathematics' },
            description: { type: 'string', example: 'Study of numbers' },
          },
        },
        description: {
          type: 'string',
          example: 'Branch of mathematics dealing with limits',
        },
      },
    },
  })
  @ApiBadRequestResponse({ description: 'Bad request - Invalid input data' })
  @ApiConflictResponse({
    description:
      'Conflict - Topic with this name already exists in the subject',
  })
  @ApiNotFoundResponse({ description: 'Topic or Subject not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  update(@Param('id') id: string, @Body() updateTopicDto: UpdateTopicDto) {
    return this.topicsService.update(id, updateTopicDto);
  }

  /**
   * Delete a topic
   * @param id Topic ID
   */
  @Delete(':id')
  @Roles('superAdmin')
  @ApiOperation({
    summary: 'Delete topic',
    description: 'Deletes a topic (super admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'Topic ID',
    example: '60d21b4667d0d8992e610c86',
  })
  @ApiOkResponse({ description: 'Topic deleted successfully' })
  @ApiNotFoundResponse({ description: 'Topic not found' })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  remove(@Param('id') id: string) {
    return this.topicsService.remove(id);
  }
}
