# Unified Question Paper API Implementation

## Overview
Successfully merged the two question paper generation endpoints (`/question-papers/generate` and `/question-papers`) into a single unified endpoint that supports both automatic and customized question paper generation.

## Key Changes Made

### 1. Updated DTO (Data Transfer Object)
**File:** `src/question-papers/dto/create-question-paper.dto.ts`

- Added new enums: `ExamType`, `SubjectShortCode`
- Created `CustomDifficultyConfig` class for custom difficulty settings
- Created `CustomiseConfig` class containing all customization options
- Updated `CreateQuestionPaperDto` to support both modes:
  - `subject`: Can be either ObjectId or shortcode (physics/chemistry/biology/mathematics)
  - `customise`: Optional object that determines generation mode
  - `examType`: Optional exam type (NEET/CET/JEE/AIIMS/JIPMER/CUSTOM)
  - `sections`: Optional for backward compatibility

### 2. Updated Controller
**File:** `src/question-papers/question-papers.controller.ts`

- Removed the separate `/generate` endpoint
- Updated the main `POST /question-papers` endpoint to handle both modes
- Updated API documentation with comprehensive examples for:
  - Automatic generation
  - Customized generation
  - Legacy manual mode (backward compatibility)
- Removed unused imports

### 3. Updated Service
**File:** `src/question-papers/question-papers.service.ts`

- Added `createUnified()` method that handles both automatic and customized generation
- Added `resolveSubject()` helper method to handle both ObjectId and shortcode inputs
- Updated PDF and DOCX generation to include answers based on `withAnswers` field
- Maintained all existing college-based question filtering logic
- Preserved question usage tracking for preventing reuse

## API Usage

### Automatic Generation
```json
POST /api/question-papers
{
  "title": "NEET Physics Mock Test 2024",
  "subject": "physics",
  "examType": "NEET",
  "totalMarks": 100,
  "duration": 180,
  "instructions": "Read all questions carefully before answering"
}
```

### Customized Generation
```json
POST /api/question-papers
{
  "title": "Custom Physics Test 2024",
  "subject": "physics",
  "examType": "CUSTOM",
  "totalMarks": 100,
  "duration": 180,
  "instructions": "Read all questions carefully",
  "customise": {
    "customDifficulty": {
      "easyPercentage": 30,
      "mediumPercentage": 50,
      "hardPercentage": 20
    },
    "numberOfQuestions": 50,
    "totalMarks": 100,
    "duration": 180,
    "includeAnswers": true
  }
}
```

## Generation Logic

### Mode Detection
- **Customized Mode**: Triggered when `customise` object is present in request
- **Automatic Mode**: Used when `customise` object is not provided

### Answer Inclusion Logic
- **Automatic Mode**: Never includes answers (always `withAnswers: false`)
- **Customized Mode**: Includes answers based on `customise.includeAnswers` field
- **Storage**: Answers are always saved in the database, but only sent in response/PDF based on the logic above

### College-Based Question Filtering
- Applied in both modes to prevent question reuse within the same college
- Uses the existing `QuestionUsageService` for permanent tracking
- Super admins can reuse questions, teachers cannot

## Simplified API Design
- Removed legacy sections-based approach for cleaner API
- Only supports automatic and customized generation modes
- Subject input accepts both ObjectId and shortcodes for flexibility

## Benefits
1. **Unified API**: Single endpoint for all question paper generation needs
2. **Flexible Input**: Supports both subject ObjectIds and shortcodes
3. **Smart Answer Handling**: Automatic vs customized logic for answer inclusion
4. **College Isolation**: Maintains question filtering to prevent reuse
5. **Clean Architecture**: Simplified codebase without legacy complexity
6. **Comprehensive Documentation**: Clear examples for all use cases

## Files Modified
- `src/question-papers/dto/create-question-paper.dto.ts`
- `src/question-papers/question-papers.controller.ts`
- `src/question-papers/question-papers.service.ts`

## Code Removed
- Legacy sections-based question paper creation
- `generateForTeacher` method (replaced by unified approach)
- `SectionConfig` class from DTO
- Manual section configuration logic

## Testing Recommendations
1. Test automatic generation with subject shortcodes
2. Test customized generation with answer inclusion
3. Test subject resolution with both ObjectIds and shortcodes
4. Verify college-based question filtering works in both modes
5. Test PDF/DOCX generation with and without answers
6. Verify that legacy endpoints are no longer accessible
