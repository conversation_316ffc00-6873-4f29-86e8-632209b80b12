import { UserSchema } from '../../schema/user.schema';
import { QuestionSchema } from '../../schema/question.schema';
import { QuestionPaperSchema } from '../../schema/question-paper.schema';
import { UserActivitySchema } from '../../schema/user-activity.schema';
import { DownloadSchema } from '../../schema/download.schema';
import { CollegeSchema } from '../../schema/college.schema';

/**
 * This function adds indexes to the MongoDB schemas to improve query performance.
 * It should be called when the application starts.
 */
export function addSchemaIndexes(): void {
  // User indexes
  UserSchema.index({ role: 1 });
  UserSchema.index({ collegeId: 1, role: 1 });
  // Email index is already defined in the schema with @Prop({ unique: true })
  UserSchema.index({ firebaseUid: 1 }, { sparse: true });
  UserSchema.index({ status: 1 });

  // Question indexes
  QuestionSchema.index({ subjectId: 1 });
  QuestionSchema.index({ topicId: 1 });
  QuestionSchema.index({ difficulty: 1 });
  QuestionSchema.index({ type: 1 });
  QuestionSchema.index({ status: 1 });
  QuestionSchema.index({ reviewStatus: 1 });
  QuestionSchema.index({ collegeId: 1, status: 1 });
  QuestionSchema.index({ collegeId: 1, subjectId: 1, status: 1 });
  QuestionSchema.index({ createdBy: 1 });
  QuestionSchema.index({ createdAt: 1 });

  // QuestionPaper indexes
  QuestionPaperSchema.index({ collegeId: 1 });
  QuestionPaperSchema.index({ subjectId: 1 });
  QuestionPaperSchema.index({ generatedBy: 1 });
  QuestionPaperSchema.index({ status: 1 });
  QuestionPaperSchema.index({ collegeId: 1, subjectId: 1 });
  QuestionPaperSchema.index({ collegeId: 1, createdAt: -1 });
  QuestionPaperSchema.index({ 'sections.questions.questionId': 1 });

  // UserActivity indexes
  UserActivitySchema.index({ userId: 1 });
  UserActivitySchema.index({ collegeId: 1 });
  UserActivitySchema.index({ activityType: 1 });
  UserActivitySchema.index({ timestamp: -1 });
  UserActivitySchema.index({ collegeId: 1, activityType: 1 });
  UserActivitySchema.index({ collegeId: 1, timestamp: -1 });
  UserActivitySchema.index({ collegeId: 1, activityType: 1, timestamp: -1 });

  // Download indexes
  DownloadSchema.index({ paperId: 1 });
  DownloadSchema.index({ userId: 1 });
  DownloadSchema.index({ collegeId: 1 });
  DownloadSchema.index({ downloadDate: -1 });
  DownloadSchema.index({ collegeId: 1, downloadDate: -1 });
  DownloadSchema.index({ downloadFormat: 1 });
  DownloadSchema.index({ userId: 1, downloadDate: -1 });
  DownloadSchema.index({ collegeId: 1, userId: 1 });

  // College indexes
  CollegeSchema.index({ status: 1 });
  CollegeSchema.index({ name: 1 });
}
