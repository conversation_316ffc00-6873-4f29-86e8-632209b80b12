# User Registration Validation Flow

## Overview

This document describes the enhanced user registration validation flow that validates teacher emails against college-specific teacher arrays instead of a single college contact email.

## Implementation Details

### 1. College Schema Changes

The `College` schema now includes a `teachers` array field:

```typescript
@ApiPropertyOptional({
  description: 'Array of teacher email addresses authorized for this college',
  example: ['<EMAIL>', '<EMAIL>'],
  type: [String]
})
@Prop({ type: [String], default: [] })
teachers: string[];
```

### 2. Registration Validation Flow

When a user attempts to register, the system follows this validation flow:

#### Step 1: Email Authorization Check
- Query: `College.findOne({ teachers: { $in: [email] } })`
- If NO college found with the email in teachers array:
  - **HTTP 400 Bad Request**
  - Message: "<PERSON><PERSON> is not authorized as a teacher. Please contact your college administrator."

#### Step 2: Existing User Check
- Query: `User.findOne({ email: email })`
- If user already exists:
  - **HTTP 409 Conflict**
  - Message: "User is already registered"

#### Step 3: User Creation
- If both validations pass:
  - Create new user with `collegeId` set to the matched college
  - Generate JWT token and return authentication response

### 3. Teacher Management Integration

#### Adding Teachers (`POST /colleges/{collegeId}/teachers`)
When college admins add teachers:
1. Create User document with `role: 'teacher'` and `collegeId`
2. Add teacher email to `college.teachers` array (if not already present)
3. Maintain data consistency between User collection and College.teachers array

#### Updating Teacher Email
When a teacher's email is updated:
1. Remove old email from `college.teachers` array
2. Add new email to `college.teachers` array
3. Update User document with new email

#### Removing Teachers
When a teacher is removed:
1. Remove teacher email from `college.teachers` array
2. Delete User document

### 4. API Endpoints Affected

#### Registration Endpoints
- `POST /auth/register`
- `POST /auth/register-teacher` (admin only)

Both endpoints now validate against college.teachers array.

#### Teacher Management Endpoints
- `POST /colleges/{collegeId}/teachers` - Adds email to college.teachers array
- `PUT /teachers/{id}` - Updates college.teachers array if email changes
- `DELETE /teachers/{id}` - Removes email from college.teachers array

### 5. Error Handling

| Scenario | HTTP Status | Error Message |
|----------|-------------|---------------|
| Email not in any college.teachers array | 400 Bad Request | "Email is not authorized as a teacher. Please contact your college administrator." |
| User already registered | 409 Conflict | "User is already registered" |
| Invalid input data | 400 Bad Request | "Bad request - Invalid input data" |

### 6. Data Consistency

The implementation ensures data consistency between:
- `User` collection (where teachers are stored as documents)
- `College.teachers` array (where authorized teacher emails are stored)

This dual storage approach allows for:
- Fast email validation during registration (MongoDB array query)
- Complete teacher data management through User documents
- Referential integrity between teachers and colleges

### 7. Migration Considerations

For existing deployments:
1. Add `teachers: []` field to existing College documents
2. Populate `college.teachers` arrays with emails from existing teacher User documents
3. Update any existing registration flows to use the new validation logic

### 8. Testing

The implementation includes comprehensive unit tests covering:
- Successful registration with authorized email
- Registration rejection for unauthorized emails
- Conflict detection for existing users
- Teacher management operations (add/update/remove)
- College.teachers array maintenance

Run tests with:
```bash
npm test -- --testPathPattern=auth.service.spec.ts
npm test -- --testPathPattern=teachers.service.spec.ts
```
