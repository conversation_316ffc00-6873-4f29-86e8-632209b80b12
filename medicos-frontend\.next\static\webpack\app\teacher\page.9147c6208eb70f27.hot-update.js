"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx":
/*!****************************************************************!*\
  !*** ./src/components/teacher/steps/difficulty-level-step.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DifficultyLevelStep: () => (/* binding */ DifficultyLevelStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _ui_option_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ui/option-button */ \"(app-pages-browser)/./src/components/teacher/ui/option-button.tsx\");\n/* harmony import */ var _ui_step_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/step-navigation */ \"(app-pages-browser)/./src/components/teacher/ui/step-navigation.tsx\");\n/* harmony import */ var _ui_info_message__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/info-message */ \"(app-pages-browser)/./src/components/teacher/ui/info-message.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Minus,Plus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ DifficultyLevelStep auto */ \n\n\n\n\n\nfunction DifficultyLevelStep(param) {\n    let { formData, updateFormData, onNext, onSkip, onBack, backDisabled } = param;\n    const handleModeSelect = (mode)=>{\n        if (mode === \"auto\") {\n            // Set default percentages for auto generation\n            updateFormData({\n                difficultyMode: mode,\n                difficultyLevels: {\n                    easyPercentage: 30,\n                    mediumPercentage: 50,\n                    hardPercentage: 20\n                }\n            });\n        } else {\n            updateFormData({\n                difficultyMode: mode\n            });\n        }\n    };\n    const adjustDifficulty = (level, amount)=>{\n        const newValue = Math.max(0, Math.min(100, formData.difficultyLevels[level] + amount));\n        updateFormData({\n            difficultyLevels: {\n                ...formData.difficultyLevels,\n                [level]: newValue\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold\",\n                        children: \"Select Difficulty Level\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"Choose the complexity of your questions: Easy, Medium, Hard, or Mixed\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_option_button__WEBPACK_IMPORTED_MODULE_1__.OptionButton, {\n                            selected: formData.difficultyMode === \"auto\",\n                            onClick: ()=>handleModeSelect(\"auto\"),\n                            grouped: true,\n                            position: \"left\",\n                            className: \"rounded-none border-0\",\n                            children: \"Auto generation\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-px bg-gray-200\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_option_button__WEBPACK_IMPORTED_MODULE_1__.OptionButton, {\n                            selected: formData.difficultyMode === \"custom\",\n                            onClick: ()=>handleModeSelect(\"custom\"),\n                            grouped: true,\n                            position: \"right\",\n                            className: \"rounded-none border-0\",\n                            children: \"Customization\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-left font-medium\",\n                                children: \"Easy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"easy\", -10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-[50px] text-center font-medium\",\n                                        children: [\n                                            formData.difficultyLevels.easyPercentage,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"easy\", 10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-left font-medium\",\n                                children: \"Medium\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"medium\", -10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-[50px] text-center font-medium\",\n                                        children: [\n                                            formData.difficultyLevels.medium,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"medium\", 10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-left font-medium\",\n                                children: \"Hard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"hard\", -10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"w-[50px] text-center font-medium\",\n                                        children: [\n                                            formData.difficultyLevels.hard,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"icon\",\n                                        onClick: ()=>adjustDifficulty(\"hard\", 10),\n                                        className: \"rounded-sm border-[#E5E7EB] h-8\",\n                                        disabled: formData.difficultyMode === \"auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Minus_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_navigation__WEBPACK_IMPORTED_MODULE_2__.StepNavigation, {\n                onNext: onNext,\n                onSkip: onSkip,\n                onBack: onBack,\n                backDisabled: backDisabled\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_info_message__WEBPACK_IMPORTED_MODULE_3__.InfoMessage, {\n                message: \"Please select a difficulty level before proceeding. Your choice will determine the complexity of the questions generated.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\steps\\\\difficulty-level-step.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_c = DifficultyLevelStep;\nvar _c;\n$RefreshReg$(_c, \"DifficultyLevelStep\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\n"));

/***/ })

});