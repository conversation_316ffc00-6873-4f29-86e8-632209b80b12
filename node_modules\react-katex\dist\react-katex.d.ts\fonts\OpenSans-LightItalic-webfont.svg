<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg">
<metadata></metadata>
<defs>
<font id="open_sanslight_italic" horiz-adv-x="1128" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="532" />
<glyph unicode="&#xfb00;" horiz-adv-x="1155" d="M-131 -492q-48 0 -102 19v90q53 -16 100 -16q88 0 134 53t75 186l246 1166h-205l14 67l205 14l35 160q35 168 116.5 244t227.5 76q73 0 166 -31l-25 -80q-87 27 -147 27q-96 0 -153.5 -53.5t-84.5 -178.5l-35 -164h477l35 160q35 168 116.5 244t227.5 76q73 0 166 -31 l-24 -80q-87 27 -148 27q-97 0 -154.5 -54.5t-82.5 -177.5l-35 -164h248l-17 -81h-248l-252 -1190q-34 -165 -105.5 -236.5t-193.5 -71.5q-48 0 -102 19v90q53 -16 100 -16q88 0 134 53t75 186l244 1166h-477l-252 -1190q-33 -161 -104 -234.5t-195 -73.5z" />
<glyph unicode="&#xfb01;" horiz-adv-x="1040" d="M0 0zM739 0h-98l231 1087h96zM915 1366q0 55 22 88t60 33q57 0 57 -72q0 -57 -22 -90t-57 -33q-29 0 -44.5 19.5t-15.5 54.5zM-148 -492q-48 0 -102 19v90q53 -16 100 -16q88 0 134 53t75 186l246 1166h-205l14 67l205 14l35 160q35 168 116.5 244t227.5 76q73 0 166 -31 l-25 -80q-87 27 -147 27q-96 0 -153.5 -53.5t-84.5 -178.5l-35 -164h248l-16 -81h-248l-252 -1190q-33 -161 -104 -234.5t-195 -73.5z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1042" d="M0 0zM737 0h-94l334 1556h94zM-148 -492q-48 0 -102 19v90q53 -16 100 -16q88 0 134 53t75 186l246 1166h-205l14 67l205 14l35 160q35 168 116.5 244t227.5 76q73 0 166 -31l-25 -80q-87 27 -147 27q-96 0 -153.5 -53.5t-84.5 -178.5l-35 -164h248l-16 -81h-248 l-252 -1190q-33 -161 -104 -234.5t-195 -73.5z" />
<glyph unicode="&#xfb03;" horiz-adv-x="1616" d="M0 0zM-148 -492q-48 0 -102 19v90q53 -16 100 -16q88 0 134 53t75 186l246 1166h-205l14 67l205 14l35 160q35 168 116.5 244t227.5 76q73 0 166 -31l-25 -80q-87 27 -147 27q-96 0 -153.5 -53.5t-84.5 -178.5l-35 -164h477l35 160q35 168 116.5 244t227.5 76 q73 0 166 -31l-24 -80q-87 27 -148 27q-97 0 -154.5 -54.5t-82.5 -177.5l-35 -164h248l-17 -81h-248l-252 -1190q-34 -165 -105.5 -236.5t-193.5 -71.5q-48 0 -102 19v90q53 -16 100 -16q88 0 134 53t75 186l244 1166h-477l-252 -1190q-33 -161 -104 -234.5t-195 -73.5z M1315 0h-98l231 1087h96zM1491 1366q0 55 22 88t60 33q57 0 57 -72q0 -57 -22 -90t-57 -33q-29 0 -44.5 19.5t-15.5 54.5z" />
<glyph unicode="&#xfb04;" horiz-adv-x="1626" d="M0 0zM-148 -492q-48 0 -102 19v90q53 -16 100 -16q88 0 134 53t75 186l246 1166h-205l14 67l205 14l35 160q35 168 116.5 244t227.5 76q73 0 166 -31l-25 -80q-87 27 -147 27q-96 0 -153.5 -53.5t-84.5 -178.5l-35 -164h477l35 160q35 168 116.5 244t227.5 76 q73 0 166 -31l-24 -80q-87 27 -148 27q-97 0 -154.5 -54.5t-82.5 -177.5l-35 -164h248l-17 -81h-248l-252 -1190q-34 -165 -105.5 -236.5t-193.5 -71.5q-48 0 -102 19v90q53 -16 100 -16q88 0 134 53t75 186l244 1166h-477l-252 -1190q-33 -161 -104 -234.5t-195 -73.5z M1321 0h-94l334 1556h94z" />
<glyph horiz-adv-x="2048" />
<glyph horiz-adv-x="2048" />
<glyph unicode="&#xd;" horiz-adv-x="1044" />
<glyph unicode=" "  horiz-adv-x="532" />
<glyph unicode="&#x09;" horiz-adv-x="532" />
<glyph unicode="&#xa0;" horiz-adv-x="532" />
<glyph unicode="!" horiz-adv-x="502" d="M248 377h-62l203 1085h119zM80 57q0 56 25 88.5t69 32.5q66 0 66 -72q0 -53 -25 -87.5t-68 -34.5q-67 0 -67 73z" />
<glyph unicode="&#x22;" horiz-adv-x="721" d="M455 1462l-146 -528h-61l80 528h127zM784 1462l-145 -528h-61l79 528h127z" />
<glyph unicode="#" horiz-adv-x="1323" d="M389 530l119 398h-297l8 80h311l134 454h90l-136 -454h365l135 454h86l-135 -454h285l-8 -80h-302l-118 -398h303l-8 -79h-320l-133 -451h-90l135 451h-360l-134 -451h-88l134 451h-283l8 79h299zM475 530h363l120 398h-362z" />
<glyph unicode="$" d="M928 467q0 -151 -110.5 -243t-297.5 -103l-53 -240h-82l51 240q-79 2 -158 18t-137 43v94q65 -34 153.5 -53.5t160.5 -19.5l110 512q-110 53 -153 91t-66.5 87.5t-23.5 116.5q0 155 105.5 250.5t272.5 99.5l41 192h80l-41 -192q149 -5 277 -68l-35 -78q-110 61 -256 70 l-109 -514q124 -60 172.5 -99.5t73.5 -88.5t25 -115zM537 203q142 7 223.5 74.5t81.5 183.5q0 76 -48 129.5t-157 97.5zM686 1284q-134 -9 -205 -80t-71 -186q0 -78 37 -128.5t137 -96.5z" />
<glyph unicode="%" horiz-adv-x="1556" d="M1458 1462l-1139 -1462h-106l1135 1462h110zM1520 612q0 -175 -50 -327t-136 -230t-197 -78q-112 0 -172 75.5t-60 223.5q0 160 52 312t138 229.5t193 77.5q121 0 176.5 -71.5t55.5 -211.5zM1284 821q-80 0 -146.5 -72.5t-106.5 -202.5t-40 -263q0 -116 41 -174t113 -58 q130 0 209.5 166.5t79.5 386.5q0 109 -36 163t-114 54zM760 1198q0 -175 -50 -327t-136 -230t-197 -78q-112 0 -172 75.5t-60 223.5q0 160 52 312t138 229.5t193 77.5q232 0 232 -283zM524 1407q-79 0 -145 -71.5t-107 -203t-41 -264.5q0 -115 41 -173t113 -58 q84 0 148.5 72t102.5 204t38 277q0 109 -36 163t-114 54z" />
<glyph unicode="&#x26;" horiz-adv-x="1331" d="M748 1395q-125 0 -194 -68t-69 -192q0 -132 109 -281q203 89 279.5 163.5t76.5 182.5q0 91 -56.5 143t-145.5 52zM465 63q108 0 211.5 42.5t222.5 146.5l-352 493q-164 -79 -232 -134.5t-103.5 -124t-35.5 -158.5q0 -119 78 -192t211 -73zM78 324q0 162 99 277.5 t325 215.5l-41 67q-78 128 -78 251q0 157 101 253.5t264 96.5q145 0 227 -76.5t82 -206.5q0 -85 -41 -154t-121 -128t-256 -138l330 -463q73 75 135.5 176.5t91.5 186.5h111q-102 -247 -285 -436l184 -246h-123l-131 184q-121 -108 -242 -156t-266 -48q-167 0 -266.5 94 t-99.5 250z" />
<glyph unicode="'" horiz-adv-x="403" d="M461 1462l-146 -528h-61l80 528h127z" />
<glyph unicode="(" horiz-adv-x="526" d="M104 270q0 343 122 633t382 559h105q-259 -276 -384.5 -568t-125.5 -618q0 -317 127 -600h-80q-146 262 -146 594z" />
<glyph unicode=")" horiz-adv-x="526" d="M453 868q0 -345 -123.5 -636t-380.5 -556h-105q257 274 383.5 566.5t126.5 619.5q0 148 -28.5 294t-98.5 306h80q146 -262 146 -594z" />
<glyph unicode="*" horiz-adv-x="1137" d="M834 1540l-109 -405l438 8l-8 -107l-416 29l181 -401l-115 -37l-135 417l-285 -348l-78 78l318 318l-392 125l39 102l394 -168l47 408z" />
<glyph unicode="+" d="M563 672h-401v100h401v404h101v-404h401v-100h-401v-400h-101v400z" />
<glyph unicode="," horiz-adv-x="451" d="M250 238l8 -23q-34 -92 -114 -233.5t-160 -245.5h-74q79 132 141 271t88 231h111z" />
<glyph unicode="-" horiz-adv-x="629" d="M82 502l18 90h457l-16 -90h-459z" />
<glyph unicode="." horiz-adv-x="485" d="M82 55q0 56 25 88.5t69 32.5q66 0 66 -72q0 -53 -25 -87.5t-67 -34.5q-68 0 -68 73z" />
<glyph unicode="/" horiz-adv-x="641" d="M827 1462l-817 -1462h-110l815 1462h112z" />
<glyph unicode="0" d="M1075 1016q0 -201 -41 -405t-116.5 -346t-183.5 -213.5t-242 -71.5q-176 0 -264.5 126.5t-88.5 387.5q0 186 44.5 381.5t124 334t187 207t240.5 68.5q340 0 340 -469zM723 1397q-134 0 -241.5 -113t-173.5 -329t-66 -451q0 -222 62.5 -329t197.5 -107q139 0 244 112 t166 337t61 489q0 199 -59.5 295t-190.5 96z" />
<glyph unicode="1" d="M537 0h-105l225 1055q19 92 74 293q-42 -36 -75.5 -61t-249.5 -161l-52 78l406 258h90z" />
<glyph unicode="2" d="M909 0h-870l22 104l449 402q198 177 284 276.5t126.5 186.5t40.5 180q0 112 -66 178t-197 66q-176 0 -333 -129l-54 73q180 146 394 146q173 0 268.5 -85t95.5 -237q0 -110 -43.5 -208.5t-141.5 -211.5t-311 -303l-383 -338v-4h736z" />
<glyph unicode="3" d="M1049 1174q0 -162 -106 -275t-286 -143v-4q117 -24 185.5 -115.5t68.5 -226.5q0 -134 -64 -233t-179.5 -148t-274.5 -49q-96 0 -184.5 20.5t-153.5 52.5v101q172 -86 344 -86q197 0 303.5 89.5t106.5 252.5q0 145 -89 223t-247 78h-117l21 96h110q209 0 333 95.5 t124 258.5q0 114 -63.5 175t-188.5 61q-167 0 -344 -131l-49 75q84 67 188 104.5t218 37.5q161 0 252.5 -82.5t91.5 -226.5z" />
<glyph unicode="4" d="M1071 371h-264l-80 -371h-96l80 371h-688l20 96l881 1010h118l-215 -1018h265zM729 459q79 369 119 558.5t86 354.5h-4q-66 -91 -129 -166l-651 -747h579z" />
<glyph unicode="5" d="M582 879q188 0 292.5 -102t104.5 -279q0 -237 -148 -377.5t-407 -140.5q-84 0 -177.5 20t-158.5 51v107q170 -90 340 -90q208 0 328.5 114.5t120.5 313.5q0 140 -85 219.5t-225 79.5q-133 0 -243 -41l-66 49l193 659h624l-18 -96h-541l-149 -516q98 29 215 29z" />
<glyph unicode="6" d="M170 428q0 283 105 544.5t269.5 385t383.5 123.5q123 0 182 -21l-18 -90q-86 23 -170 23q-233 0 -393.5 -174t-233.5 -502h8q68 94 164 143t211 49q161 0 250.5 -100.5t89.5 -282.5q0 -156 -60 -281t-171 -195t-257 -70q-171 0 -265.5 119t-94.5 329zM543 68 q112 0 196.5 58.5t130 162t45.5 229.5q0 146 -67 224.5t-195 78.5q-81 0 -154 -31.5t-129 -87t-78 -115t-22 -173.5q0 -164 72.5 -255t200.5 -91z" />
<glyph unicode="7" d="M244 0l796 1366h-766l23 96h858l-20 -110l-779 -1352h-112z" />
<glyph unicode="8" d="M737 1485q163 0 258 -85t95 -229q0 -138 -84 -234.5t-285 -172.5q130 -78 190 -170.5t60 -208.5t-58 -208t-165.5 -144.5t-260.5 -52.5q-178 0 -283.5 92.5t-105.5 253.5q0 159 100.5 268.5t321.5 187.5q-100 72 -144 152t-44 180q0 159 114 265t291 106zM610 733 q-218 -73 -313.5 -167.5t-95.5 -225.5q0 -136 77.5 -206.5t219.5 -70.5q168 0 270 91t102 233q0 104 -62 189t-198 157zM727 1399q-130 0 -214.5 -82t-84.5 -203q0 -91 41.5 -159t157.5 -142q192 62 279 144t87 206q0 109 -70.5 172.5t-195.5 63.5z" />
<glyph unicode="9" d="M1059 1032q0 -288 -101.5 -548t-263.5 -382t-393 -122q-114 0 -186 22v90q87 -29 192 -29q474 0 627 674h-8q-140 -192 -367 -192q-162 0 -255 105t-93 284q0 155 59.5 281t170.5 196t257 70q174 0 267.5 -115.5t93.5 -333.5zM686 1393q-112 0 -197.5 -58.5 t-130.5 -162.5t-45 -230q0 -145 67.5 -225t192.5 -80q83 0 157.5 32.5t129 87.5t76.5 114t22 176q0 166 -71 256t-201 90z" />
<glyph unicode=":" horiz-adv-x="485" d="M260 989q0 57 25.5 89t68.5 32q66 0 66 -72q0 -55 -25 -89t-67 -34q-68 0 -68 74zM102 55q0 56 25.5 88.5t69.5 32.5q65 0 65 -72q0 -55 -25.5 -88.5t-66.5 -33.5q-68 0 -68 73z" />
<glyph unicode=";" horiz-adv-x="485" d="M287 238l8 -23q-35 -96 -118.5 -242t-156.5 -237h-73q79 132 141 271t88 231h111zM266 989q0 57 25.5 89t68.5 32q66 0 66 -72q0 -55 -25 -89t-67 -34q-68 0 -68 74z" />
<glyph unicode="&#x3c;" d="M1051 262l-914 414v74l914 471v-103l-801 -399l801 -350v-107z" />
<glyph unicode="=" d="M168 885v100h903v-100h-903zM168 461v98h903v-98h-903z" />
<glyph unicode="&#x3e;" d="M170 369l801 350l-801 399v103l915 -471v-74l-915 -414v107z" />
<glyph unicode="?" horiz-adv-x="799" d="M242 362l6 29q29 132 82 206.5t157 147.5q118 84 175 145.5t86.5 127.5t29.5 141q0 108 -67.5 170t-182.5 62q-139 0 -307 -101l-39 86q85 49 171.5 78t187.5 29q159 0 250.5 -84.5t91.5 -229.5q0 -127 -66 -234t-231 -226q-85 -61 -132.5 -108.5t-73 -95t-46.5 -143.5 h-92zM170 59q0 56 25 88.5t69 32.5q66 0 66 -71q0 -54 -24.5 -88.5t-67.5 -34.5q-68 0 -68 73z" />
<glyph unicode="@" horiz-adv-x="1724" d="M1688 858q0 -179 -56 -323.5t-154.5 -227t-211.5 -82.5q-98 0 -154.5 55t-56.5 144h-4q-54 -97 -132.5 -148t-168.5 -51q-112 0 -178 73t-66 202q0 156 63 283t178 198.5t261 71.5q122 0 252 -52l-84 -315q-39 -140 -39 -221q0 -71 34.5 -111.5t100.5 -40.5 q86 0 160 73.5t117.5 198t43.5 251.5q0 156 -65 277t-187 188t-292 67q-235 0 -424.5 -108.5t-295.5 -304t-106 -439.5q0 -288 155 -449t435 -161q207 0 420 82v-90q-210 -82 -428 -82q-203 0 -357.5 82.5t-238.5 239t-84 370.5q0 276 121.5 493.5t337 337t473.5 119.5 q189 0 330.5 -72.5t221 -213t79.5 -314.5zM1008 969q-113 0 -204.5 -59t-142.5 -165.5t-51 -238.5q0 -92 40.5 -142.5t113.5 -50.5q101 0 180.5 89t124.5 255l78 289q-66 23 -139 23z" />
<glyph unicode="A" horiz-adv-x="1059" d="M805 520h-512l-287 -520h-117l822 1468h67l201 -1468h-105zM793 612l-39 291q-31 242 -39 402q-30 -63 -64.5 -130t-306.5 -563h449z" />
<glyph unicode="B" horiz-adv-x="1202" d="M412 1462h379q190 0 290.5 -84t100.5 -241q0 -153 -90 -249t-254 -124v-4q125 -31 188.5 -113.5t63.5 -204.5q0 -205 -140.5 -323.5t-390.5 -118.5h-457zM377 811h278q206 0 313 81t107 238q0 119 -78 180.5t-229 61.5h-272zM358 721l-135 -631h342q201 0 309.5 87.5 t108.5 256.5q0 145 -90 216t-275 71h-260z" />
<glyph unicode="C" horiz-adv-x="1169" d="M961 1389q-197 0 -351.5 -104.5t-245 -304.5t-90.5 -441q0 -225 110.5 -346t317.5 -121q140 0 304 51v-94q-156 -49 -316 -49q-252 0 -386 145t-134 410q0 266 104.5 488t284.5 341t402 119q177 0 307 -68l-45 -90q-55 30 -124.5 47t-137.5 17z" />
<glyph unicode="D" horiz-adv-x="1350" d="M1288 879q0 -253 -109.5 -461.5t-300.5 -313t-446 -104.5h-330l310 1462h305q282 0 426.5 -147.5t144.5 -435.5zM430 90q226 0 394.5 94.5t261 275.5t92.5 412q0 498 -476 498h-206l-275 -1280h209z" />
<glyph unicode="E" horiz-adv-x="1067" d="M829 0h-727l310 1462h727l-21 -94h-624l-117 -553h590l-21 -94h-588l-135 -627h627z" />
<glyph unicode="F" horiz-adv-x="981" d="M205 0h-103l310 1462h708l-20 -94h-604l-134 -620h570l-21 -95h-569z" />
<glyph unicode="G" horiz-adv-x="1374" d="M856 735h406l-150 -682q-211 -73 -405 -73q-257 0 -397 146t-140 421q0 265 105 483.5t283.5 335.5t395.5 117q113 0 203 -19t184 -59l-38 -94q-110 46 -189.5 62t-167.5 16q-184 0 -339 -107.5t-244 -301.5t-89 -433q0 -229 114.5 -352t326.5 -123q155 0 309 47l117 526 h-303z" />
<glyph unicode="H" horiz-adv-x="1366" d="M1063 0h-100l151 719h-760l-149 -719h-103l310 1462h102l-139 -649h760l137 649h100z" />
<glyph unicode="I" horiz-adv-x="504" d="M102 0l310 1462h98l-309 -1462h-99z" />
<glyph unicode="J" horiz-adv-x="477" d="M-180 -360q-48 0 -88 8t-56 16l11 92q57 -20 137 -20q213 0 262 241l309 1485h105l-314 -1491q-35 -170 -125 -250.5t-241 -80.5z" />
<glyph unicode="K" horiz-adv-x="1122" d="M979 0h-111l-342 788l-190 -153l-131 -635h-103l310 1462h102l-158 -723l133 121l680 602h138l-699 -610z" />
<glyph unicode="L" horiz-adv-x="938" d="M102 0l310 1462h102l-289 -1366h621l-23 -96h-721z" />
<glyph unicode="M" horiz-adv-x="1669" d="M772 205l733 1257h150l-301 -1462h-101l191 901q79 369 100 447h-6l-780 -1348h-51l-222 1348h-6q-20 -154 -78 -426l-196 -922h-96l309 1462h143l205 -1257h6z" />
<glyph unicode="N" horiz-adv-x="1372" d="M1069 0h-86l-516 1284h-8q-23 -149 -48 -273t-214 -1011h-95l310 1462h80l522 -1294h8q23 176 74 416l188 878h94z" />
<glyph unicode="O" horiz-adv-x="1464" d="M1403 911q0 -255 -94 -481.5t-252 -338t-365 -111.5q-250 0 -385 149t-135 430q0 262 93 477.5t255 331t373 115.5q247 0 378.5 -148.5t131.5 -423.5zM879 1389q-174 0 -308.5 -101t-214.5 -298t-80 -417q0 -245 109.5 -373t319.5 -128q169 0 300 98.5t210 300t79 430.5 q0 240 -104.5 364t-310.5 124z" />
<glyph unicode="P" horiz-adv-x="1145" d="M1145 1102q0 -500 -610 -500h-201l-129 -602h-103l310 1462h315q202 0 310 -92.5t108 -267.5zM350 694h191q252 0 373.5 96.5t121.5 305.5q0 274 -329 274h-211z" />
<glyph unicode="Q" horiz-adv-x="1464" d="M1403 911q0 -216 -70 -418t-186.5 -324t-274.5 -167l267 -350h-142l-231 332l-74 -4q-250 0 -385 149t-135 430q0 262 93 477.5t255 331t373 115.5q247 0 378.5 -148.5t131.5 -423.5zM879 1389q-174 0 -308.5 -101t-214.5 -298t-80 -417q0 -245 109.5 -373t319.5 -128 q169 0 300 98.5t210 300t79 430.5q0 240 -104.5 364t-310.5 124z" />
<glyph unicode="R" horiz-adv-x="1145" d="M336 633l-131 -633h-103l310 1462h303q430 0 430 -360q0 -182 -103.5 -303t-281.5 -152q201 -591 221 -647h-111l-211 633h-323zM358 725h252q208 0 317 95.5t109 281.5q0 268 -329 268h-211z" />
<glyph unicode="S" horiz-adv-x="1020" d="M895 403q0 -126 -65.5 -224.5t-179.5 -148.5t-269 -50q-88 0 -172.5 17t-171.5 58v109q163 -92 348 -92q188 0 295.5 86.5t107.5 232.5q0 61 -17 104.5t-52.5 78.5t-91 68t-131.5 75q-150 76 -209.5 164t-59.5 206t59 207.5t165 139t237 49.5q99 0 180 -17.5t168 -60.5 l-32 -94q-66 40 -151.5 63t-164.5 23q-163 0 -259.5 -82.5t-96.5 -218.5q0 -103 49 -170t182 -133q154 -79 213.5 -130t89 -113t29.5 -147z" />
<glyph unicode="T" horiz-adv-x="985" d="M438 0h-102l289 1368h-432l20 94h973l-19 -94h-440z" />
<glyph unicode="U" horiz-adv-x="1370" d="M1395 1462l-207 -977q-58 -270 -197 -387.5t-375 -117.5q-440 0 -440 401q0 83 27 201l186 880h103l-193 -899q-20 -89 -20 -184q0 -309 342 -309q195 0 307.5 96.5t158.5 318.5l207 977h101z" />
<glyph unicode="V" horiz-adv-x="1079" d="M457 147q51 111 124 247l572 1068h117l-799 -1462h-88l-182 1462h100l117 -950q26 -217 35 -365h4z" />
<glyph unicode="W" horiz-adv-x="1702" d="M1018 1341q-27 -76 -62 -153.5t-563 -1187.5h-82l-73 1462h100l47 -1031l4 -165l-2 -86h6q85 226 170 398l434 884h105l61 -878q19 -266 19 -410h6q30 86 61.5 163t493.5 1125h108q-169 -365 -330.5 -731t-328.5 -731h-78l-78 1075q-11 142 -11 219l1 47h-8z" />
<glyph unicode="X" horiz-adv-x="971" d="M879 0h-107l-254 678l-526 -678h-127l608 766l-272 696h106l240 -626l483 626h119l-555 -719z" />
<glyph unicode="Y" horiz-adv-x="965" d="M494 645l544 817h117l-631 -932l-108 -530h-105l119 545l-237 917h100z" />
<glyph unicode="Z" d="M913 0h-925l22 92l1069 1276h-764l23 94h887l-19 -88l-1069 -1280h799z" />
<glyph unicode="[" horiz-adv-x="537" d="M369 -324h-426l376 1786h429l-19 -90h-330l-340 -1605h330z" />
<glyph unicode="\" horiz-adv-x="641" d="M295 1462l242 -1462h-82l-246 1462h86z" />
<glyph unicode="]" horiz-adv-x="537" d="M203 1462h426l-377 -1786h-428l18 91h330l340 1605h-330z" />
<glyph unicode="^" horiz-adv-x="1047" d="M70 569l587 906h91l260 -906h-105l-217 809l-500 -809h-116z" />
<glyph unicode="_" horiz-adv-x="801" d="M625 -291h-807l18 86h807z" />
<glyph unicode="`" horiz-adv-x="1135" d="M766 1241h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="a" horiz-adv-x="1133" d="M655 1104q232 0 279 -219h6l59 202h80l-229 -1087h-82l45 274h-6q-84 -142 -187 -218t-237 -76q-281 0 -281 374q0 197 75 376t200.5 276.5t277.5 97.5zM406 70q89 0 177.5 62t159 166t107.5 230t37 213q0 79 -26 141.5t-77 99t-127 36.5q-124 0 -224 -82t-164 -245.5 t-64 -325.5q0 -152 50 -223.5t151 -71.5z" />
<glyph unicode="b" horiz-adv-x="1151" d="M500 -16q-113 0 -186 59t-97 166h-6l-55 -209h-80l327 1556h95q-131 -628 -162 -751h6q93 156 199 229.5t231 73.5q281 0 281 -375q0 -203 -76 -380t-201 -273t-276 -96zM750 1018q-91 0 -180 -61.5t-160.5 -169.5t-106.5 -235t-35 -206q0 -129 64 -202.5t166 -73.5 q124 0 224 83t164 245t64 325q0 152 -49 223.5t-151 71.5z" />
<glyph unicode="c" horiz-adv-x="887" d="M477 -20q-174 0 -274.5 110.5t-100.5 306.5q0 193 73.5 361.5t198.5 257t290 88.5q134 0 241 -43l-28 -90q-107 47 -218 47q-129 0 -232.5 -77t-162.5 -222t-59 -320q0 -158 73.5 -243.5t208.5 -85.5q71 0 131.5 13t131.5 46v-92q-116 -57 -273 -57z" />
<glyph unicode="d" horiz-adv-x="1133" d="M655 1104q230 0 279 -219h4q12 66 143 671h99l-330 -1556h-82l45 274h-6q-173 -294 -424 -294q-281 0 -281 374q0 193 71.5 370t197.5 278.5t284 101.5zM406 70q89 0 178.5 62.5t160 168t106.5 231t36 209.5q0 126 -61.5 201.5t-168.5 75.5q-124 0 -224 -83t-164 -242.5 t-64 -327.5q0 -295 201 -295z" />
<glyph unicode="e" horiz-adv-x="928" d="M469 -20q-173 0 -270 109.5t-97 305.5q0 181 71 347t195.5 264t274.5 98q114 0 182 -61t68 -166q0 -181 -163.5 -276t-485.5 -95h-33q-6 -44 -6 -98q0 -165 74 -251.5t213 -86.5q132 0 276 73v-94q-140 -69 -299 -69zM631 1018q-131 0 -243.5 -115.5t-162.5 -308.5h49 q517 0 517 270q0 67 -43.5 110.5t-116.5 43.5z" />
<glyph unicode="f" horiz-adv-x="578" d="M-131 -492q-48 0 -102 19v90q53 -16 100 -16q88 0 134 53t75 186l246 1166h-205l14 67l205 14l35 160q35 168 116.5 244t227.5 76q73 0 166 -31l-25 -80q-87 27 -147 27q-96 0 -153.5 -53.5t-84.5 -178.5l-35 -164h248l-16 -81h-248l-252 -1190q-33 -161 -104 -234.5 t-195 -73.5z" />
<glyph unicode="g" horiz-adv-x="1040" d="M1100 1087l-17 -79l-243 -11q26 -28 43.5 -84t17.5 -114q0 -109 -54.5 -206.5t-148 -145.5t-213.5 -48q-63 0 -77 9q-80 -33 -124 -73t-44 -81t31.5 -64.5t113.5 -31.5l121 -11q346 -31 346 -264q0 -112 -65 -197.5t-187 -131.5t-291 -46q-186 0 -291.5 72t-105.5 203 q0 236 309 334q-78 42 -78 123q0 123 191 202q-71 36 -110.5 105.5t-39.5 157.5q0 111 53.5 204t148 146t206.5 53q69 0 147 -21h361zM14 -207q0 -101 81 -150t224 -49q203 0 317 74.5t114 204.5q0 85 -62.5 130.5t-218.5 57.5l-160 15q-157 -45 -226 -114.5t-69 -168.5z M285 711q0 -112 58.5 -170t164.5 -58q88 0 154 37t102.5 114t36.5 169q0 104 -56 161.5t-157 57.5q-93 0 -161 -43t-105 -116t-37 -152z" />
<glyph unicode="h" horiz-adv-x="1143" d="M764 0l149 692q21 92 21 156q0 80 -43.5 125t-134.5 45q-112 0 -210.5 -67t-166 -188t-103.5 -286l-102 -477h-98l332 1556h96l-86 -411q-44 -200 -66 -279h6q78 113 186.5 175.5t229.5 62.5q124 0 192 -65t68 -183q0 -70 -24 -182l-148 -674h-98z" />
<glyph unicode="i" horiz-adv-x="475" d="M174 0h-98l231 1087h96zM350 1366q0 55 22 88t60 33q57 0 57 -72q0 -57 -22 -90t-57 -33q-29 0 -44.5 19.5t-15.5 54.5z" />
<glyph unicode="j" horiz-adv-x="475" d="M-166 -492q-62 0 -113 19v92q47 -22 113 -22q82 0 128.5 51.5t72.5 177.5l266 1261h96l-268 -1271q-35 -165 -106.5 -236.5t-188.5 -71.5zM350 1366q0 55 22 88t60 33q57 0 57 -72q0 -57 -22 -90t-57 -33q-29 0 -44.5 19.5t-15.5 54.5z" />
<glyph unicode="k" horiz-adv-x="944" d="M270 477l609 610h125l-474 -469l297 -618h-106l-264 559l-205 -188l-80 -371h-96l330 1556h96l-166 -780l-70 -299h4z" />
<glyph unicode="l" horiz-adv-x="475" d="M170 0h-94l334 1556h94z" />
<glyph unicode="m" horiz-adv-x="1751" d="M711 0l147 674q25 125 25 162q0 182 -154 182q-106 0 -200 -67.5t-159 -188.5t-100 -287l-100 -475h-98l231 1087h80l-33 -210h6q80 113 181.5 170t212.5 57q106 0 163 -67t60 -195h6q77 129 181 195.5t222 66.5q117 0 182.5 -61.5t65.5 -176.5q0 -29 -2.5 -56.5 t-19.5 -119.5l-152 -690h-100l149 680q25 120 25 176q0 77 -43 119.5t-119 42.5q-157 0 -277.5 -137.5t-168.5 -362.5l-109 -518h-102z" />
<glyph unicode="n" horiz-adv-x="1143" d="M764 0l149 692q21 92 21 156q0 80 -43.5 125t-134.5 45q-112 0 -210.5 -67t-166 -187.5t-103.5 -286.5l-102 -477h-98l231 1087h82l-37 -221h6q164 238 416 238q130 0 195 -64t65 -184q0 -70 -24 -182l-148 -674h-98z" />
<glyph unicode="o" horiz-adv-x="1124" d="M649 1108q180 0 278.5 -108.5t98.5 -299.5q0 -197 -71.5 -368.5t-195.5 -261.5t-286 -90q-184 0 -279.5 109.5t-95.5 313.5q0 191 73 358t197 257t281 90zM641 1022q-124 0 -223 -78.5t-158 -225t-59 -310.5q0 -342 282 -342q127 0 225.5 77.5t157 228t58.5 330.5 q0 154 -73 237t-210 83z" />
<glyph unicode="p" horiz-adv-x="1149" d="M498 -16q-230 0 -279 219h-4q-13 -72 -149 -695h-99l336 1579h82l-45 -274h6q91 153 195.5 224t228.5 71q135 0 208 -92.5t73 -282.5q0 -195 -72 -371t-197.5 -277t-283.5 -101zM748 1018q-87 0 -174 -58.5t-161.5 -167.5t-110.5 -237.5t-36 -208.5q0 -125 61.5 -200.5 t168.5 -75.5q124 0 225 84t164 243.5t63 325.5q0 295 -200 295z" />
<glyph unicode="q" horiz-adv-x="1157" d="M651 1104q109 0 183.5 -58t99.5 -167h6l55 208h80l-327 -1556h-95l98 470l64 282h-6q-93 -156 -199 -229.5t-231 -73.5q-281 0 -281 374q0 201 75.5 379t200.5 274.5t277 96.5zM401 70q92 0 182 62.5t160.5 171.5t105 236.5t34.5 200.5q0 130 -63.5 203.5t-166.5 73.5 q-124 0 -224 -83t-164 -245t-64 -325q0 -143 45.5 -219t154.5 -76z" />
<glyph unicode="r" horiz-adv-x="752" d="M713 1106q69 0 123 -14l-21 -93q-47 15 -113 15q-94 0 -179 -64t-153 -192t-100 -277l-100 -481h-98l231 1087h80l-29 -204h6q73 94 123 135.5t106.5 64.5t123.5 23z" />
<glyph unicode="s" horiz-adv-x="827" d="M713 295q0 -144 -103 -229.5t-280 -85.5q-173 0 -305 75v107q74 -46 153 -71t148 -25q138 0 211 57.5t73 163.5q0 42 -15.5 74t-50 61.5t-132.5 85.5q-148 80 -200 145.5t-52 159.5q0 128 98.5 209.5t259.5 81.5q75 0 158.5 -17.5t140.5 -46.5l-35 -88q-136 64 -264 64 q-116 0 -186 -53t-70 -138q0 -55 17 -88t60.5 -68.5t119.5 -76.5q114 -63 161.5 -103.5t70 -86.5t22.5 -107z" />
<glyph unicode="t" horiz-adv-x="616" d="M694 1087l-18 -81h-283l-135 -635q-22 -99 -22 -164q0 -139 126 -139q68 0 152 26v-86q-101 -28 -170 -28q-99 0 -153 54.5t-54 158.5q0 73 29 206l129 607h-182l14 67l184 17l97 253h55l-55 -256h286z" />
<glyph unicode="u" horiz-adv-x="1143" d="M381 1087l-152 -698q-22 -106 -22 -158q0 -74 47.5 -117.5t138.5 -43.5q110 0 207.5 65.5t164 187t99.5 279.5l105 485h98l-231 -1087h-80l28 205h-6q-167 -221 -403 -221q-131 0 -198.5 62t-67.5 181q0 60 22 170l150 690h100z" />
<glyph unicode="v" horiz-adv-x="895" d="M276 0l-172 1087h101l108 -735q26 -165 33 -254h6q51 115 129 256l406 733h102l-600 -1087h-113z" />
<glyph unicode="w" horiz-adv-x="1393" d="M838 0l-60 686q-14 224 -14 266h-6q-34 -92 -144 -290l-356 -662h-117l-20 1087h92l13 -821l-3 -157h6q61 134 150 297l373 681h77l64 -681q14 -147 14 -297h6l24 61l101 236l320 681h96l-508 -1087h-108z" />
<glyph unicode="x" horiz-adv-x="922" d="M442 483l-393 -483h-121l471 559l-245 528h100l207 -462l373 462h120l-448 -534l258 -553h-98z" />
<glyph unicode="y" horiz-adv-x="920" d="M123 1087h100l82 -548q51 -351 55 -449h11q43 105 186 367l348 630h103l-713 -1290q-72 -127 -122.5 -178t-114 -81t-146.5 -30q-68 0 -129 21v92q71 -27 137 -27q80 0 147 49.5t130 164.5t100 184z" />
<glyph unicode="z" horiz-adv-x="887" d="M700 0h-729l15 72l776 932h-543l17 83h659l-18 -83l-762 -920h602z" />
<glyph unicode="{" horiz-adv-x="709" d="M424 -324q-256 0 -256 199q0 45 16 115l56 252q18 90 18 127q0 159 -199 159l21 78q126 0 191 49t89 158l89 393q30 135 106 195.5t215 60.5h29l-17 -86q-86 -2 -129 -20.5t-69.5 -61.5t-44.5 -120l-74 -338q-30 -134 -91.5 -194.5t-164.5 -78.5v-4q68 -18 105.5 -68.5 t37.5 -121.5q0 -52 -24 -164l-47 -225q-13 -58 -13 -101q0 -61 37.5 -89t138.5 -28v-86h-20z" />
<glyph unicode="|" d="M584 1561h100v-2071h-100v2071z" />
<glyph unicode="}" horiz-adv-x="709" d="M332 1462q131 0 189.5 -51t58.5 -147q0 -41 -17 -115l-55 -252q-19 -95 -19 -127q0 -77 49.5 -118.5t149.5 -41.5l-20 -78q-125 0 -191 -48.5t-90 -157.5l-88 -394q-32 -139 -108.5 -197.5t-213.5 -58.5h-18v86q96 2 138 21t68.5 61t43.5 121l74 338q27 126 87.5 189.5 t168.5 82.5v5q-75 20 -109.5 72.5t-34.5 117.5q0 55 18 131l54 258q12 61 12 101q0 44 -18 69t-54 36t-116 11l20 86h21z" />
<glyph unicode="~" d="M350 745q-49 0 -108 -30.5t-115 -89.5v94q108 110 233 110q61 0 115 -13.5t156 -57.5q126 -58 219 -58q54 0 107.5 29t117.5 96v-96q-111 -113 -233 -113q-117 0 -271 72q-62 29 -112.5 43t-108.5 14z" />
<glyph unicode="&#xa1;" horiz-adv-x="502" d="M264 711h62l-203 -1086h-119zM432 1030q0 -56 -25 -88.5t-69 -32.5q-66 0 -66 72q0 55 25 89t68 34q67 0 67 -74z" />
<glyph unicode="&#xa2;" d="M578 -20h-93l45 215q-132 25 -206 132.5t-74 272.5q0 184 63.5 341t178 253t256.5 111l36 178h90l-38 -176q116 -4 217 -43l-29 -90q-107 47 -217 47q-130 0 -233 -76t-162.5 -221t-59.5 -322q0 -164 74.5 -247t208.5 -83q127 0 264 60v-92q-118 -58 -281 -58z" />
<glyph unicode="&#xa3;" d="M879 1479q170 0 313 -78l-39 -84l-54 26q-108 50 -231 50q-134 0 -220.5 -74.5t-117.5 -220.5l-73 -340h409l-18 -82h-408l-57 -268q-50 -225 -188 -314h759l-20 -94h-938l16 84q93 11 165.5 95.5t107.5 236.5l57 260h-199l17 82h198l76 350q41 187 155 279t290 92z" />
<glyph unicode="&#xa4;" d="M262 723q0 118 74 225l-129 129l63 64l127 -129q105 78 230 78q118 0 223 -78l131 129l61 -62l-129 -129q78 -106 78 -227q0 -135 -78 -227l129 -127l-61 -62l-131 127q-104 -76 -223 -76q-126 0 -228 80l-129 -129l-61 62l127 127q-74 98 -74 225zM350 723 q0 -116 80 -196.5t197 -80.5t198.5 81t81.5 196q0 75 -36.5 140t-102.5 104t-141 39q-114 0 -195.5 -82t-81.5 -201z" />
<glyph unicode="&#xa5;" d="M584 645l544 817h117l-559 -823h266l-16 -76h-315l-39 -190h317l-18 -84h-316l-59 -289h-105l64 289h-299l18 84h299l41 190h-301l17 76h258l-215 823h100z" />
<glyph unicode="&#xa6;" d="M578 1561h100v-756h-100v756zM578 246h100v-756h-100v756z" />
<glyph unicode="&#xa7;" horiz-adv-x="995" d="M211 778q0 101 69.5 182t198.5 130q-64 31 -103.5 85.5t-39.5 120.5q0 74 46 134.5t132.5 94.5t202.5 34q163 0 289 -58l-31 -80q-138 54 -264 54q-124 0 -202.5 -46.5t-78.5 -123.5q0 -59 46 -104.5t183 -106.5q112 -52 158.5 -89.5t71 -85t24.5 -110.5 q0 -197 -249 -317q122 -64 122 -197q0 -86 -48 -153.5t-139.5 -105.5t-221.5 -38q-157 0 -275 53v99q47 -27 126 -46.5t153 -19.5q149 0 228 52.5t79 150.5q0 62 -42.5 106t-166.5 96q-155 65 -211.5 130t-56.5 159zM559 1038q-119 -30 -187.5 -97.5t-68.5 -154.5 q0 -57 24.5 -96.5t81 -73t187.5 -81.5q103 49 162 113.5t59 156.5q0 72 -57.5 126t-200.5 107z" />
<glyph unicode="&#xa8;" horiz-adv-x="1135" d="M836 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM492 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xa9;" horiz-adv-x="1704" d="M944 1092q-142 0 -222.5 -94.5t-80.5 -264.5q0 -186 74.5 -275t220.5 -89q85 0 199 43v-88q-104 -45 -209 -45q-187 0 -288 116t-101 330q0 207 110 332t297 125q119 0 227 -52l-36 -83q-99 45 -191 45zM147 731q0 200 100 375t275 276t377 101q200 0 375 -100t276 -275 t101 -377q0 -197 -97 -370t-272 -277t-383 -104q-207 0 -382 103.5t-272.5 276.5t-97.5 371zM240 731q0 -178 88.5 -329.5t240.5 -240.5t330 -89q174 0 325 85.5t243 239t92 334.5q0 178 -89 330t-240.5 241t-330.5 89q-182 0 -335 -92t-238.5 -243t-85.5 -325z" />
<glyph unicode="&#xaa;" horiz-adv-x="643" d="M487 1485q55 0 97.5 -29t66.5 -86h6l35 103h66l-137 -650h-72l22 125h-4q-96 -137 -223 -137q-80 0 -127 56.5t-47 164.5q0 189 90.5 321t226.5 132zM369 885q66 0 133.5 75.5t97.5 184.5q16 51 16 123q0 58 -36 100.5t-93 42.5q-94 0 -161.5 -111.5t-67.5 -271.5 q0 -143 111 -143z" />
<glyph unicode="&#xab;" horiz-adv-x="860" d="M426 932l57 -49l-317 -336l213 -385l-64 -39l-254 418l2 26zM786 932l58 -49l-314 -336l209 -385l-63 -39l-254 418l2 26z" />
<glyph unicode="&#xac;" d="M1028 772v-500h-100v400h-803v100h903z" />
<glyph unicode="&#xad;" horiz-adv-x="629" d="M77 502zM77 502l18 90h457l-16 -90h-459z" />
<glyph unicode="&#xae;" horiz-adv-x="1704" d="M758 731h112q93 0 144 46.5t51 135.5q0 172 -197 172h-110v-354zM1169 918q0 -80 -39.5 -141t-109.5 -93l237 -393h-120l-211 360h-168v-360h-101v880h211q143 0 222 -62t79 -191zM150 731q0 207 103.5 382t276.5 272.5t371 97.5q200 0 375 -100t276 -275t101 -377 q0 -197 -97 -370t-272 -277t-383 -104q-204 0 -376.5 100.5t-273.5 273t-101 377.5zM242 731q0 -178 88.5 -329.5t240.5 -240.5t330 -89q174 0 325 85.5t243 239t92 334.5q0 178 -89 330t-240.5 241t-330.5 89q-182 0 -335 -92t-238.5 -243t-85.5 -325z" />
<glyph unicode="&#xaf;" horiz-adv-x="655" d="M1001 1556h-653l53 97h654z" />
<glyph unicode="&#xb0;" horiz-adv-x="877" d="M242 1190q0 120 85 206.5t208 86.5q122 0 207 -86.5t85 -206.5q0 -122 -85.5 -207.5t-206.5 -85.5q-122 0 -207.5 85.5t-85.5 207.5zM315 1190q0 -89 64.5 -153t155.5 -64q92 0 155.5 64t63.5 153q0 90 -64 155.5t-155 65.5q-90 0 -155 -65.5t-65 -155.5z" />
<glyph unicode="&#xb1;" d="M528 629h-401v98h401v406h101v-406h401v-98h-401v-400h-101v400zM127 0v100h903v-100h-903z" />
<glyph unicode="&#xb2;" horiz-adv-x="643" d="M604 586h-522l16 80l297 258q137 118 182.5 190.5t45.5 153.5q0 59 -38.5 97t-105.5 38q-95 0 -194 -76l-41 62q108 90 239 90q73 0 125 -27t78.5 -72t26.5 -100q0 -106 -59 -198.5t-183 -194.5l-266 -223h416z" />
<glyph unicode="&#xb3;" horiz-adv-x="643" d="M705 1276q0 -85 -48.5 -148t-154.5 -88v-4q66 -16 105.5 -68t39.5 -124q0 -77 -39 -141t-109 -99t-161 -35q-59 0 -123.5 15.5t-105.5 40.5v90q46 -28 108 -48t125 -20q99 0 159 52.5t60 142.5q0 162 -196 162h-84l16 79h86q102 0 168.5 49.5t66.5 129.5 q0 68 -37.5 102.5t-105.5 34.5q-100 0 -199 -68l-40 64q109 86 251 86q100 0 159 -56.5t59 -148.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="1135" d="M580 1262q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xb5;" horiz-adv-x="1171" d="M238 242q0 -82 48.5 -127t135.5 -45q110 0 207 64.5t162.5 182.5t101.5 285l104 485h99l-234 -1087h-78l29 205h-6q-164 -221 -404 -221q-85 0 -139 32.5t-76 89.5h-6q-18 -132 -51 -284l-63 -314h-97l338 1579h101l-152 -698q-20 -96 -20 -147z" />
<glyph unicode="&#xb6;" horiz-adv-x="1341" d="M1208 -260h-100v1722h-227v-1722h-101v819q-64 -18 -145 -18q-216 0 -318 125t-102 376q0 260 109 387t342 127h542v-1816z" />
<glyph unicode="&#xb7;" horiz-adv-x="485" d="M207 625zM207 698q0 56 25 88.5t69 32.5q66 0 66 -72q0 -53 -25 -87.5t-67 -34.5q-68 0 -68 73z" />
<glyph unicode="&#xb8;" horiz-adv-x="420" d="M197 -289q0 -94 -75.5 -148.5t-217.5 -54.5q-46 0 -78 7v79q30 -6 72 -6q198 0 198 115q0 97 -151 107l110 190h80l-78 -137q140 -30 140 -152z" />
<glyph unicode="&#xb9;" horiz-adv-x="643" d="M512 1462h80l-186 -876h-84l118 569q5 21 11.5 50.5t14 60t15.5 59t15 49.5q-34 -31 -60 -51.5t-143 -93.5l-39 59z" />
<glyph unicode="&#xba;" horiz-adv-x="655" d="M518 1479q105 0 165 -64t60 -180q0 -115 -40 -214t-114 -156.5t-175 -57.5q-114 0 -169 67.5t-55 184.5q0 112 41.5 209.5t116 154t170.5 56.5zM508 1405q-70 0 -124 -46.5t-84 -124.5t-30 -167q0 -186 156 -186q73 0 125.5 46.5t81.5 127.5t29 176q0 83 -39 128.5 t-115 45.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="860" d="M451 123l-58 49l314 336l-209 385l63 39l254 -418l-2 -27zM90 123l-57 49l313 336l-209 385l64 39l254 -418l-2 -27z" />
<glyph unicode="&#xbc;" horiz-adv-x="1481" d="M129 0zM1319 230h-131l-49 -229h-82l49 229h-391l21 76l506 577h86l-125 -581h133zM1121 302q61 294 79 365.5t29 105.5q-10 -16 -61 -79t-338 -392h291zM1323 1462l-1087 -1462h-107l1086 1462h108zM509 1462h80l-186 -876h-84l118 569q5 21 11.5 50.5t14 60t15.5 59 t15 49.5q-34 -31 -60 -51.5t-143 -93.5l-39 59z" />
<glyph unicode="&#xbd;" horiz-adv-x="1458" d="M53 0zM1278 1h-522l16 80l297 258q137 118 182.5 190.5t45.5 153.5q0 59 -38.5 97t-105.5 38q-95 0 -194 -76l-41 62q108 90 239 90q73 0 125 -27t78.5 -72t26.5 -100q0 -106 -59 -198.5t-183 -194.5l-266 -223h416zM431 1462h80l-186 -876h-84l118 569q5 21 11.5 50.5 t14 60t15.5 59t15 49.5q-34 -31 -60 -51.5t-143 -93.5l-39 59zM1247 1462l-1087 -1462h-107l1086 1462h108z" />
<glyph unicode="&#xbe;" horiz-adv-x="1458" d="M71 0zM1380 230h-131l-49 -229h-82l49 229h-391l21 76l506 577h86l-125 -581h133zM1182 302q61 294 79 365.5t29 105.5q-10 -16 -61 -79t-338 -392h291zM667 1276q0 -85 -48.5 -148t-154.5 -88v-4q66 -16 105.5 -68t39.5 -124q0 -77 -39 -141t-109 -99t-161 -35 q-59 0 -123.5 15.5t-105.5 40.5v90q46 -28 108 -48t125 -20q99 0 159 52.5t60 142.5q0 162 -196 162h-84l16 79h86q102 0 168.5 49.5t66.5 129.5q0 68 -37.5 102.5t-105.5 34.5q-100 0 -199 -68l-40 64q109 86 251 86q100 0 159 -56.5t59 -148.5zM1407 1462l-1087 -1462 h-107l1086 1462h108z" />
<glyph unicode="&#xbf;" horiz-adv-x="799" d="M641 717l-6 -29q-28 -127 -79 -200t-161 -154q-118 -84 -175 -145.5t-86.5 -127.5t-29.5 -141q0 -106 65.5 -168.5t184.5 -62.5q141 0 308 100l38 -86q-85 -49 -170.5 -77.5t-187.5 -28.5q-159 0 -250.5 84.5t-91.5 228.5q0 133 70 240.5t227 220.5q85 61 133.5 109 t73 95t45.5 142h92zM713 1020q0 -56 -25.5 -88.5t-69.5 -32.5q-65 0 -65 72q0 56 25 89.5t67 33.5q68 0 68 -74z" />
<glyph unicode="&#xc0;" horiz-adv-x="1059" d="M0 0zM805 520h-512l-287 -520h-117l822 1468h67l201 -1468h-105zM793 612l-39 291q-31 242 -39 402q-30 -63 -64.5 -130t-306.5 -563h449zM815 1579h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="&#xc1;" horiz-adv-x="1059" d="M0 0zM805 520h-512l-287 -520h-117l822 1468h67l201 -1468h-105zM793 612l-39 291q-31 242 -39 402q-30 -63 -64.5 -130t-306.5 -563h449zM668 1600q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xc2;" horiz-adv-x="1059" d="M0 0zM805 520h-512l-287 -520h-117l822 1468h67l201 -1468h-105zM793 612l-39 291q-31 242 -39 402q-30 -63 -64.5 -130t-306.5 -563h449zM1007 1579h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5 t92.5 -146.5v-29z" />
<glyph unicode="&#xc3;" horiz-adv-x="1059" d="M0 0zM805 520h-512l-287 -520h-117l822 1468h67l201 -1468h-105zM793 612l-39 291q-31 242 -39 402q-30 -63 -64.5 -130t-306.5 -563h449zM911 1587q-40 0 -77.5 19t-75.5 45q-34 23 -64.5 41t-68.5 18q-45 0 -74 -28.5t-51 -100.5h-74q19 108 71 166.5t134 58.5 q41 0 73.5 -14t117.5 -72q52 -36 94 -36q43 0 71.5 30.5t46.5 100.5h76q-26 -118 -74.5 -173t-124.5 -55z" />
<glyph unicode="&#xc4;" horiz-adv-x="1059" d="M0 0zM805 520h-512l-287 -520h-117l822 1468h67l201 -1468h-105zM793 612l-39 291q-31 242 -39 402q-30 -63 -64.5 -130t-306.5 -563h449zM879 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM535 1704q0 49 20.5 78t56.5 29q54 0 54 -64 q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xc5;" horiz-adv-x="1059" d="M0 0zM805 520h-512l-287 -520h-117l822 1468h67l201 -1468h-105zM793 612l-39 291q-31 242 -39 402q-30 -63 -64.5 -130t-306.5 -563h449zM945 1600q0 -92 -57.5 -148.5t-145.5 -56.5q-93 0 -148 52t-55 145q0 88 59.5 144t149.5 56q88 0 142.5 -50t54.5 -142zM867 1598 q0 57 -33.5 90t-87.5 33q-60 0 -93.5 -36t-33.5 -93t33 -90t90 -33q56 0 90.5 36t34.5 93z" />
<glyph unicode="&#xc6;" horiz-adv-x="1640" d="M1403 0h-727l110 522h-444l-328 -522h-131l946 1462h883l-20 -94h-625l-117 -553h590l-20 -94h-588l-135 -627h626zM408 627h401l156 741h-88z" />
<glyph unicode="&#xc7;" horiz-adv-x="1169" d="M170 0zM961 1389q-197 0 -351.5 -104.5t-245 -304.5t-90.5 -441q0 -225 110.5 -346t317.5 -121q140 0 304 51v-94q-156 -49 -316 -49q-252 0 -386 145t-134 410q0 266 104.5 488t284.5 341t402 119q177 0 307 -68l-45 -90q-55 30 -124.5 47t-137.5 17zM752 -289 q0 -94 -75.5 -148.5t-217.5 -54.5q-46 0 -78 7v79q30 -6 72 -6q198 0 198 115q0 97 -151 107l110 190h80l-78 -137q140 -30 140 -152z" />
<glyph unicode="&#xc8;" horiz-adv-x="1067" d="M102 0zM829 0h-727l310 1462h727l-21 -94h-624l-117 -553h590l-21 -94h-588l-135 -627h627zM891 1579h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="&#xc9;" horiz-adv-x="1067" d="M102 0zM829 0h-727l310 1462h727l-21 -94h-624l-117 -553h590l-21 -94h-588l-135 -627h627zM654 1600q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xca;" horiz-adv-x="1067" d="M102 0zM829 0h-727l310 1462h727l-21 -94h-624l-117 -553h590l-21 -94h-588l-135 -627h627zM1036 1579h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#xcb;" horiz-adv-x="1067" d="M102 0zM829 0h-727l310 1462h727l-21 -94h-624l-117 -553h590l-21 -94h-588l-135 -627h627zM902 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM558 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xcc;" horiz-adv-x="504" d="M102 0zM102 0l310 1462h98l-309 -1462h-99zM525 1579h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="&#xcd;" horiz-adv-x="504" d="M102 0zM102 0l310 1462h98l-309 -1462h-99zM419 1600q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xce;" horiz-adv-x="504" d="M102 0zM102 0l310 1462h98l-309 -1462h-99zM738 1579h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#xcf;" horiz-adv-x="504" d="M102 0zM102 0l310 1462h98l-309 -1462h-99zM604 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM260 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xd0;" horiz-adv-x="1352" d="M1288 879q0 -253 -109.5 -461.5t-300.5 -313t-446 -104.5h-330l144 676h-156l21 96h155l146 690h305q282 0 426.5 -147.5t144.5 -435.5zM430 90q226 0 394.5 94.5t261 275.5t92.5 412q0 498 -476 498h-206l-129 -598h378l-20 -96h-379l-125 -586h209z" />
<glyph unicode="&#xd1;" horiz-adv-x="1372" d="M102 0zM1069 0h-86l-516 1284h-8q-23 -149 -48 -273t-214 -1011h-95l310 1462h80l522 -1294h8q23 176 74 416l188 878h94zM1062 1587q-40 0 -77.5 19t-75.5 45q-34 23 -64.5 41t-68.5 18q-45 0 -74 -28.5t-51 -100.5h-74q19 108 71 166.5t134 58.5q41 0 73.5 -14 t117.5 -72q52 -36 94 -36q43 0 71.5 30.5t46.5 100.5h76q-26 -118 -74.5 -173t-124.5 -55z" />
<glyph unicode="&#xd2;" horiz-adv-x="1464" d="M172 0zM1403 911q0 -255 -94 -481.5t-252 -338t-365 -111.5q-250 0 -385 149t-135 430q0 262 93 477.5t255 331t373 115.5q247 0 378.5 -148.5t131.5 -423.5zM879 1389q-174 0 -308.5 -101t-214.5 -298t-80 -417q0 -245 109.5 -373t319.5 -128q169 0 300 98.5t210 300 t79 430.5q0 240 -104.5 364t-310.5 124zM989 1579h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="&#xd3;" horiz-adv-x="1464" d="M172 0zM1403 911q0 -255 -94 -481.5t-252 -338t-365 -111.5q-250 0 -385 149t-135 430q0 262 93 477.5t255 331t373 115.5q247 0 378.5 -148.5t131.5 -423.5zM879 1389q-174 0 -308.5 -101t-214.5 -298t-80 -417q0 -245 109.5 -373t319.5 -128q169 0 300 98.5t210 300 t79 430.5q0 240 -104.5 364t-310.5 124zM844 1600q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xd4;" horiz-adv-x="1464" d="M172 0zM1403 911q0 -255 -94 -481.5t-252 -338t-365 -111.5q-250 0 -385 149t-135 430q0 262 93 477.5t255 331t373 115.5q247 0 378.5 -148.5t131.5 -423.5zM879 1389q-174 0 -308.5 -101t-214.5 -298t-80 -417q0 -245 109.5 -373t319.5 -128q169 0 300 98.5t210 300 t79 430.5q0 240 -104.5 364t-310.5 124zM1171 1579h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#xd5;" horiz-adv-x="1464" d="M172 0zM1403 911q0 -255 -94 -481.5t-252 -338t-365 -111.5q-250 0 -385 149t-135 430q0 262 93 477.5t255 331t373 115.5q247 0 378.5 -148.5t131.5 -423.5zM879 1389q-174 0 -308.5 -101t-214.5 -298t-80 -417q0 -245 109.5 -373t319.5 -128q169 0 300 98.5t210 300 t79 430.5q0 240 -104.5 364t-310.5 124zM1077 1587q-40 0 -77.5 19t-75.5 45q-34 23 -64.5 41t-68.5 18q-45 0 -74 -28.5t-51 -100.5h-74q19 108 71 166.5t134 58.5q41 0 73.5 -14t117.5 -72q52 -36 94 -36q43 0 71.5 30.5t46.5 100.5h76q-26 -118 -74.5 -173t-124.5 -55z " />
<glyph unicode="&#xd6;" horiz-adv-x="1464" d="M172 0zM1403 911q0 -255 -94 -481.5t-252 -338t-365 -111.5q-250 0 -385 149t-135 430q0 262 93 477.5t255 331t373 115.5q247 0 378.5 -148.5t131.5 -423.5zM879 1389q-174 0 -308.5 -101t-214.5 -298t-80 -417q0 -245 109.5 -373t319.5 -128q169 0 300 98.5t210 300 t79 430.5q0 240 -104.5 364t-310.5 124zM1031 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM687 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xd7;" d="M551 723l-330 332l70 69l330 -329l333 329l68 -67l-332 -334l332 -332l-68 -67l-333 329l-330 -327l-68 67z" />
<glyph unicode="&#xd8;" horiz-adv-x="1464" d="M1403 911q0 -187 -52 -365.5t-144.5 -304.5t-223 -193.5t-291.5 -67.5q-215 0 -348 112l-139 -170l-66 64l146 172q-113 149 -113 401q0 263 94 479.5t256.5 330.5t370.5 114q219 0 352 -121l133 168l70 -53l-145 -183q45 -51 72.5 -161t27.5 -222zM879 1389 q-134 0 -244 -59.5t-188.5 -170t-124.5 -267.5t-46 -319q0 -105 21.5 -191t56.5 -138l826 1032q-107 113 -301 113zM1294 901q0 85 -17.5 172t-43.5 129l-821 -1030q107 -100 293 -100q170 0 301 100t209.5 296.5t78.5 432.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1370" d="M176 0zM1395 1462l-207 -977q-58 -270 -197 -387.5t-375 -117.5q-440 0 -440 401q0 83 27 201l186 880h103l-193 -899q-20 -89 -20 -184q0 -309 342 -309q195 0 307.5 96.5t158.5 318.5l207 977h101zM946 1579h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303 v-25z" />
<glyph unicode="&#xda;" horiz-adv-x="1370" d="M176 0zM1395 1462l-207 -977q-58 -270 -197 -387.5t-375 -117.5q-440 0 -440 401q0 83 27 201l186 880h103l-193 -899q-20 -89 -20 -184q0 -309 342 -309q195 0 307.5 96.5t158.5 318.5l207 977h101zM838 1600q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5 t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xdb;" horiz-adv-x="1370" d="M176 0zM1395 1462l-207 -977q-58 -270 -197 -387.5t-375 -117.5q-440 0 -440 401q0 83 27 201l186 880h103l-193 -899q-20 -89 -20 -184q0 -309 342 -309q195 0 307.5 96.5t158.5 318.5l207 977h101zM1148 1579h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54 v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#xdc;" horiz-adv-x="1370" d="M176 0zM1395 1462l-207 -977q-58 -270 -197 -387.5t-375 -117.5q-440 0 -440 401q0 83 27 201l186 880h103l-193 -899q-20 -89 -20 -184q0 -309 342 -309q195 0 307.5 96.5t158.5 318.5l207 977h101zM1022 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29 q-55 0 -55 63zM678 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xdd;" horiz-adv-x="965" d="M193 0zM494 645l544 817h117l-631 -932l-108 -530h-105l119 545l-237 917h100zM563 1600q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xde;" horiz-adv-x="1145" d="M1087 836q0 -247 -153 -373.5t-457 -126.5h-201l-71 -336h-103l310 1462h102l-57 -266h213q200 0 308.5 -92.5t108.5 -267.5zM293 428h190q256 0 376 98.5t120 302.5q0 275 -330 275h-211z" />
<glyph unicode="&#xdf;" horiz-adv-x="1094" d="M-162 -492q-73 0 -119 23v90q53 -24 115 -24q79 0 123 50.5t66 153.5l305 1409q80 357 405 357q137 0 215 -61.5t78 -174.5q0 -75 -44.5 -140.5t-166.5 -148.5q-107 -76 -141.5 -124.5t-34.5 -106.5q0 -51 34 -88.5t93 -75.5q96 -63 138 -133.5t42 -165.5 q0 -170 -106.5 -269t-286.5 -99q-143 0 -234 65v109q45 -36 112.5 -59t129.5 -23q132 0 208.5 71t76.5 195q0 75 -31.5 129t-109.5 108q-82 58 -119 110.5t-37 121.5q0 57 21 103t60.5 88.5t137.5 113.5q101 70 131.5 116t30.5 101q0 70 -55 110t-150 40q-129 0 -205 -76 t-108 -229l-291 -1377q-33 -152 -103.5 -220.5t-179.5 -68.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1133" d="M102 0zM655 1104q232 0 279 -219h6l59 202h80l-229 -1087h-82l45 274h-6q-84 -142 -187 -218t-237 -76q-281 0 -281 374q0 197 75 376t200.5 276.5t277.5 97.5zM406 70q89 0 177.5 62t159 166t107.5 230t37 213q0 79 -26 141.5t-77 99t-127 36.5q-124 0 -224 -82 t-164 -245.5t-64 -325.5q0 -152 50 -223.5t151 -71.5zM809 1241h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="&#xe1;" horiz-adv-x="1133" d="M102 0zM655 1104q232 0 279 -219h6l59 202h80l-229 -1087h-82l45 274h-6q-84 -142 -187 -218t-237 -76q-281 0 -281 374q0 197 75 376t200.5 276.5t277.5 97.5zM406 70q89 0 177.5 62t159 166t107.5 230t37 213q0 79 -26 141.5t-77 99t-127 36.5q-124 0 -224 -82 t-164 -245.5t-64 -325.5q0 -152 50 -223.5t151 -71.5zM586 1262q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xe2;" horiz-adv-x="1133" d="M102 0zM655 1104q232 0 279 -219h6l59 202h80l-229 -1087h-82l45 274h-6q-84 -142 -187 -218t-237 -76q-281 0 -281 374q0 197 75 376t200.5 276.5t277.5 97.5zM406 70q89 0 177.5 62t159 166t107.5 230t37 213q0 79 -26 141.5t-77 99t-127 36.5q-124 0 -224 -82 t-164 -245.5t-64 -325.5q0 -152 50 -223.5t151 -71.5zM955 1243h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#xe3;" horiz-adv-x="1133" d="M102 0zM655 1104q232 0 279 -219h6l59 202h80l-229 -1087h-82l45 274h-6q-84 -142 -187 -218t-237 -76q-281 0 -281 374q0 197 75 376t200.5 276.5t277.5 97.5zM406 70q89 0 177.5 62t159 166t107.5 230t37 213q0 79 -26 141.5t-77 99t-127 36.5q-124 0 -224 -82 t-164 -245.5t-64 -325.5q0 -152 50 -223.5t151 -71.5zM858 1249q-40 0 -77.5 19t-75.5 45q-34 23 -64.5 41t-68.5 18q-45 0 -74 -28.5t-51 -100.5h-74q19 108 71 166.5t134 58.5q41 0 73.5 -14t117.5 -72q52 -36 94 -36q43 0 71.5 30.5t46.5 100.5h76q-26 -118 -74.5 -173 t-124.5 -55z" />
<glyph unicode="&#xe4;" horiz-adv-x="1133" d="M102 0zM655 1104q232 0 279 -219h6l59 202h80l-229 -1087h-82l45 274h-6q-84 -142 -187 -218t-237 -76q-281 0 -281 374q0 197 75 376t200.5 276.5t277.5 97.5zM406 70q89 0 177.5 62t159 166t107.5 230t37 213q0 79 -26 141.5t-77 99t-127 36.5q-124 0 -224 -82 t-164 -245.5t-64 -325.5q0 -152 50 -223.5t151 -71.5zM835 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM491 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xe5;" horiz-adv-x="1133" d="M102 0zM655 1104q232 0 279 -219h6l59 202h80l-229 -1087h-82l45 274h-6q-84 -142 -187 -218t-237 -76q-281 0 -281 374q0 197 75 376t200.5 276.5t277.5 97.5zM406 70q89 0 177.5 62t159 166t107.5 230t37 213q0 79 -26 141.5t-77 99t-127 36.5q-124 0 -224 -82 t-164 -245.5t-64 -325.5q0 -152 50 -223.5t151 -71.5zM927 1448q0 -92 -57.5 -148.5t-145.5 -56.5q-93 0 -148 52t-55 145q0 88 59.5 144t149.5 56q88 0 142.5 -50t54.5 -142zM849 1446q0 57 -33.5 90t-87.5 33q-60 0 -93.5 -36t-33.5 -93t33 -90t90 -33q56 0 90.5 36 t34.5 93z" />
<glyph unicode="&#xe6;" horiz-adv-x="1602" d="M1155 -20q-123 0 -211 60t-117 165l-39 -205h-77l41 254h-9q-94 -142 -189 -208t-208 -66q-120 0 -182 94t-62 270q0 206 70.5 384.5t192.5 277t274 98.5q106 0 166 -56.5t74 -156.5h10l59 192h66l-35 -186q139 207 350 207q112 0 175 -61.5t63 -172.5 q0 -179 -158.5 -271.5t-470.5 -92.5h-39q-8 -51 -8 -96q0 -161 69.5 -250.5t217.5 -89.5q69 0 133.5 21t130.5 52v-94q-80 -37 -147 -53t-140 -16zM369 70q67 0 138.5 42t134 117.5t106 170.5t63.5 199t20 165q0 118 -49 186t-141 68q-123 0 -223 -86t-156.5 -240 t-56.5 -340q0 -150 42.5 -216t121.5 -66zM1317 1018q-119 0 -232 -115.5t-172 -308.5h48q263 0 383 67t120 203q0 71 -38.5 112.5t-108.5 41.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="887" d="M102 0zM477 -20q-174 0 -274.5 110.5t-100.5 306.5q0 193 73.5 361.5t198.5 257t290 88.5q134 0 241 -43l-28 -90q-107 47 -218 47q-129 0 -232.5 -77t-162.5 -222t-59 -320q0 -158 73.5 -243.5t208.5 -85.5q71 0 131.5 13t131.5 46v-92q-116 -57 -273 -57zM574 -289 q0 -94 -75.5 -148.5t-217.5 -54.5q-46 0 -78 7v79q30 -6 72 -6q198 0 198 115q0 97 -151 107l110 190h80l-78 -137q140 -30 140 -152z" />
<glyph unicode="&#xe8;" horiz-adv-x="928" d="M102 0zM469 -20q-173 0 -270 109.5t-97 305.5q0 181 71 347t195.5 264t274.5 98q114 0 182 -61t68 -166q0 -181 -163.5 -276t-485.5 -95h-33q-6 -44 -6 -98q0 -165 74 -251.5t213 -86.5q132 0 276 73v-94q-140 -69 -299 -69zM631 1018q-131 0 -243.5 -115.5 t-162.5 -308.5h49q517 0 517 270q0 67 -43.5 110.5t-116.5 43.5zM751 1241h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="&#xe9;" horiz-adv-x="928" d="M102 0zM469 -20q-173 0 -270 109.5t-97 305.5q0 181 71 347t195.5 264t274.5 98q114 0 182 -61t68 -166q0 -181 -163.5 -276t-485.5 -95h-33q-6 -44 -6 -98q0 -165 74 -251.5t213 -86.5q132 0 276 73v-94q-140 -69 -299 -69zM631 1018q-131 0 -243.5 -115.5 t-162.5 -308.5h49q517 0 517 270q0 67 -43.5 110.5t-116.5 43.5zM532 1262q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xea;" horiz-adv-x="928" d="M102 0zM469 -20q-173 0 -270 109.5t-97 305.5q0 181 71 347t195.5 264t274.5 98q114 0 182 -61t68 -166q0 -181 -163.5 -276t-485.5 -95h-33q-6 -44 -6 -98q0 -165 74 -251.5t213 -86.5q132 0 276 73v-94q-140 -69 -299 -69zM631 1018q-131 0 -243.5 -115.5 t-162.5 -308.5h49q517 0 517 270q0 67 -43.5 110.5t-116.5 43.5zM904 1241h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#xeb;" horiz-adv-x="928" d="M102 0zM469 -20q-173 0 -270 109.5t-97 305.5q0 181 71 347t195.5 264t274.5 98q114 0 182 -61t68 -166q0 -181 -163.5 -276t-485.5 -95h-33q-6 -44 -6 -98q0 -165 74 -251.5t213 -86.5q132 0 276 73v-94q-140 -69 -299 -69zM631 1018q-131 0 -243.5 -115.5 t-162.5 -308.5h49q517 0 517 270q0 67 -43.5 110.5t-116.5 43.5zM780 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM436 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xec;" horiz-adv-x="475" d="M76 0zM174 0h-98l231 1087h96zM454 1241h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="&#xed;" horiz-adv-x="475" d="M76 0zM174 0h-98l231 1087h96zM284 1262q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xee;" horiz-adv-x="475" d="M76 0zM174 0h-98l231 1087h96zM642 1241h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#xef;" horiz-adv-x="475" d="M76 0zM174 0h-98l231 1087h96zM515 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM171 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xf0;" horiz-adv-x="1124" d="M713 1364q-54 60 -158 139l59 68q32 -26 81 -66t100 -94l266 150l39 -74l-256 -141q87 -116 131.5 -276t44.5 -335q0 -355 -141.5 -555t-399.5 -200q-177 0 -277 106.5t-100 294.5q0 170 63 301.5t178.5 203.5t262.5 72q107 0 188 -49.5t121 -142.5h5q0 139 -43 289 t-115 243l-295 -163l-39 73zM489 70q118 0 208.5 61t144 186.5t53.5 270.5q0 77 -35 142t-100 101.5t-156 36.5q-124 0 -213.5 -61.5t-137.5 -169.5t-48 -248q0 -153 73.5 -236t210.5 -83z" />
<glyph unicode="&#xf1;" horiz-adv-x="1143" d="M76 0zM764 0l149 692q21 92 21 156q0 80 -43.5 125t-134.5 45q-112 0 -210.5 -67t-166 -187.5t-103.5 -286.5l-102 -477h-98l231 1087h82l-37 -221h6q164 238 416 238q130 0 195 -64t65 -184q0 -70 -24 -182l-148 -674h-98zM874 1249q-40 0 -77.5 19t-75.5 45 q-34 23 -64.5 41t-68.5 18q-45 0 -74 -28.5t-51 -100.5h-74q19 108 71 166.5t134 58.5q41 0 73.5 -14t117.5 -72q52 -36 94 -36q43 0 71.5 30.5t46.5 100.5h76q-26 -118 -74.5 -173t-124.5 -55z" />
<glyph unicode="&#xf2;" horiz-adv-x="1124" d="M98 0zM649 1108q180 0 278.5 -108.5t98.5 -299.5q0 -197 -71.5 -368.5t-195.5 -261.5t-286 -90q-184 0 -279.5 109.5t-95.5 313.5q0 191 73 358t197 257t281 90zM641 1022q-124 0 -223 -78.5t-158 -225t-59 -310.5q0 -342 282 -342q127 0 225.5 77.5t157 228t58.5 330.5 q0 154 -73 237t-210 83zM744 1241h-66q-50 52 -114 144.5t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="&#xf3;" horiz-adv-x="1124" d="M98 0zM649 1108q180 0 278.5 -108.5t98.5 -299.5q0 -197 -71.5 -368.5t-195.5 -261.5t-286 -90q-184 0 -279.5 109.5t-95.5 313.5q0 191 73 358t197 257t281 90zM641 1022q-124 0 -223 -78.5t-158 -225t-59 -310.5q0 -342 282 -342q127 0 225.5 77.5t157 228t58.5 330.5 q0 154 -73 237t-210 83zM573 1262q66 51 150.5 142t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xf4;" horiz-adv-x="1124" d="M98 0zM649 1108q180 0 278.5 -108.5t98.5 -299.5q0 -197 -71.5 -368.5t-195.5 -261.5t-286 -90q-184 0 -279.5 109.5t-95.5 313.5q0 191 73 358t197 257t281 90zM641 1022q-124 0 -223 -78.5t-158 -225t-59 -310.5q0 -342 282 -342q127 0 225.5 77.5t157 228t58.5 330.5 q0 154 -73 237t-210 83zM941 1241h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#xf5;" horiz-adv-x="1124" d="M98 0zM649 1108q180 0 278.5 -108.5t98.5 -299.5q0 -197 -71.5 -368.5t-195.5 -261.5t-286 -90q-184 0 -279.5 109.5t-95.5 313.5q0 191 73 358t197 257t281 90zM641 1022q-124 0 -223 -78.5t-158 -225t-59 -310.5q0 -342 282 -342q127 0 225.5 77.5t157 228t58.5 330.5 q0 154 -73 237t-210 83zM839 1249q-40 0 -77.5 19t-75.5 45q-34 23 -64.5 41t-68.5 18q-45 0 -74 -28.5t-51 -100.5h-74q19 108 71 166.5t134 58.5q41 0 73.5 -14t117.5 -72q52 -36 94 -36q43 0 71.5 30.5t46.5 100.5h76q-26 -118 -74.5 -173t-124.5 -55z" />
<glyph unicode="&#xf6;" horiz-adv-x="1124" d="M98 0zM649 1108q180 0 278.5 -108.5t98.5 -299.5q0 -197 -71.5 -368.5t-195.5 -261.5t-286 -90q-184 0 -279.5 109.5t-95.5 313.5q0 191 73 358t197 257t281 90zM641 1022q-124 0 -223 -78.5t-158 -225t-59 -310.5q0 -342 282 -342q127 0 225.5 77.5t157 228t58.5 330.5 q0 154 -73 237t-210 83zM812 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM468 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xf7;" d="M168 672v100h903v-100h-903zM522 373q0 106 96 106q48 0 73.5 -27.5t25.5 -78.5q0 -57 -29 -82t-70 -25q-96 0 -96 107zM522 1071q0 107 96 107q46 0 72.5 -27.5t26.5 -79.5q0 -57 -29 -81.5t-70 -24.5q-96 0 -96 106z" />
<glyph unicode="&#xf8;" horiz-adv-x="1124" d="M164 127q-66 106 -66 276q0 191 73 358t197 257t281 90q150 0 250 -82l109 133l65 -53l-117 -143q70 -105 70 -263q0 -197 -71.5 -368.5t-195.5 -261.5t-286 -90q-163 0 -254 83l-110 -135l-64 54zM924 702q0 101 -35 179l-608 -742q67 -73 202 -73q127 0 225.5 77.5 t157 228t58.5 330.5zM641 1022q-124 0 -223 -78.5t-158 -225t-59 -310.5q0 -125 32 -197l605 739q-74 72 -197 72z" />
<glyph unicode="&#xf9;" horiz-adv-x="1143" d="M109 0zM381 1087l-152 -698q-22 -106 -22 -158q0 -74 47.5 -117.5t138.5 -43.5q110 0 207.5 65.5t164 187t99.5 279.5l105 485h98l-231 -1087h-80l28 205h-6q-167 -221 -403 -221q-131 0 -198.5 62t-67.5 181q0 60 22 170l150 690h100zM774 1241h-66q-50 52 -114 144.5 t-99 162.5v21h115q46 -129 164 -303v-25z" />
<glyph unicode="&#xfa;" horiz-adv-x="1143" d="M109 0zM381 1087l-152 -698q-22 -106 -22 -158q0 -74 47.5 -117.5t138.5 -43.5q110 0 207.5 65.5t164 187t99.5 279.5l105 485h98l-231 -1087h-80l28 205h-6q-167 -221 -403 -221q-131 0 -198.5 62t-67.5 181q0 60 22 170l150 690h100zM627 1262q66 51 150.5 142 t129.5 165h137v-23q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xfb;" horiz-adv-x="1143" d="M109 0zM381 1087l-152 -698q-22 -106 -22 -158q0 -74 47.5 -117.5t138.5 -43.5q110 0 207.5 65.5t164 187t99.5 279.5l105 485h98l-231 -1087h-80l28 205h-6q-167 -221 -403 -221q-131 0 -198.5 62t-67.5 181q0 60 22 170l150 690h100zM957 1241h-49q-70 60 -161 207 q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#xfc;" horiz-adv-x="1143" d="M109 0zM381 1087l-152 -698q-22 -106 -22 -158q0 -74 47.5 -117.5t138.5 -43.5q110 0 207.5 65.5t164 187t99.5 279.5l105 485h98l-231 -1087h-80l28 205h-6q-167 -221 -403 -221q-131 0 -198.5 62t-67.5 181q0 60 22 170l150 690h100zM827 1366q0 49 20.5 78t56.5 29 q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM483 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#xfd;" horiz-adv-x="920" d="M0 0zM123 1087h100l82 -548q51 -351 55 -449h11q43 105 186 367l348 630h103l-713 -1290q-72 -127 -122.5 -178t-114 -81t-146.5 -30q-68 0 -129 21v92q71 -27 137 -27q80 0 147 49.5t130 164.5t100 184zM505 1262q66 51 150.5 142t129.5 165h137v-23 q-51 -66 -157.5 -158.5t-192.5 -146.5h-67v21z" />
<glyph unicode="&#xfe;" horiz-adv-x="1163" d="M498 -16q-230 0 -279 219h-4q-13 -72 -149 -695h-99l434 2048h99q-114 -535 -164 -751h6q93 156 199 229.5t231 73.5q133 0 206 -92.5t73 -282.5q0 -195 -72 -371t-197.5 -277t-283.5 -101zM748 1018q-86 0 -172.5 -57.5t-162.5 -169.5t-111.5 -238t-35.5 -207 q0 -125 61.5 -200.5t168.5 -75.5q124 0 225 84t164 243.5t63 325.5q0 295 -200 295z" />
<glyph unicode="&#xff;" horiz-adv-x="920" d="M0 0zM123 1087h100l82 -548q51 -351 55 -449h11q43 105 186 367l348 630h103l-713 -1290q-72 -127 -122.5 -178t-114 -81t-146.5 -30q-68 0 -129 21v92q71 -27 137 -27q80 0 147 49.5t130 164.5t100 184zM698 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77 t-55 -29q-55 0 -55 63zM354 1366q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#x131;" horiz-adv-x="475" d="M174 0h-98l231 1087h96z" />
<glyph unicode="&#x152;" horiz-adv-x="1767" d="M1530 0h-666q-25 -6 -77.5 -13t-94.5 -7q-251 0 -385.5 149.5t-134.5 429.5q0 263 96 482t262 330.5t381 111.5q130 0 240 -21h688l-20 -94h-625l-117 -553h590l-20 -94h-588l-135 -627h626zM705 72q68 0 116 12l271 1290q-110 15 -189 15q-182 0 -321.5 -98.5 t-222.5 -293.5t-83 -424q0 -245 109 -373t320 -128z" />
<glyph unicode="&#x153;" horiz-adv-x="1720" d="M1262 -20q-135 0 -228 69t-125 201q-65 -127 -179 -198.5t-257 -71.5q-184 0 -279.5 109.5t-95.5 313.5q0 191 73 358t197 257t281 90q141 0 237 -74.5t126 -212.5q70 132 182.5 207.5t241.5 75.5q114 0 182 -61t68 -166q0 -181 -163.5 -276t-486.5 -95h-32 q-7 -38 -7 -98q0 -165 74 -251.5t213 -86.5q133 0 277 73v-94q-140 -69 -299 -69zM641 1022q-124 0 -223 -78.5t-158 -225t-59 -310.5q0 -342 282 -342q127 0 225.5 77.5t157 228t58.5 330.5q0 154 -73 237t-210 83zM1423 1018q-131 0 -243 -115t-162 -309h49q516 0 516 270 q0 70 -44.5 112t-115.5 42z" />
<glyph unicode="&#x178;" horiz-adv-x="965" d="M193 0zM494 645l544 817h117l-631 -932l-108 -530h-105l119 545l-237 917h100zM798 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63zM454 1704q0 49 20.5 78t56.5 29q54 0 54 -64q0 -48 -21 -77t-55 -29q-55 0 -55 63z" />
<glyph unicode="&#x2c6;" horiz-adv-x="1135" d="M958 1241h-49q-70 60 -161 207q-55 -57 -125 -114.5t-125 -92.5h-54v29q68 56 157.5 148.5t127.5 150.5h64q23 -64 72.5 -152.5t92.5 -146.5v-29z" />
<glyph unicode="&#x2da;" horiz-adv-x="1182" d="M967 1448q0 -92 -57.5 -148.5t-145.5 -56.5q-93 0 -148 52t-55 145q0 88 59.5 144t149.5 56q88 0 142.5 -50t54.5 -142zM889 1446q0 57 -33.5 90t-87.5 33q-60 0 -93.5 -36t-33.5 -93t33 -90t90 -33q56 0 90.5 36t34.5 93z" />
<glyph unicode="&#x2dc;" horiz-adv-x="1135" d="M831 1249q-40 0 -77.5 19t-75.5 45q-34 23 -64.5 41t-68.5 18q-45 0 -74 -28.5t-51 -100.5h-74q19 108 71 166.5t134 58.5q41 0 73.5 -14t117.5 -72q52 -36 94 -36q43 0 71.5 30.5t46.5 100.5h76q-26 -118 -74.5 -173t-124.5 -55z" />
<glyph unicode="&#x2000;" horiz-adv-x="953" />
<glyph unicode="&#x2001;" horiz-adv-x="1907" />
<glyph unicode="&#x2002;" horiz-adv-x="953" />
<glyph unicode="&#x2003;" horiz-adv-x="1907" />
<glyph unicode="&#x2004;" horiz-adv-x="635" />
<glyph unicode="&#x2005;" horiz-adv-x="476" />
<glyph unicode="&#x2006;" horiz-adv-x="317" />
<glyph unicode="&#x2007;" horiz-adv-x="317" />
<glyph unicode="&#x2008;" horiz-adv-x="238" />
<glyph unicode="&#x2009;" horiz-adv-x="381" />
<glyph unicode="&#x200a;" horiz-adv-x="105" />
<glyph unicode="&#x2010;" horiz-adv-x="629" d="M82 502l18 90h457l-16 -90h-459z" />
<glyph unicode="&#x2011;" horiz-adv-x="629" d="M82 502l18 90h457l-16 -90h-459z" />
<glyph unicode="&#x2012;" horiz-adv-x="629" d="M82 502l18 90h457l-16 -90h-459z" />
<glyph unicode="&#x2013;" horiz-adv-x="983" d="M66 502l18 90h807l-17 -90h-808z" />
<glyph unicode="&#x2014;" horiz-adv-x="1966" d="M68 502l18 90h1788l-16 -90h-1790z" />
<glyph unicode="&#x2018;" horiz-adv-x="299" d="M133 961l-4 22q41 100 116 231t161 248h73q-66 -106 -129.5 -242.5t-103.5 -258.5h-113z" />
<glyph unicode="&#x2019;" horiz-adv-x="299" d="M475 1462l4 -22q-43 -105 -117.5 -235.5t-158.5 -243.5h-74q66 106 129.5 242.5t103.5 258.5h113z" />
<glyph unicode="&#x201a;" horiz-adv-x="451" d="M246 238l4 -23q-40 -97 -115.5 -230t-161.5 -249h-73q68 110 131.5 248t101.5 254h113z" />
<glyph unicode="&#x201c;" horiz-adv-x="631" d="M133 961l-4 22q41 100 116 231t161 248h73q-66 -106 -129.5 -242.5t-103.5 -258.5h-113zM467 961l-4 22q43 104 120 238.5t156 240.5h74q-66 -106 -129.5 -242.5t-103.5 -258.5h-113z" />
<glyph unicode="&#x201d;" horiz-adv-x="631" d="M809 1462l4 -22q-43 -105 -117.5 -235.5t-158.5 -243.5h-74q66 106 129.5 242.5t103.5 258.5h113zM475 1462l4 -22q-43 -105 -117.5 -235.5t-158.5 -243.5h-74q66 106 129.5 242.5t103.5 258.5h113z" />
<glyph unicode="&#x201e;" horiz-adv-x="776" d="M561 238l4 -23q-43 -105 -117.5 -235.5t-158.5 -243.5h-74q66 108 129 242.5t105 259.5h112zM227 238l4 -23q-43 -105 -117.5 -235.5t-158.5 -243.5h-74q73 119 135.5 254.5t98.5 247.5h112z" />
<glyph unicode="&#x2022;" horiz-adv-x="793" d="M248 682q0 137 63 213t172 76q76 0 116 -39.5t40 -118.5q0 -125 -66 -207t-176 -82q-149 0 -149 158z" />
<glyph unicode="&#x2026;" horiz-adv-x="1489" d="M69 0zM69 55q0 56 25 88.5t69 32.5q66 0 66 -72q0 -53 -25 -87.5t-67 -34.5q-68 0 -68 73zM569 55q0 56 25 88.5t69 32.5q66 0 66 -72q0 -53 -25 -87.5t-67 -34.5q-68 0 -68 73zM1071 55q0 56 25 88.5t69 32.5q66 0 66 -72q0 -53 -25 -87.5t-67 -34.5q-68 0 -68 73z" />
<glyph unicode="&#x202f;" horiz-adv-x="381" />
<glyph unicode="&#x2039;" horiz-adv-x="537" d="M451 932l57 -49l-318 -336l213 -385l-63 -39l-254 418l2 26z" />
<glyph unicode="&#x203a;" horiz-adv-x="537" d="M94 123l-57 49l317 336l-213 385l64 39l254 -418l-2 -27z" />
<glyph unicode="&#x2044;" horiz-adv-x="274" d="M731 1462l-1087 -1462h-107l1086 1462h108z" />
<glyph unicode="&#x205f;" horiz-adv-x="476" />
<glyph unicode="&#x2074;" horiz-adv-x="643" d="M657 815h-131l-49 -229h-82l49 229h-391l21 76l506 577h86l-125 -581h133zM459 887q61 294 79 365.5t29 105.5q-10 -16 -61 -79t-338 -392h291z" />
<glyph unicode="&#x20ac;" d="M991 1389q-186 0 -330.5 -120.5t-226.5 -346.5h457l-21 -82h-460q-30 -98 -39 -217h442l-20 -82h-424q0 -243 89 -356t265 -113q115 0 252 57v-94q-129 -55 -270 -55q-209 0 -325 139.5t-116 394.5v27h-184l16 82h172q5 101 35 217h-170l19 82h174q95 273 270 417 t399 144q166 0 287 -90l-53 -82q-102 78 -238 78z" />
<glyph unicode="&#x2122;" horiz-adv-x="1534" d="M477 741h-86v643h-217v78h522v-78h-219v-643zM1077 741l-221 609h-6l4 -201v-408h-82v721h125l221 -606l223 606h125v-721h-86v398l4 207h-6l-227 -605h-74z" />
<glyph unicode="&#xe000;" horiz-adv-x="1085" d="M0 1085h1085v-1085h-1085v1085z" />
<glyph horiz-adv-x="1133" d="M0 0z" />
<hkern u1="&#x22;" u2="&#x178;" k="-20" />
<hkern u1="&#x22;" u2="&#x153;" k="123" />
<hkern u1="&#x22;" u2="&#xfc;" k="61" />
<hkern u1="&#x22;" u2="&#xfb;" k="61" />
<hkern u1="&#x22;" u2="&#xfa;" k="61" />
<hkern u1="&#x22;" u2="&#xf9;" k="61" />
<hkern u1="&#x22;" u2="&#xf8;" k="123" />
<hkern u1="&#x22;" u2="&#xf6;" k="123" />
<hkern u1="&#x22;" u2="&#xf5;" k="123" />
<hkern u1="&#x22;" u2="&#xf4;" k="123" />
<hkern u1="&#x22;" u2="&#xf3;" k="123" />
<hkern u1="&#x22;" u2="&#xf2;" k="123" />
<hkern u1="&#x22;" u2="&#xeb;" k="123" />
<hkern u1="&#x22;" u2="&#xea;" k="123" />
<hkern u1="&#x22;" u2="&#xe9;" k="123" />
<hkern u1="&#x22;" u2="&#xe8;" k="123" />
<hkern u1="&#x22;" u2="&#xe7;" k="123" />
<hkern u1="&#x22;" u2="&#xe6;" k="82" />
<hkern u1="&#x22;" u2="&#xe5;" k="82" />
<hkern u1="&#x22;" u2="&#xe4;" k="82" />
<hkern u1="&#x22;" u2="&#xe3;" k="82" />
<hkern u1="&#x22;" u2="&#xe2;" k="82" />
<hkern u1="&#x22;" u2="&#xe1;" k="82" />
<hkern u1="&#x22;" u2="&#xe0;" k="123" />
<hkern u1="&#x22;" u2="&#xdd;" k="-20" />
<hkern u1="&#x22;" u2="&#xc5;" k="143" />
<hkern u1="&#x22;" u2="&#xc4;" k="143" />
<hkern u1="&#x22;" u2="&#xc3;" k="143" />
<hkern u1="&#x22;" u2="&#xc2;" k="143" />
<hkern u1="&#x22;" u2="&#xc1;" k="143" />
<hkern u1="&#x22;" u2="&#xc0;" k="143" />
<hkern u1="&#x22;" u2="u" k="61" />
<hkern u1="&#x22;" u2="s" k="61" />
<hkern u1="&#x22;" u2="r" k="61" />
<hkern u1="&#x22;" u2="q" k="123" />
<hkern u1="&#x22;" u2="p" k="61" />
<hkern u1="&#x22;" u2="o" k="123" />
<hkern u1="&#x22;" u2="n" k="61" />
<hkern u1="&#x22;" u2="m" k="61" />
<hkern u1="&#x22;" u2="g" k="61" />
<hkern u1="&#x22;" u2="e" k="123" />
<hkern u1="&#x22;" u2="d" k="123" />
<hkern u1="&#x22;" u2="c" k="123" />
<hkern u1="&#x22;" u2="a" k="82" />
<hkern u1="&#x22;" u2="Y" k="-20" />
<hkern u1="&#x22;" u2="W" k="-41" />
<hkern u1="&#x22;" u2="V" k="-41" />
<hkern u1="&#x22;" u2="T" k="-41" />
<hkern u1="&#x22;" u2="A" k="143" />
<hkern u1="&#x27;" u2="&#x178;" k="-20" />
<hkern u1="&#x27;" u2="&#x153;" k="123" />
<hkern u1="&#x27;" u2="&#xfc;" k="61" />
<hkern u1="&#x27;" u2="&#xfb;" k="61" />
<hkern u1="&#x27;" u2="&#xfa;" k="61" />
<hkern u1="&#x27;" u2="&#xf9;" k="61" />
<hkern u1="&#x27;" u2="&#xf8;" k="123" />
<hkern u1="&#x27;" u2="&#xf6;" k="123" />
<hkern u1="&#x27;" u2="&#xf5;" k="123" />
<hkern u1="&#x27;" u2="&#xf4;" k="123" />
<hkern u1="&#x27;" u2="&#xf3;" k="123" />
<hkern u1="&#x27;" u2="&#xf2;" k="123" />
<hkern u1="&#x27;" u2="&#xeb;" k="123" />
<hkern u1="&#x27;" u2="&#xea;" k="123" />
<hkern u1="&#x27;" u2="&#xe9;" k="123" />
<hkern u1="&#x27;" u2="&#xe8;" k="123" />
<hkern u1="&#x27;" u2="&#xe7;" k="123" />
<hkern u1="&#x27;" u2="&#xe6;" k="82" />
<hkern u1="&#x27;" u2="&#xe5;" k="82" />
<hkern u1="&#x27;" u2="&#xe4;" k="82" />
<hkern u1="&#x27;" u2="&#xe3;" k="82" />
<hkern u1="&#x27;" u2="&#xe2;" k="82" />
<hkern u1="&#x27;" u2="&#xe1;" k="82" />
<hkern u1="&#x27;" u2="&#xe0;" k="123" />
<hkern u1="&#x27;" u2="&#xdd;" k="-20" />
<hkern u1="&#x27;" u2="&#xc5;" k="143" />
<hkern u1="&#x27;" u2="&#xc4;" k="143" />
<hkern u1="&#x27;" u2="&#xc3;" k="143" />
<hkern u1="&#x27;" u2="&#xc2;" k="143" />
<hkern u1="&#x27;" u2="&#xc1;" k="143" />
<hkern u1="&#x27;" u2="&#xc0;" k="143" />
<hkern u1="&#x27;" u2="u" k="61" />
<hkern u1="&#x27;" u2="s" k="61" />
<hkern u1="&#x27;" u2="r" k="61" />
<hkern u1="&#x27;" u2="q" k="123" />
<hkern u1="&#x27;" u2="p" k="61" />
<hkern u1="&#x27;" u2="o" k="123" />
<hkern u1="&#x27;" u2="n" k="61" />
<hkern u1="&#x27;" u2="m" k="61" />
<hkern u1="&#x27;" u2="g" k="61" />
<hkern u1="&#x27;" u2="e" k="123" />
<hkern u1="&#x27;" u2="d" k="123" />
<hkern u1="&#x27;" u2="c" k="123" />
<hkern u1="&#x27;" u2="a" k="82" />
<hkern u1="&#x27;" u2="Y" k="-20" />
<hkern u1="&#x27;" u2="W" k="-41" />
<hkern u1="&#x27;" u2="V" k="-41" />
<hkern u1="&#x27;" u2="T" k="-41" />
<hkern u1="&#x27;" u2="A" k="143" />
<hkern u1="&#x28;" u2="J" k="-184" />
<hkern u1="&#x2c;" u2="&#x178;" k="123" />
<hkern u1="&#x2c;" u2="&#x152;" k="102" />
<hkern u1="&#x2c;" u2="&#xdd;" k="123" />
<hkern u1="&#x2c;" u2="&#xdc;" k="41" />
<hkern u1="&#x2c;" u2="&#xdb;" k="41" />
<hkern u1="&#x2c;" u2="&#xda;" k="41" />
<hkern u1="&#x2c;" u2="&#xd9;" k="41" />
<hkern u1="&#x2c;" u2="&#xd8;" k="102" />
<hkern u1="&#x2c;" u2="&#xd6;" k="102" />
<hkern u1="&#x2c;" u2="&#xd5;" k="102" />
<hkern u1="&#x2c;" u2="&#xd4;" k="102" />
<hkern u1="&#x2c;" u2="&#xd3;" k="102" />
<hkern u1="&#x2c;" u2="&#xd2;" k="102" />
<hkern u1="&#x2c;" u2="&#xc7;" k="102" />
<hkern u1="&#x2c;" u2="Y" k="123" />
<hkern u1="&#x2c;" u2="W" k="123" />
<hkern u1="&#x2c;" u2="V" k="123" />
<hkern u1="&#x2c;" u2="U" k="41" />
<hkern u1="&#x2c;" u2="T" k="143" />
<hkern u1="&#x2c;" u2="Q" k="102" />
<hkern u1="&#x2c;" u2="O" k="102" />
<hkern u1="&#x2c;" u2="G" k="102" />
<hkern u1="&#x2c;" u2="C" k="102" />
<hkern u1="&#x2d;" u2="T" k="82" />
<hkern u1="&#x2e;" u2="&#x178;" k="123" />
<hkern u1="&#x2e;" u2="&#x152;" k="102" />
<hkern u1="&#x2e;" u2="&#xdd;" k="123" />
<hkern u1="&#x2e;" u2="&#xdc;" k="41" />
<hkern u1="&#x2e;" u2="&#xdb;" k="41" />
<hkern u1="&#x2e;" u2="&#xda;" k="41" />
<hkern u1="&#x2e;" u2="&#xd9;" k="41" />
<hkern u1="&#x2e;" u2="&#xd8;" k="102" />
<hkern u1="&#x2e;" u2="&#xd6;" k="102" />
<hkern u1="&#x2e;" u2="&#xd5;" k="102" />
<hkern u1="&#x2e;" u2="&#xd4;" k="102" />
<hkern u1="&#x2e;" u2="&#xd3;" k="102" />
<hkern u1="&#x2e;" u2="&#xd2;" k="102" />
<hkern u1="&#x2e;" u2="&#xc7;" k="102" />
<hkern u1="&#x2e;" u2="Y" k="123" />
<hkern u1="&#x2e;" u2="W" k="123" />
<hkern u1="&#x2e;" u2="V" k="123" />
<hkern u1="&#x2e;" u2="U" k="41" />
<hkern u1="&#x2e;" u2="T" k="143" />
<hkern u1="&#x2e;" u2="Q" k="102" />
<hkern u1="&#x2e;" u2="O" k="102" />
<hkern u1="&#x2e;" u2="G" k="102" />
<hkern u1="&#x2e;" u2="C" k="102" />
<hkern u1="A" u2="&#x201d;" k="143" />
<hkern u1="A" u2="&#x2019;" k="143" />
<hkern u1="A" u2="&#x178;" k="123" />
<hkern u1="A" u2="&#x152;" k="41" />
<hkern u1="A" u2="&#xdd;" k="123" />
<hkern u1="A" u2="&#xd8;" k="41" />
<hkern u1="A" u2="&#xd6;" k="41" />
<hkern u1="A" u2="&#xd5;" k="41" />
<hkern u1="A" u2="&#xd4;" k="41" />
<hkern u1="A" u2="&#xd3;" k="41" />
<hkern u1="A" u2="&#xd2;" k="41" />
<hkern u1="A" u2="&#xc7;" k="41" />
<hkern u1="A" u2="Y" k="123" />
<hkern u1="A" u2="W" k="82" />
<hkern u1="A" u2="V" k="82" />
<hkern u1="A" u2="T" k="143" />
<hkern u1="A" u2="Q" k="41" />
<hkern u1="A" u2="O" k="41" />
<hkern u1="A" u2="J" k="-266" />
<hkern u1="A" u2="G" k="41" />
<hkern u1="A" u2="C" k="41" />
<hkern u1="A" u2="&#x27;" k="143" />
<hkern u1="A" u2="&#x22;" k="143" />
<hkern u1="B" u2="&#x201e;" k="82" />
<hkern u1="B" u2="&#x201a;" k="82" />
<hkern u1="B" u2="&#x178;" k="20" />
<hkern u1="B" u2="&#xdd;" k="20" />
<hkern u1="B" u2="&#xc5;" k="41" />
<hkern u1="B" u2="&#xc4;" k="41" />
<hkern u1="B" u2="&#xc3;" k="41" />
<hkern u1="B" u2="&#xc2;" k="41" />
<hkern u1="B" u2="&#xc1;" k="41" />
<hkern u1="B" u2="&#xc0;" k="41" />
<hkern u1="B" u2="Z" k="20" />
<hkern u1="B" u2="Y" k="20" />
<hkern u1="B" u2="X" k="41" />
<hkern u1="B" u2="W" k="20" />
<hkern u1="B" u2="V" k="20" />
<hkern u1="B" u2="T" k="61" />
<hkern u1="B" u2="A" k="41" />
<hkern u1="B" u2="&#x2e;" k="82" />
<hkern u1="B" u2="&#x2c;" k="82" />
<hkern u1="C" u2="&#x152;" k="41" />
<hkern u1="C" u2="&#xd8;" k="41" />
<hkern u1="C" u2="&#xd6;" k="41" />
<hkern u1="C" u2="&#xd5;" k="41" />
<hkern u1="C" u2="&#xd4;" k="41" />
<hkern u1="C" u2="&#xd3;" k="41" />
<hkern u1="C" u2="&#xd2;" k="41" />
<hkern u1="C" u2="&#xc7;" k="41" />
<hkern u1="C" u2="Q" k="41" />
<hkern u1="C" u2="O" k="41" />
<hkern u1="C" u2="G" k="41" />
<hkern u1="C" u2="C" k="41" />
<hkern u1="D" u2="&#x201e;" k="82" />
<hkern u1="D" u2="&#x201a;" k="82" />
<hkern u1="D" u2="&#x178;" k="20" />
<hkern u1="D" u2="&#xdd;" k="20" />
<hkern u1="D" u2="&#xc5;" k="41" />
<hkern u1="D" u2="&#xc4;" k="41" />
<hkern u1="D" u2="&#xc3;" k="41" />
<hkern u1="D" u2="&#xc2;" k="41" />
<hkern u1="D" u2="&#xc1;" k="41" />
<hkern u1="D" u2="&#xc0;" k="41" />
<hkern u1="D" u2="Z" k="20" />
<hkern u1="D" u2="Y" k="20" />
<hkern u1="D" u2="X" k="41" />
<hkern u1="D" u2="W" k="20" />
<hkern u1="D" u2="V" k="20" />
<hkern u1="D" u2="T" k="61" />
<hkern u1="D" u2="A" k="41" />
<hkern u1="D" u2="&#x2e;" k="82" />
<hkern u1="D" u2="&#x2c;" k="82" />
<hkern u1="E" u2="J" k="-123" />
<hkern u1="F" u2="&#x201e;" k="123" />
<hkern u1="F" u2="&#x201a;" k="123" />
<hkern u1="F" u2="&#xc5;" k="41" />
<hkern u1="F" u2="&#xc4;" k="41" />
<hkern u1="F" u2="&#xc3;" k="41" />
<hkern u1="F" u2="&#xc2;" k="41" />
<hkern u1="F" u2="&#xc1;" k="41" />
<hkern u1="F" u2="&#xc0;" k="41" />
<hkern u1="F" u2="A" k="41" />
<hkern u1="F" u2="&#x3f;" k="-41" />
<hkern u1="F" u2="&#x2e;" k="123" />
<hkern u1="F" u2="&#x2c;" k="123" />
<hkern u1="K" u2="&#x152;" k="41" />
<hkern u1="K" u2="&#xd8;" k="41" />
<hkern u1="K" u2="&#xd6;" k="41" />
<hkern u1="K" u2="&#xd5;" k="41" />
<hkern u1="K" u2="&#xd4;" k="41" />
<hkern u1="K" u2="&#xd3;" k="41" />
<hkern u1="K" u2="&#xd2;" k="41" />
<hkern u1="K" u2="&#xc7;" k="41" />
<hkern u1="K" u2="Q" k="41" />
<hkern u1="K" u2="O" k="41" />
<hkern u1="K" u2="G" k="41" />
<hkern u1="K" u2="C" k="41" />
<hkern u1="L" u2="&#x201d;" k="164" />
<hkern u1="L" u2="&#x2019;" k="164" />
<hkern u1="L" u2="&#x178;" k="61" />
<hkern u1="L" u2="&#x152;" k="41" />
<hkern u1="L" u2="&#xdd;" k="61" />
<hkern u1="L" u2="&#xdc;" k="20" />
<hkern u1="L" u2="&#xdb;" k="20" />
<hkern u1="L" u2="&#xda;" k="20" />
<hkern u1="L" u2="&#xd9;" k="20" />
<hkern u1="L" u2="&#xd8;" k="41" />
<hkern u1="L" u2="&#xd6;" k="41" />
<hkern u1="L" u2="&#xd5;" k="41" />
<hkern u1="L" u2="&#xd4;" k="41" />
<hkern u1="L" u2="&#xd3;" k="41" />
<hkern u1="L" u2="&#xd2;" k="41" />
<hkern u1="L" u2="&#xc7;" k="41" />
<hkern u1="L" u2="Y" k="61" />
<hkern u1="L" u2="W" k="41" />
<hkern u1="L" u2="V" k="41" />
<hkern u1="L" u2="U" k="20" />
<hkern u1="L" u2="T" k="41" />
<hkern u1="L" u2="Q" k="41" />
<hkern u1="L" u2="O" k="41" />
<hkern u1="L" u2="G" k="41" />
<hkern u1="L" u2="C" k="41" />
<hkern u1="L" u2="&#x27;" k="164" />
<hkern u1="L" u2="&#x22;" k="164" />
<hkern u1="O" u2="&#x201e;" k="82" />
<hkern u1="O" u2="&#x201a;" k="82" />
<hkern u1="O" u2="&#x178;" k="20" />
<hkern u1="O" u2="&#xdd;" k="20" />
<hkern u1="O" u2="&#xc5;" k="41" />
<hkern u1="O" u2="&#xc4;" k="41" />
<hkern u1="O" u2="&#xc3;" k="41" />
<hkern u1="O" u2="&#xc2;" k="41" />
<hkern u1="O" u2="&#xc1;" k="41" />
<hkern u1="O" u2="&#xc0;" k="41" />
<hkern u1="O" u2="Z" k="20" />
<hkern u1="O" u2="Y" k="20" />
<hkern u1="O" u2="X" k="41" />
<hkern u1="O" u2="W" k="20" />
<hkern u1="O" u2="V" k="20" />
<hkern u1="O" u2="T" k="61" />
<hkern u1="O" u2="A" k="41" />
<hkern u1="O" u2="&#x2e;" k="82" />
<hkern u1="O" u2="&#x2c;" k="82" />
<hkern u1="P" u2="&#x201e;" k="266" />
<hkern u1="P" u2="&#x201a;" k="266" />
<hkern u1="P" u2="&#xc5;" k="102" />
<hkern u1="P" u2="&#xc4;" k="102" />
<hkern u1="P" u2="&#xc3;" k="102" />
<hkern u1="P" u2="&#xc2;" k="102" />
<hkern u1="P" u2="&#xc1;" k="102" />
<hkern u1="P" u2="&#xc0;" k="102" />
<hkern u1="P" u2="Z" k="20" />
<hkern u1="P" u2="X" k="41" />
<hkern u1="P" u2="A" k="102" />
<hkern u1="P" u2="&#x2e;" k="266" />
<hkern u1="P" u2="&#x2c;" k="266" />
<hkern u1="Q" u2="&#x201e;" k="82" />
<hkern u1="Q" u2="&#x201a;" k="82" />
<hkern u1="Q" u2="&#x178;" k="20" />
<hkern u1="Q" u2="&#xdd;" k="20" />
<hkern u1="Q" u2="&#xc5;" k="41" />
<hkern u1="Q" u2="&#xc4;" k="41" />
<hkern u1="Q" u2="&#xc3;" k="41" />
<hkern u1="Q" u2="&#xc2;" k="41" />
<hkern u1="Q" u2="&#xc1;" k="41" />
<hkern u1="Q" u2="&#xc0;" k="41" />
<hkern u1="Q" u2="Z" k="20" />
<hkern u1="Q" u2="Y" k="20" />
<hkern u1="Q" u2="X" k="41" />
<hkern u1="Q" u2="W" k="20" />
<hkern u1="Q" u2="V" k="20" />
<hkern u1="Q" u2="T" k="61" />
<hkern u1="Q" u2="A" k="41" />
<hkern u1="Q" u2="&#x2e;" k="82" />
<hkern u1="Q" u2="&#x2c;" k="82" />
<hkern u1="T" u2="&#x201e;" k="123" />
<hkern u1="T" u2="&#x201a;" k="123" />
<hkern u1="T" u2="&#x2014;" k="82" />
<hkern u1="T" u2="&#x2013;" k="82" />
<hkern u1="T" u2="&#x153;" k="143" />
<hkern u1="T" u2="&#x152;" k="41" />
<hkern u1="T" u2="&#xfd;" k="41" />
<hkern u1="T" u2="&#xfc;" k="102" />
<hkern u1="T" u2="&#xfb;" k="102" />
<hkern u1="T" u2="&#xfa;" k="102" />
<hkern u1="T" u2="&#xf9;" k="102" />
<hkern u1="T" u2="&#xf8;" k="143" />
<hkern u1="T" u2="&#xf6;" k="143" />
<hkern u1="T" u2="&#xf5;" k="143" />
<hkern u1="T" u2="&#xf4;" k="143" />
<hkern u1="T" u2="&#xf3;" k="143" />
<hkern u1="T" u2="&#xf2;" k="143" />
<hkern u1="T" u2="&#xeb;" k="143" />
<hkern u1="T" u2="&#xea;" k="143" />
<hkern u1="T" u2="&#xe9;" k="143" />
<hkern u1="T" u2="&#xe8;" k="143" />
<hkern u1="T" u2="&#xe7;" k="143" />
<hkern u1="T" u2="&#xe6;" k="164" />
<hkern u1="T" u2="&#xe5;" k="164" />
<hkern u1="T" u2="&#xe4;" k="164" />
<hkern u1="T" u2="&#xe3;" k="164" />
<hkern u1="T" u2="&#xe2;" k="164" />
<hkern u1="T" u2="&#xe1;" k="164" />
<hkern u1="T" u2="&#xe0;" k="143" />
<hkern u1="T" u2="&#xd8;" k="41" />
<hkern u1="T" u2="&#xd6;" k="41" />
<hkern u1="T" u2="&#xd5;" k="41" />
<hkern u1="T" u2="&#xd4;" k="41" />
<hkern u1="T" u2="&#xd3;" k="41" />
<hkern u1="T" u2="&#xd2;" k="41" />
<hkern u1="T" u2="&#xc7;" k="41" />
<hkern u1="T" u2="&#xc5;" k="143" />
<hkern u1="T" u2="&#xc4;" k="143" />
<hkern u1="T" u2="&#xc3;" k="143" />
<hkern u1="T" u2="&#xc2;" k="143" />
<hkern u1="T" u2="&#xc1;" k="143" />
<hkern u1="T" u2="&#xc0;" k="143" />
<hkern u1="T" u2="z" k="82" />
<hkern u1="T" u2="y" k="41" />
<hkern u1="T" u2="x" k="41" />
<hkern u1="T" u2="w" k="41" />
<hkern u1="T" u2="v" k="41" />
<hkern u1="T" u2="u" k="102" />
<hkern u1="T" u2="s" k="123" />
<hkern u1="T" u2="r" k="102" />
<hkern u1="T" u2="q" k="143" />
<hkern u1="T" u2="p" k="102" />
<hkern u1="T" u2="o" k="143" />
<hkern u1="T" u2="n" k="102" />
<hkern u1="T" u2="m" k="102" />
<hkern u1="T" u2="g" k="143" />
<hkern u1="T" u2="e" k="143" />
<hkern u1="T" u2="d" k="143" />
<hkern u1="T" u2="c" k="143" />
<hkern u1="T" u2="a" k="164" />
<hkern u1="T" u2="T" k="-41" />
<hkern u1="T" u2="Q" k="41" />
<hkern u1="T" u2="O" k="41" />
<hkern u1="T" u2="G" k="41" />
<hkern u1="T" u2="C" k="41" />
<hkern u1="T" u2="A" k="143" />
<hkern u1="T" u2="&#x3f;" k="-41" />
<hkern u1="T" u2="&#x2e;" k="123" />
<hkern u1="T" u2="&#x2d;" k="82" />
<hkern u1="T" u2="&#x2c;" k="123" />
<hkern u1="U" u2="&#x201e;" k="41" />
<hkern u1="U" u2="&#x201a;" k="41" />
<hkern u1="U" u2="&#xc5;" k="20" />
<hkern u1="U" u2="&#xc4;" k="20" />
<hkern u1="U" u2="&#xc3;" k="20" />
<hkern u1="U" u2="&#xc2;" k="20" />
<hkern u1="U" u2="&#xc1;" k="20" />
<hkern u1="U" u2="&#xc0;" k="20" />
<hkern u1="U" u2="A" k="20" />
<hkern u1="U" u2="&#x2e;" k="41" />
<hkern u1="U" u2="&#x2c;" k="41" />
<hkern u1="V" u2="&#x201e;" k="102" />
<hkern u1="V" u2="&#x201a;" k="102" />
<hkern u1="V" u2="&#x153;" k="41" />
<hkern u1="V" u2="&#x152;" k="20" />
<hkern u1="V" u2="&#xfc;" k="20" />
<hkern u1="V" u2="&#xfb;" k="20" />
<hkern u1="V" u2="&#xfa;" k="20" />
<hkern u1="V" u2="&#xf9;" k="20" />
<hkern u1="V" u2="&#xf8;" k="41" />
<hkern u1="V" u2="&#xf6;" k="41" />
<hkern u1="V" u2="&#xf5;" k="41" />
<hkern u1="V" u2="&#xf4;" k="41" />
<hkern u1="V" u2="&#xf3;" k="41" />
<hkern u1="V" u2="&#xf2;" k="41" />
<hkern u1="V" u2="&#xeb;" k="41" />
<hkern u1="V" u2="&#xea;" k="41" />
<hkern u1="V" u2="&#xe9;" k="41" />
<hkern u1="V" u2="&#xe8;" k="41" />
<hkern u1="V" u2="&#xe7;" k="41" />
<hkern u1="V" u2="&#xe6;" k="41" />
<hkern u1="V" u2="&#xe5;" k="41" />
<hkern u1="V" u2="&#xe4;" k="41" />
<hkern u1="V" u2="&#xe3;" k="41" />
<hkern u1="V" u2="&#xe2;" k="41" />
<hkern u1="V" u2="&#xe1;" k="41" />
<hkern u1="V" u2="&#xe0;" k="41" />
<hkern u1="V" u2="&#xd8;" k="20" />
<hkern u1="V" u2="&#xd6;" k="20" />
<hkern u1="V" u2="&#xd5;" k="20" />
<hkern u1="V" u2="&#xd4;" k="20" />
<hkern u1="V" u2="&#xd3;" k="20" />
<hkern u1="V" u2="&#xd2;" k="20" />
<hkern u1="V" u2="&#xc7;" k="20" />
<hkern u1="V" u2="&#xc5;" k="82" />
<hkern u1="V" u2="&#xc4;" k="82" />
<hkern u1="V" u2="&#xc3;" k="82" />
<hkern u1="V" u2="&#xc2;" k="82" />
<hkern u1="V" u2="&#xc1;" k="82" />
<hkern u1="V" u2="&#xc0;" k="82" />
<hkern u1="V" u2="u" k="20" />
<hkern u1="V" u2="s" k="20" />
<hkern u1="V" u2="r" k="20" />
<hkern u1="V" u2="q" k="41" />
<hkern u1="V" u2="p" k="20" />
<hkern u1="V" u2="o" k="41" />
<hkern u1="V" u2="n" k="20" />
<hkern u1="V" u2="m" k="20" />
<hkern u1="V" u2="g" k="20" />
<hkern u1="V" u2="e" k="41" />
<hkern u1="V" u2="d" k="41" />
<hkern u1="V" u2="c" k="41" />
<hkern u1="V" u2="a" k="41" />
<hkern u1="V" u2="Q" k="20" />
<hkern u1="V" u2="O" k="20" />
<hkern u1="V" u2="G" k="20" />
<hkern u1="V" u2="C" k="20" />
<hkern u1="V" u2="A" k="82" />
<hkern u1="V" u2="&#x3f;" k="-41" />
<hkern u1="V" u2="&#x2e;" k="102" />
<hkern u1="V" u2="&#x2c;" k="102" />
<hkern u1="W" u2="&#x201e;" k="102" />
<hkern u1="W" u2="&#x201a;" k="102" />
<hkern u1="W" u2="&#x153;" k="41" />
<hkern u1="W" u2="&#x152;" k="20" />
<hkern u1="W" u2="&#xfc;" k="20" />
<hkern u1="W" u2="&#xfb;" k="20" />
<hkern u1="W" u2="&#xfa;" k="20" />
<hkern u1="W" u2="&#xf9;" k="20" />
<hkern u1="W" u2="&#xf8;" k="41" />
<hkern u1="W" u2="&#xf6;" k="41" />
<hkern u1="W" u2="&#xf5;" k="41" />
<hkern u1="W" u2="&#xf4;" k="41" />
<hkern u1="W" u2="&#xf3;" k="41" />
<hkern u1="W" u2="&#xf2;" k="41" />
<hkern u1="W" u2="&#xeb;" k="41" />
<hkern u1="W" u2="&#xea;" k="41" />
<hkern u1="W" u2="&#xe9;" k="41" />
<hkern u1="W" u2="&#xe8;" k="41" />
<hkern u1="W" u2="&#xe7;" k="41" />
<hkern u1="W" u2="&#xe6;" k="41" />
<hkern u1="W" u2="&#xe5;" k="41" />
<hkern u1="W" u2="&#xe4;" k="41" />
<hkern u1="W" u2="&#xe3;" k="41" />
<hkern u1="W" u2="&#xe2;" k="41" />
<hkern u1="W" u2="&#xe1;" k="41" />
<hkern u1="W" u2="&#xe0;" k="41" />
<hkern u1="W" u2="&#xd8;" k="20" />
<hkern u1="W" u2="&#xd6;" k="20" />
<hkern u1="W" u2="&#xd5;" k="20" />
<hkern u1="W" u2="&#xd4;" k="20" />
<hkern u1="W" u2="&#xd3;" k="20" />
<hkern u1="W" u2="&#xd2;" k="20" />
<hkern u1="W" u2="&#xc7;" k="20" />
<hkern u1="W" u2="&#xc5;" k="82" />
<hkern u1="W" u2="&#xc4;" k="82" />
<hkern u1="W" u2="&#xc3;" k="82" />
<hkern u1="W" u2="&#xc2;" k="82" />
<hkern u1="W" u2="&#xc1;" k="82" />
<hkern u1="W" u2="&#xc0;" k="82" />
<hkern u1="W" u2="u" k="20" />
<hkern u1="W" u2="s" k="20" />
<hkern u1="W" u2="r" k="20" />
<hkern u1="W" u2="q" k="41" />
<hkern u1="W" u2="p" k="20" />
<hkern u1="W" u2="o" k="41" />
<hkern u1="W" u2="n" k="20" />
<hkern u1="W" u2="m" k="20" />
<hkern u1="W" u2="g" k="20" />
<hkern u1="W" u2="e" k="41" />
<hkern u1="W" u2="d" k="41" />
<hkern u1="W" u2="c" k="41" />
<hkern u1="W" u2="a" k="41" />
<hkern u1="W" u2="Q" k="20" />
<hkern u1="W" u2="O" k="20" />
<hkern u1="W" u2="G" k="20" />
<hkern u1="W" u2="C" k="20" />
<hkern u1="W" u2="A" k="82" />
<hkern u1="W" u2="&#x3f;" k="-41" />
<hkern u1="W" u2="&#x2e;" k="102" />
<hkern u1="W" u2="&#x2c;" k="102" />
<hkern u1="X" u2="&#x152;" k="41" />
<hkern u1="X" u2="&#xd8;" k="41" />
<hkern u1="X" u2="&#xd6;" k="41" />
<hkern u1="X" u2="&#xd5;" k="41" />
<hkern u1="X" u2="&#xd4;" k="41" />
<hkern u1="X" u2="&#xd3;" k="41" />
<hkern u1="X" u2="&#xd2;" k="41" />
<hkern u1="X" u2="&#xc7;" k="41" />
<hkern u1="X" u2="Q" k="41" />
<hkern u1="X" u2="O" k="41" />
<hkern u1="X" u2="G" k="41" />
<hkern u1="X" u2="C" k="41" />
<hkern u1="Y" u2="&#x201e;" k="123" />
<hkern u1="Y" u2="&#x201a;" k="123" />
<hkern u1="Y" u2="&#x153;" k="102" />
<hkern u1="Y" u2="&#x152;" k="41" />
<hkern u1="Y" u2="&#xfc;" k="61" />
<hkern u1="Y" u2="&#xfb;" k="61" />
<hkern u1="Y" u2="&#xfa;" k="61" />
<hkern u1="Y" u2="&#xf9;" k="61" />
<hkern u1="Y" u2="&#xf8;" k="102" />
<hkern u1="Y" u2="&#xf6;" k="102" />
<hkern u1="Y" u2="&#xf5;" k="102" />
<hkern u1="Y" u2="&#xf4;" k="102" />
<hkern u1="Y" u2="&#xf3;" k="102" />
<hkern u1="Y" u2="&#xf2;" k="102" />
<hkern u1="Y" u2="&#xeb;" k="102" />
<hkern u1="Y" u2="&#xea;" k="102" />
<hkern u1="Y" u2="&#xe9;" k="102" />
<hkern u1="Y" u2="&#xe8;" k="102" />
<hkern u1="Y" u2="&#xe7;" k="102" />
<hkern u1="Y" u2="&#xe6;" k="102" />
<hkern u1="Y" u2="&#xe5;" k="102" />
<hkern u1="Y" u2="&#xe4;" k="102" />
<hkern u1="Y" u2="&#xe3;" k="102" />
<hkern u1="Y" u2="&#xe2;" k="102" />
<hkern u1="Y" u2="&#xe1;" k="102" />
<hkern u1="Y" u2="&#xe0;" k="102" />
<hkern u1="Y" u2="&#xd8;" k="41" />
<hkern u1="Y" u2="&#xd6;" k="41" />
<hkern u1="Y" u2="&#xd5;" k="41" />
<hkern u1="Y" u2="&#xd4;" k="41" />
<hkern u1="Y" u2="&#xd3;" k="41" />
<hkern u1="Y" u2="&#xd2;" k="41" />
<hkern u1="Y" u2="&#xc7;" k="41" />
<hkern u1="Y" u2="&#xc5;" k="123" />
<hkern u1="Y" u2="&#xc4;" k="123" />
<hkern u1="Y" u2="&#xc3;" k="123" />
<hkern u1="Y" u2="&#xc2;" k="123" />
<hkern u1="Y" u2="&#xc1;" k="123" />
<hkern u1="Y" u2="&#xc0;" k="123" />
<hkern u1="Y" u2="z" k="41" />
<hkern u1="Y" u2="u" k="61" />
<hkern u1="Y" u2="s" k="82" />
<hkern u1="Y" u2="r" k="61" />
<hkern u1="Y" u2="q" k="102" />
<hkern u1="Y" u2="p" k="61" />
<hkern u1="Y" u2="o" k="102" />
<hkern u1="Y" u2="n" k="61" />
<hkern u1="Y" u2="m" k="61" />
<hkern u1="Y" u2="g" k="41" />
<hkern u1="Y" u2="e" k="102" />
<hkern u1="Y" u2="d" k="102" />
<hkern u1="Y" u2="c" k="102" />
<hkern u1="Y" u2="a" k="102" />
<hkern u1="Y" u2="Q" k="41" />
<hkern u1="Y" u2="O" k="41" />
<hkern u1="Y" u2="G" k="41" />
<hkern u1="Y" u2="C" k="41" />
<hkern u1="Y" u2="A" k="123" />
<hkern u1="Y" u2="&#x3f;" k="-41" />
<hkern u1="Y" u2="&#x2e;" k="123" />
<hkern u1="Y" u2="&#x2c;" k="123" />
<hkern u1="Z" u2="&#x152;" k="20" />
<hkern u1="Z" u2="&#xd8;" k="20" />
<hkern u1="Z" u2="&#xd6;" k="20" />
<hkern u1="Z" u2="&#xd5;" k="20" />
<hkern u1="Z" u2="&#xd4;" k="20" />
<hkern u1="Z" u2="&#xd3;" k="20" />
<hkern u1="Z" u2="&#xd2;" k="20" />
<hkern u1="Z" u2="&#xc7;" k="20" />
<hkern u1="Z" u2="Q" k="20" />
<hkern u1="Z" u2="O" k="20" />
<hkern u1="Z" u2="G" k="20" />
<hkern u1="Z" u2="C" k="20" />
<hkern u1="[" u2="J" k="-184" />
<hkern u1="a" u2="&#x201d;" k="20" />
<hkern u1="a" u2="&#x2019;" k="20" />
<hkern u1="a" u2="&#x27;" k="20" />
<hkern u1="a" u2="&#x22;" k="20" />
<hkern u1="b" u2="&#x201d;" k="20" />
<hkern u1="b" u2="&#x2019;" k="20" />
<hkern u1="b" u2="&#xfd;" k="41" />
<hkern u1="b" u2="z" k="20" />
<hkern u1="b" u2="y" k="41" />
<hkern u1="b" u2="x" k="41" />
<hkern u1="b" u2="w" k="41" />
<hkern u1="b" u2="v" k="41" />
<hkern u1="b" u2="&#x27;" k="20" />
<hkern u1="b" u2="&#x22;" k="20" />
<hkern u1="c" u2="&#x201d;" k="-41" />
<hkern u1="c" u2="&#x2019;" k="-41" />
<hkern u1="c" u2="&#x27;" k="-41" />
<hkern u1="c" u2="&#x22;" k="-41" />
<hkern u1="e" u2="&#x201d;" k="20" />
<hkern u1="e" u2="&#x2019;" k="20" />
<hkern u1="e" u2="&#xfd;" k="41" />
<hkern u1="e" u2="z" k="20" />
<hkern u1="e" u2="y" k="41" />
<hkern u1="e" u2="x" k="41" />
<hkern u1="e" u2="w" k="41" />
<hkern u1="e" u2="v" k="41" />
<hkern u1="e" u2="&#x27;" k="20" />
<hkern u1="e" u2="&#x22;" k="20" />
<hkern u1="f" u2="&#x201d;" k="-123" />
<hkern u1="f" u2="&#x2019;" k="-123" />
<hkern u1="f" u2="&#x27;" k="-123" />
<hkern u1="f" u2="&#x22;" k="-123" />
<hkern u1="h" u2="&#x201d;" k="20" />
<hkern u1="h" u2="&#x2019;" k="20" />
<hkern u1="h" u2="&#x27;" k="20" />
<hkern u1="h" u2="&#x22;" k="20" />
<hkern u1="k" u2="&#x153;" k="41" />
<hkern u1="k" u2="&#xf8;" k="41" />
<hkern u1="k" u2="&#xf6;" k="41" />
<hkern u1="k" u2="&#xf5;" k="41" />
<hkern u1="k" u2="&#xf4;" k="41" />
<hkern u1="k" u2="&#xf3;" k="41" />
<hkern u1="k" u2="&#xf2;" k="41" />
<hkern u1="k" u2="&#xeb;" k="41" />
<hkern u1="k" u2="&#xea;" k="41" />
<hkern u1="k" u2="&#xe9;" k="41" />
<hkern u1="k" u2="&#xe8;" k="41" />
<hkern u1="k" u2="&#xe7;" k="41" />
<hkern u1="k" u2="&#xe0;" k="41" />
<hkern u1="k" u2="q" k="41" />
<hkern u1="k" u2="o" k="41" />
<hkern u1="k" u2="e" k="41" />
<hkern u1="k" u2="d" k="41" />
<hkern u1="k" u2="c" k="41" />
<hkern u1="m" u2="&#x201d;" k="20" />
<hkern u1="m" u2="&#x2019;" k="20" />
<hkern u1="m" u2="&#x27;" k="20" />
<hkern u1="m" u2="&#x22;" k="20" />
<hkern u1="n" u2="&#x201d;" k="20" />
<hkern u1="n" u2="&#x2019;" k="20" />
<hkern u1="n" u2="&#x27;" k="20" />
<hkern u1="n" u2="&#x22;" k="20" />
<hkern u1="o" u2="&#x201d;" k="20" />
<hkern u1="o" u2="&#x2019;" k="20" />
<hkern u1="o" u2="&#xfd;" k="41" />
<hkern u1="o" u2="z" k="20" />
<hkern u1="o" u2="y" k="41" />
<hkern u1="o" u2="x" k="41" />
<hkern u1="o" u2="w" k="41" />
<hkern u1="o" u2="v" k="41" />
<hkern u1="o" u2="&#x27;" k="20" />
<hkern u1="o" u2="&#x22;" k="20" />
<hkern u1="p" u2="&#x201d;" k="20" />
<hkern u1="p" u2="&#x2019;" k="20" />
<hkern u1="p" u2="&#xfd;" k="41" />
<hkern u1="p" u2="z" k="20" />
<hkern u1="p" u2="y" k="41" />
<hkern u1="p" u2="x" k="41" />
<hkern u1="p" u2="w" k="41" />
<hkern u1="p" u2="v" k="41" />
<hkern u1="p" u2="&#x27;" k="20" />
<hkern u1="p" u2="&#x22;" k="20" />
<hkern u1="r" u2="&#x201d;" k="-82" />
<hkern u1="r" u2="&#x2019;" k="-82" />
<hkern u1="r" u2="&#x153;" k="41" />
<hkern u1="r" u2="&#xf8;" k="41" />
<hkern u1="r" u2="&#xf6;" k="41" />
<hkern u1="r" u2="&#xf5;" k="41" />
<hkern u1="r" u2="&#xf4;" k="41" />
<hkern u1="r" u2="&#xf3;" k="41" />
<hkern u1="r" u2="&#xf2;" k="41" />
<hkern u1="r" u2="&#xeb;" k="41" />
<hkern u1="r" u2="&#xea;" k="41" />
<hkern u1="r" u2="&#xe9;" k="41" />
<hkern u1="r" u2="&#xe8;" k="41" />
<hkern u1="r" u2="&#xe7;" k="41" />
<hkern u1="r" u2="&#xe6;" k="41" />
<hkern u1="r" u2="&#xe5;" k="41" />
<hkern u1="r" u2="&#xe4;" k="41" />
<hkern u1="r" u2="&#xe3;" k="41" />
<hkern u1="r" u2="&#xe2;" k="41" />
<hkern u1="r" u2="&#xe1;" k="41" />
<hkern u1="r" u2="&#xe0;" k="41" />
<hkern u1="r" u2="q" k="41" />
<hkern u1="r" u2="o" k="41" />
<hkern u1="r" u2="g" k="20" />
<hkern u1="r" u2="e" k="41" />
<hkern u1="r" u2="d" k="41" />
<hkern u1="r" u2="c" k="41" />
<hkern u1="r" u2="a" k="41" />
<hkern u1="r" u2="&#x27;" k="-82" />
<hkern u1="r" u2="&#x22;" k="-82" />
<hkern u1="t" u2="&#x201d;" k="-41" />
<hkern u1="t" u2="&#x2019;" k="-41" />
<hkern u1="t" u2="&#x27;" k="-41" />
<hkern u1="t" u2="&#x22;" k="-41" />
<hkern u1="v" u2="&#x201e;" k="82" />
<hkern u1="v" u2="&#x201d;" k="-82" />
<hkern u1="v" u2="&#x201a;" k="82" />
<hkern u1="v" u2="&#x2019;" k="-82" />
<hkern u1="v" u2="&#x3f;" k="-41" />
<hkern u1="v" u2="&#x2e;" k="82" />
<hkern u1="v" u2="&#x2c;" k="82" />
<hkern u1="v" u2="&#x27;" k="-82" />
<hkern u1="v" u2="&#x22;" k="-82" />
<hkern u1="w" u2="&#x201e;" k="82" />
<hkern u1="w" u2="&#x201d;" k="-82" />
<hkern u1="w" u2="&#x201a;" k="82" />
<hkern u1="w" u2="&#x2019;" k="-82" />
<hkern u1="w" u2="&#x3f;" k="-41" />
<hkern u1="w" u2="&#x2e;" k="82" />
<hkern u1="w" u2="&#x2c;" k="82" />
<hkern u1="w" u2="&#x27;" k="-82" />
<hkern u1="w" u2="&#x22;" k="-82" />
<hkern u1="x" u2="&#x153;" k="41" />
<hkern u1="x" u2="&#xf8;" k="41" />
<hkern u1="x" u2="&#xf6;" k="41" />
<hkern u1="x" u2="&#xf5;" k="41" />
<hkern u1="x" u2="&#xf4;" k="41" />
<hkern u1="x" u2="&#xf3;" k="41" />
<hkern u1="x" u2="&#xf2;" k="41" />
<hkern u1="x" u2="&#xeb;" k="41" />
<hkern u1="x" u2="&#xea;" k="41" />
<hkern u1="x" u2="&#xe9;" k="41" />
<hkern u1="x" u2="&#xe8;" k="41" />
<hkern u1="x" u2="&#xe7;" k="41" />
<hkern u1="x" u2="&#xe0;" k="41" />
<hkern u1="x" u2="q" k="41" />
<hkern u1="x" u2="o" k="41" />
<hkern u1="x" u2="e" k="41" />
<hkern u1="x" u2="d" k="41" />
<hkern u1="x" u2="c" k="41" />
<hkern u1="y" u2="&#x201e;" k="82" />
<hkern u1="y" u2="&#x201d;" k="-82" />
<hkern u1="y" u2="&#x201a;" k="82" />
<hkern u1="y" u2="&#x2019;" k="-82" />
<hkern u1="y" u2="&#x3f;" k="-41" />
<hkern u1="y" u2="&#x2e;" k="82" />
<hkern u1="y" u2="&#x2c;" k="82" />
<hkern u1="y" u2="&#x27;" k="-82" />
<hkern u1="y" u2="&#x22;" k="-82" />
<hkern u1="&#x7b;" u2="J" k="-184" />
<hkern u1="&#xc0;" u2="&#x201d;" k="143" />
<hkern u1="&#xc0;" u2="&#x2019;" k="143" />
<hkern u1="&#xc0;" u2="&#x178;" k="123" />
<hkern u1="&#xc0;" u2="&#x152;" k="41" />
<hkern u1="&#xc0;" u2="&#xdd;" k="123" />
<hkern u1="&#xc0;" u2="&#xd8;" k="41" />
<hkern u1="&#xc0;" u2="&#xd6;" k="41" />
<hkern u1="&#xc0;" u2="&#xd5;" k="41" />
<hkern u1="&#xc0;" u2="&#xd4;" k="41" />
<hkern u1="&#xc0;" u2="&#xd3;" k="41" />
<hkern u1="&#xc0;" u2="&#xd2;" k="41" />
<hkern u1="&#xc0;" u2="&#xc7;" k="41" />
<hkern u1="&#xc0;" u2="Y" k="123" />
<hkern u1="&#xc0;" u2="W" k="82" />
<hkern u1="&#xc0;" u2="V" k="82" />
<hkern u1="&#xc0;" u2="T" k="143" />
<hkern u1="&#xc0;" u2="Q" k="41" />
<hkern u1="&#xc0;" u2="O" k="41" />
<hkern u1="&#xc0;" u2="J" k="-266" />
<hkern u1="&#xc0;" u2="G" k="41" />
<hkern u1="&#xc0;" u2="C" k="41" />
<hkern u1="&#xc0;" u2="&#x27;" k="143" />
<hkern u1="&#xc0;" u2="&#x22;" k="143" />
<hkern u1="&#xc1;" u2="&#x201d;" k="143" />
<hkern u1="&#xc1;" u2="&#x2019;" k="143" />
<hkern u1="&#xc1;" u2="&#x178;" k="123" />
<hkern u1="&#xc1;" u2="&#x152;" k="41" />
<hkern u1="&#xc1;" u2="&#xdd;" k="123" />
<hkern u1="&#xc1;" u2="&#xd8;" k="41" />
<hkern u1="&#xc1;" u2="&#xd6;" k="41" />
<hkern u1="&#xc1;" u2="&#xd5;" k="41" />
<hkern u1="&#xc1;" u2="&#xd4;" k="41" />
<hkern u1="&#xc1;" u2="&#xd3;" k="41" />
<hkern u1="&#xc1;" u2="&#xd2;" k="41" />
<hkern u1="&#xc1;" u2="&#xc7;" k="41" />
<hkern u1="&#xc1;" u2="Y" k="123" />
<hkern u1="&#xc1;" u2="W" k="82" />
<hkern u1="&#xc1;" u2="V" k="82" />
<hkern u1="&#xc1;" u2="T" k="143" />
<hkern u1="&#xc1;" u2="Q" k="41" />
<hkern u1="&#xc1;" u2="O" k="41" />
<hkern u1="&#xc1;" u2="J" k="-266" />
<hkern u1="&#xc1;" u2="G" k="41" />
<hkern u1="&#xc1;" u2="C" k="41" />
<hkern u1="&#xc1;" u2="&#x27;" k="143" />
<hkern u1="&#xc1;" u2="&#x22;" k="143" />
<hkern u1="&#xc2;" u2="&#x201d;" k="143" />
<hkern u1="&#xc2;" u2="&#x2019;" k="143" />
<hkern u1="&#xc2;" u2="&#x178;" k="123" />
<hkern u1="&#xc2;" u2="&#x152;" k="41" />
<hkern u1="&#xc2;" u2="&#xdd;" k="123" />
<hkern u1="&#xc2;" u2="&#xd8;" k="41" />
<hkern u1="&#xc2;" u2="&#xd6;" k="41" />
<hkern u1="&#xc2;" u2="&#xd5;" k="41" />
<hkern u1="&#xc2;" u2="&#xd4;" k="41" />
<hkern u1="&#xc2;" u2="&#xd3;" k="41" />
<hkern u1="&#xc2;" u2="&#xd2;" k="41" />
<hkern u1="&#xc2;" u2="&#xc7;" k="41" />
<hkern u1="&#xc2;" u2="Y" k="123" />
<hkern u1="&#xc2;" u2="W" k="82" />
<hkern u1="&#xc2;" u2="V" k="82" />
<hkern u1="&#xc2;" u2="T" k="143" />
<hkern u1="&#xc2;" u2="Q" k="41" />
<hkern u1="&#xc2;" u2="O" k="41" />
<hkern u1="&#xc2;" u2="J" k="-266" />
<hkern u1="&#xc2;" u2="G" k="41" />
<hkern u1="&#xc2;" u2="C" k="41" />
<hkern u1="&#xc2;" u2="&#x27;" k="143" />
<hkern u1="&#xc2;" u2="&#x22;" k="143" />
<hkern u1="&#xc3;" u2="&#x201d;" k="143" />
<hkern u1="&#xc3;" u2="&#x2019;" k="143" />
<hkern u1="&#xc3;" u2="&#x178;" k="123" />
<hkern u1="&#xc3;" u2="&#x152;" k="41" />
<hkern u1="&#xc3;" u2="&#xdd;" k="123" />
<hkern u1="&#xc3;" u2="&#xd8;" k="41" />
<hkern u1="&#xc3;" u2="&#xd6;" k="41" />
<hkern u1="&#xc3;" u2="&#xd5;" k="41" />
<hkern u1="&#xc3;" u2="&#xd4;" k="41" />
<hkern u1="&#xc3;" u2="&#xd3;" k="41" />
<hkern u1="&#xc3;" u2="&#xd2;" k="41" />
<hkern u1="&#xc3;" u2="&#xc7;" k="41" />
<hkern u1="&#xc3;" u2="Y" k="123" />
<hkern u1="&#xc3;" u2="W" k="82" />
<hkern u1="&#xc3;" u2="V" k="82" />
<hkern u1="&#xc3;" u2="T" k="143" />
<hkern u1="&#xc3;" u2="Q" k="41" />
<hkern u1="&#xc3;" u2="O" k="41" />
<hkern u1="&#xc3;" u2="J" k="-266" />
<hkern u1="&#xc3;" u2="G" k="41" />
<hkern u1="&#xc3;" u2="C" k="41" />
<hkern u1="&#xc3;" u2="&#x27;" k="143" />
<hkern u1="&#xc3;" u2="&#x22;" k="143" />
<hkern u1="&#xc4;" u2="&#x201d;" k="143" />
<hkern u1="&#xc4;" u2="&#x2019;" k="143" />
<hkern u1="&#xc4;" u2="&#x178;" k="123" />
<hkern u1="&#xc4;" u2="&#x152;" k="41" />
<hkern u1="&#xc4;" u2="&#xdd;" k="123" />
<hkern u1="&#xc4;" u2="&#xd8;" k="41" />
<hkern u1="&#xc4;" u2="&#xd6;" k="41" />
<hkern u1="&#xc4;" u2="&#xd5;" k="41" />
<hkern u1="&#xc4;" u2="&#xd4;" k="41" />
<hkern u1="&#xc4;" u2="&#xd3;" k="41" />
<hkern u1="&#xc4;" u2="&#xd2;" k="41" />
<hkern u1="&#xc4;" u2="&#xc7;" k="41" />
<hkern u1="&#xc4;" u2="Y" k="123" />
<hkern u1="&#xc4;" u2="W" k="82" />
<hkern u1="&#xc4;" u2="V" k="82" />
<hkern u1="&#xc4;" u2="T" k="143" />
<hkern u1="&#xc4;" u2="Q" k="41" />
<hkern u1="&#xc4;" u2="O" k="41" />
<hkern u1="&#xc4;" u2="J" k="-266" />
<hkern u1="&#xc4;" u2="G" k="41" />
<hkern u1="&#xc4;" u2="C" k="41" />
<hkern u1="&#xc4;" u2="&#x27;" k="143" />
<hkern u1="&#xc4;" u2="&#x22;" k="143" />
<hkern u1="&#xc5;" u2="&#x201d;" k="143" />
<hkern u1="&#xc5;" u2="&#x2019;" k="143" />
<hkern u1="&#xc5;" u2="&#x178;" k="123" />
<hkern u1="&#xc5;" u2="&#x152;" k="41" />
<hkern u1="&#xc5;" u2="&#xdd;" k="123" />
<hkern u1="&#xc5;" u2="&#xd8;" k="41" />
<hkern u1="&#xc5;" u2="&#xd6;" k="41" />
<hkern u1="&#xc5;" u2="&#xd5;" k="41" />
<hkern u1="&#xc5;" u2="&#xd4;" k="41" />
<hkern u1="&#xc5;" u2="&#xd3;" k="41" />
<hkern u1="&#xc5;" u2="&#xd2;" k="41" />
<hkern u1="&#xc5;" u2="&#xc7;" k="41" />
<hkern u1="&#xc5;" u2="Y" k="123" />
<hkern u1="&#xc5;" u2="W" k="82" />
<hkern u1="&#xc5;" u2="V" k="82" />
<hkern u1="&#xc5;" u2="T" k="143" />
<hkern u1="&#xc5;" u2="Q" k="41" />
<hkern u1="&#xc5;" u2="O" k="41" />
<hkern u1="&#xc5;" u2="J" k="-266" />
<hkern u1="&#xc5;" u2="G" k="41" />
<hkern u1="&#xc5;" u2="C" k="41" />
<hkern u1="&#xc5;" u2="&#x27;" k="143" />
<hkern u1="&#xc5;" u2="&#x22;" k="143" />
<hkern u1="&#xc6;" u2="J" k="-123" />
<hkern u1="&#xc7;" u2="&#x152;" k="41" />
<hkern u1="&#xc7;" u2="&#xd8;" k="41" />
<hkern u1="&#xc7;" u2="&#xd6;" k="41" />
<hkern u1="&#xc7;" u2="&#xd5;" k="41" />
<hkern u1="&#xc7;" u2="&#xd4;" k="41" />
<hkern u1="&#xc7;" u2="&#xd3;" k="41" />
<hkern u1="&#xc7;" u2="&#xd2;" k="41" />
<hkern u1="&#xc7;" u2="&#xc7;" k="41" />
<hkern u1="&#xc7;" u2="Q" k="41" />
<hkern u1="&#xc7;" u2="O" k="41" />
<hkern u1="&#xc7;" u2="G" k="41" />
<hkern u1="&#xc7;" u2="C" k="41" />
<hkern u1="&#xc8;" u2="J" k="-123" />
<hkern u1="&#xc9;" u2="J" k="-123" />
<hkern u1="&#xca;" u2="J" k="-123" />
<hkern u1="&#xcb;" u2="J" k="-123" />
<hkern u1="&#xd0;" u2="&#x201e;" k="82" />
<hkern u1="&#xd0;" u2="&#x201a;" k="82" />
<hkern u1="&#xd0;" u2="&#x178;" k="20" />
<hkern u1="&#xd0;" u2="&#xdd;" k="20" />
<hkern u1="&#xd0;" u2="&#xc5;" k="41" />
<hkern u1="&#xd0;" u2="&#xc4;" k="41" />
<hkern u1="&#xd0;" u2="&#xc3;" k="41" />
<hkern u1="&#xd0;" u2="&#xc2;" k="41" />
<hkern u1="&#xd0;" u2="&#xc1;" k="41" />
<hkern u1="&#xd0;" u2="&#xc0;" k="41" />
<hkern u1="&#xd0;" u2="Z" k="20" />
<hkern u1="&#xd0;" u2="Y" k="20" />
<hkern u1="&#xd0;" u2="X" k="41" />
<hkern u1="&#xd0;" u2="W" k="20" />
<hkern u1="&#xd0;" u2="V" k="20" />
<hkern u1="&#xd0;" u2="T" k="61" />
<hkern u1="&#xd0;" u2="A" k="41" />
<hkern u1="&#xd0;" u2="&#x2e;" k="82" />
<hkern u1="&#xd0;" u2="&#x2c;" k="82" />
<hkern u1="&#xd2;" u2="&#x201e;" k="82" />
<hkern u1="&#xd2;" u2="&#x201a;" k="82" />
<hkern u1="&#xd2;" u2="&#x178;" k="20" />
<hkern u1="&#xd2;" u2="&#xdd;" k="20" />
<hkern u1="&#xd2;" u2="&#xc5;" k="41" />
<hkern u1="&#xd2;" u2="&#xc4;" k="41" />
<hkern u1="&#xd2;" u2="&#xc3;" k="41" />
<hkern u1="&#xd2;" u2="&#xc2;" k="41" />
<hkern u1="&#xd2;" u2="&#xc1;" k="41" />
<hkern u1="&#xd2;" u2="&#xc0;" k="41" />
<hkern u1="&#xd2;" u2="Z" k="20" />
<hkern u1="&#xd2;" u2="Y" k="20" />
<hkern u1="&#xd2;" u2="X" k="41" />
<hkern u1="&#xd2;" u2="W" k="20" />
<hkern u1="&#xd2;" u2="V" k="20" />
<hkern u1="&#xd2;" u2="T" k="61" />
<hkern u1="&#xd2;" u2="A" k="41" />
<hkern u1="&#xd2;" u2="&#x2e;" k="82" />
<hkern u1="&#xd2;" u2="&#x2c;" k="82" />
<hkern u1="&#xd3;" u2="&#x201e;" k="82" />
<hkern u1="&#xd3;" u2="&#x201a;" k="82" />
<hkern u1="&#xd3;" u2="&#x178;" k="20" />
<hkern u1="&#xd3;" u2="&#xdd;" k="20" />
<hkern u1="&#xd3;" u2="&#xc5;" k="41" />
<hkern u1="&#xd3;" u2="&#xc4;" k="41" />
<hkern u1="&#xd3;" u2="&#xc3;" k="41" />
<hkern u1="&#xd3;" u2="&#xc2;" k="41" />
<hkern u1="&#xd3;" u2="&#xc1;" k="41" />
<hkern u1="&#xd3;" u2="&#xc0;" k="41" />
<hkern u1="&#xd3;" u2="Z" k="20" />
<hkern u1="&#xd3;" u2="Y" k="20" />
<hkern u1="&#xd3;" u2="X" k="41" />
<hkern u1="&#xd3;" u2="W" k="20" />
<hkern u1="&#xd3;" u2="V" k="20" />
<hkern u1="&#xd3;" u2="T" k="61" />
<hkern u1="&#xd3;" u2="A" k="41" />
<hkern u1="&#xd3;" u2="&#x2e;" k="82" />
<hkern u1="&#xd3;" u2="&#x2c;" k="82" />
<hkern u1="&#xd4;" u2="&#x201e;" k="82" />
<hkern u1="&#xd4;" u2="&#x201a;" k="82" />
<hkern u1="&#xd4;" u2="&#x178;" k="20" />
<hkern u1="&#xd4;" u2="&#xdd;" k="20" />
<hkern u1="&#xd4;" u2="&#xc5;" k="41" />
<hkern u1="&#xd4;" u2="&#xc4;" k="41" />
<hkern u1="&#xd4;" u2="&#xc3;" k="41" />
<hkern u1="&#xd4;" u2="&#xc2;" k="41" />
<hkern u1="&#xd4;" u2="&#xc1;" k="41" />
<hkern u1="&#xd4;" u2="&#xc0;" k="41" />
<hkern u1="&#xd4;" u2="Z" k="20" />
<hkern u1="&#xd4;" u2="Y" k="20" />
<hkern u1="&#xd4;" u2="X" k="41" />
<hkern u1="&#xd4;" u2="W" k="20" />
<hkern u1="&#xd4;" u2="V" k="20" />
<hkern u1="&#xd4;" u2="T" k="61" />
<hkern u1="&#xd4;" u2="A" k="41" />
<hkern u1="&#xd4;" u2="&#x2e;" k="82" />
<hkern u1="&#xd4;" u2="&#x2c;" k="82" />
<hkern u1="&#xd5;" u2="&#x201e;" k="82" />
<hkern u1="&#xd5;" u2="&#x201a;" k="82" />
<hkern u1="&#xd5;" u2="&#x178;" k="20" />
<hkern u1="&#xd5;" u2="&#xdd;" k="20" />
<hkern u1="&#xd5;" u2="&#xc5;" k="41" />
<hkern u1="&#xd5;" u2="&#xc4;" k="41" />
<hkern u1="&#xd5;" u2="&#xc3;" k="41" />
<hkern u1="&#xd5;" u2="&#xc2;" k="41" />
<hkern u1="&#xd5;" u2="&#xc1;" k="41" />
<hkern u1="&#xd5;" u2="&#xc0;" k="41" />
<hkern u1="&#xd5;" u2="Z" k="20" />
<hkern u1="&#xd5;" u2="Y" k="20" />
<hkern u1="&#xd5;" u2="X" k="41" />
<hkern u1="&#xd5;" u2="W" k="20" />
<hkern u1="&#xd5;" u2="V" k="20" />
<hkern u1="&#xd5;" u2="T" k="61" />
<hkern u1="&#xd5;" u2="A" k="41" />
<hkern u1="&#xd5;" u2="&#x2e;" k="82" />
<hkern u1="&#xd5;" u2="&#x2c;" k="82" />
<hkern u1="&#xd6;" u2="&#x201e;" k="82" />
<hkern u1="&#xd6;" u2="&#x201a;" k="82" />
<hkern u1="&#xd6;" u2="&#x178;" k="20" />
<hkern u1="&#xd6;" u2="&#xdd;" k="20" />
<hkern u1="&#xd6;" u2="&#xc5;" k="41" />
<hkern u1="&#xd6;" u2="&#xc4;" k="41" />
<hkern u1="&#xd6;" u2="&#xc3;" k="41" />
<hkern u1="&#xd6;" u2="&#xc2;" k="41" />
<hkern u1="&#xd6;" u2="&#xc1;" k="41" />
<hkern u1="&#xd6;" u2="&#xc0;" k="41" />
<hkern u1="&#xd6;" u2="Z" k="20" />
<hkern u1="&#xd6;" u2="Y" k="20" />
<hkern u1="&#xd6;" u2="X" k="41" />
<hkern u1="&#xd6;" u2="W" k="20" />
<hkern u1="&#xd6;" u2="V" k="20" />
<hkern u1="&#xd6;" u2="T" k="61" />
<hkern u1="&#xd6;" u2="A" k="41" />
<hkern u1="&#xd6;" u2="&#x2e;" k="82" />
<hkern u1="&#xd6;" u2="&#x2c;" k="82" />
<hkern u1="&#xd8;" u2="&#x201e;" k="82" />
<hkern u1="&#xd8;" u2="&#x201a;" k="82" />
<hkern u1="&#xd8;" u2="&#x178;" k="20" />
<hkern u1="&#xd8;" u2="&#xdd;" k="20" />
<hkern u1="&#xd8;" u2="&#xc5;" k="41" />
<hkern u1="&#xd8;" u2="&#xc4;" k="41" />
<hkern u1="&#xd8;" u2="&#xc3;" k="41" />
<hkern u1="&#xd8;" u2="&#xc2;" k="41" />
<hkern u1="&#xd8;" u2="&#xc1;" k="41" />
<hkern u1="&#xd8;" u2="&#xc0;" k="41" />
<hkern u1="&#xd8;" u2="Z" k="20" />
<hkern u1="&#xd8;" u2="Y" k="20" />
<hkern u1="&#xd8;" u2="X" k="41" />
<hkern u1="&#xd8;" u2="W" k="20" />
<hkern u1="&#xd8;" u2="V" k="20" />
<hkern u1="&#xd8;" u2="T" k="61" />
<hkern u1="&#xd8;" u2="A" k="41" />
<hkern u1="&#xd8;" u2="&#x2e;" k="82" />
<hkern u1="&#xd8;" u2="&#x2c;" k="82" />
<hkern u1="&#xd9;" u2="&#x201e;" k="41" />
<hkern u1="&#xd9;" u2="&#x201a;" k="41" />
<hkern u1="&#xd9;" u2="&#xc5;" k="20" />
<hkern u1="&#xd9;" u2="&#xc4;" k="20" />
<hkern u1="&#xd9;" u2="&#xc3;" k="20" />
<hkern u1="&#xd9;" u2="&#xc2;" k="20" />
<hkern u1="&#xd9;" u2="&#xc1;" k="20" />
<hkern u1="&#xd9;" u2="&#xc0;" k="20" />
<hkern u1="&#xd9;" u2="A" k="20" />
<hkern u1="&#xd9;" u2="&#x2e;" k="41" />
<hkern u1="&#xd9;" u2="&#x2c;" k="41" />
<hkern u1="&#xda;" u2="&#x201e;" k="41" />
<hkern u1="&#xda;" u2="&#x201a;" k="41" />
<hkern u1="&#xda;" u2="&#xc5;" k="20" />
<hkern u1="&#xda;" u2="&#xc4;" k="20" />
<hkern u1="&#xda;" u2="&#xc3;" k="20" />
<hkern u1="&#xda;" u2="&#xc2;" k="20" />
<hkern u1="&#xda;" u2="&#xc1;" k="20" />
<hkern u1="&#xda;" u2="&#xc0;" k="20" />
<hkern u1="&#xda;" u2="A" k="20" />
<hkern u1="&#xda;" u2="&#x2e;" k="41" />
<hkern u1="&#xda;" u2="&#x2c;" k="41" />
<hkern u1="&#xdb;" u2="&#x201e;" k="41" />
<hkern u1="&#xdb;" u2="&#x201a;" k="41" />
<hkern u1="&#xdb;" u2="&#xc5;" k="20" />
<hkern u1="&#xdb;" u2="&#xc4;" k="20" />
<hkern u1="&#xdb;" u2="&#xc3;" k="20" />
<hkern u1="&#xdb;" u2="&#xc2;" k="20" />
<hkern u1="&#xdb;" u2="&#xc1;" k="20" />
<hkern u1="&#xdb;" u2="&#xc0;" k="20" />
<hkern u1="&#xdb;" u2="A" k="20" />
<hkern u1="&#xdb;" u2="&#x2e;" k="41" />
<hkern u1="&#xdb;" u2="&#x2c;" k="41" />
<hkern u1="&#xdc;" u2="&#x201e;" k="41" />
<hkern u1="&#xdc;" u2="&#x201a;" k="41" />
<hkern u1="&#xdc;" u2="&#xc5;" k="20" />
<hkern u1="&#xdc;" u2="&#xc4;" k="20" />
<hkern u1="&#xdc;" u2="&#xc3;" k="20" />
<hkern u1="&#xdc;" u2="&#xc2;" k="20" />
<hkern u1="&#xdc;" u2="&#xc1;" k="20" />
<hkern u1="&#xdc;" u2="&#xc0;" k="20" />
<hkern u1="&#xdc;" u2="A" k="20" />
<hkern u1="&#xdc;" u2="&#x2e;" k="41" />
<hkern u1="&#xdc;" u2="&#x2c;" k="41" />
<hkern u1="&#xdd;" u2="&#x201e;" k="123" />
<hkern u1="&#xdd;" u2="&#x201a;" k="123" />
<hkern u1="&#xdd;" u2="&#x153;" k="102" />
<hkern u1="&#xdd;" u2="&#x152;" k="41" />
<hkern u1="&#xdd;" u2="&#xfc;" k="61" />
<hkern u1="&#xdd;" u2="&#xfb;" k="61" />
<hkern u1="&#xdd;" u2="&#xfa;" k="61" />
<hkern u1="&#xdd;" u2="&#xf9;" k="61" />
<hkern u1="&#xdd;" u2="&#xf8;" k="102" />
<hkern u1="&#xdd;" u2="&#xf6;" k="102" />
<hkern u1="&#xdd;" u2="&#xf5;" k="102" />
<hkern u1="&#xdd;" u2="&#xf4;" k="102" />
<hkern u1="&#xdd;" u2="&#xf3;" k="102" />
<hkern u1="&#xdd;" u2="&#xf2;" k="102" />
<hkern u1="&#xdd;" u2="&#xeb;" k="102" />
<hkern u1="&#xdd;" u2="&#xea;" k="102" />
<hkern u1="&#xdd;" u2="&#xe9;" k="102" />
<hkern u1="&#xdd;" u2="&#xe8;" k="102" />
<hkern u1="&#xdd;" u2="&#xe7;" k="102" />
<hkern u1="&#xdd;" u2="&#xe6;" k="102" />
<hkern u1="&#xdd;" u2="&#xe5;" k="102" />
<hkern u1="&#xdd;" u2="&#xe4;" k="102" />
<hkern u1="&#xdd;" u2="&#xe3;" k="102" />
<hkern u1="&#xdd;" u2="&#xe2;" k="102" />
<hkern u1="&#xdd;" u2="&#xe1;" k="102" />
<hkern u1="&#xdd;" u2="&#xe0;" k="102" />
<hkern u1="&#xdd;" u2="&#xd8;" k="41" />
<hkern u1="&#xdd;" u2="&#xd6;" k="41" />
<hkern u1="&#xdd;" u2="&#xd5;" k="41" />
<hkern u1="&#xdd;" u2="&#xd4;" k="41" />
<hkern u1="&#xdd;" u2="&#xd3;" k="41" />
<hkern u1="&#xdd;" u2="&#xd2;" k="41" />
<hkern u1="&#xdd;" u2="&#xc7;" k="41" />
<hkern u1="&#xdd;" u2="&#xc5;" k="123" />
<hkern u1="&#xdd;" u2="&#xc4;" k="123" />
<hkern u1="&#xdd;" u2="&#xc3;" k="123" />
<hkern u1="&#xdd;" u2="&#xc2;" k="123" />
<hkern u1="&#xdd;" u2="&#xc1;" k="123" />
<hkern u1="&#xdd;" u2="&#xc0;" k="123" />
<hkern u1="&#xdd;" u2="z" k="41" />
<hkern u1="&#xdd;" u2="u" k="61" />
<hkern u1="&#xdd;" u2="s" k="82" />
<hkern u1="&#xdd;" u2="r" k="61" />
<hkern u1="&#xdd;" u2="q" k="102" />
<hkern u1="&#xdd;" u2="p" k="61" />
<hkern u1="&#xdd;" u2="o" k="102" />
<hkern u1="&#xdd;" u2="n" k="61" />
<hkern u1="&#xdd;" u2="m" k="61" />
<hkern u1="&#xdd;" u2="g" k="41" />
<hkern u1="&#xdd;" u2="e" k="102" />
<hkern u1="&#xdd;" u2="d" k="102" />
<hkern u1="&#xdd;" u2="c" k="102" />
<hkern u1="&#xdd;" u2="a" k="102" />
<hkern u1="&#xdd;" u2="Q" k="41" />
<hkern u1="&#xdd;" u2="O" k="41" />
<hkern u1="&#xdd;" u2="G" k="41" />
<hkern u1="&#xdd;" u2="C" k="41" />
<hkern u1="&#xdd;" u2="A" k="123" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-41" />
<hkern u1="&#xdd;" u2="&#x2e;" k="123" />
<hkern u1="&#xdd;" u2="&#x2c;" k="123" />
<hkern u1="&#xde;" u2="&#x201e;" k="266" />
<hkern u1="&#xde;" u2="&#x201a;" k="266" />
<hkern u1="&#xde;" u2="&#xc5;" k="102" />
<hkern u1="&#xde;" u2="&#xc4;" k="102" />
<hkern u1="&#xde;" u2="&#xc3;" k="102" />
<hkern u1="&#xde;" u2="&#xc2;" k="102" />
<hkern u1="&#xde;" u2="&#xc1;" k="102" />
<hkern u1="&#xde;" u2="&#xc0;" k="102" />
<hkern u1="&#xde;" u2="Z" k="20" />
<hkern u1="&#xde;" u2="X" k="41" />
<hkern u1="&#xde;" u2="A" k="102" />
<hkern u1="&#xde;" u2="&#x2e;" k="266" />
<hkern u1="&#xde;" u2="&#x2c;" k="266" />
<hkern u1="&#xe0;" u2="&#x201d;" k="20" />
<hkern u1="&#xe0;" u2="&#x2019;" k="20" />
<hkern u1="&#xe0;" u2="&#x27;" k="20" />
<hkern u1="&#xe0;" u2="&#x22;" k="20" />
<hkern u1="&#xe1;" u2="&#x201d;" k="20" />
<hkern u1="&#xe1;" u2="&#x2019;" k="20" />
<hkern u1="&#xe1;" u2="&#x27;" k="20" />
<hkern u1="&#xe1;" u2="&#x22;" k="20" />
<hkern u1="&#xe2;" u2="&#x201d;" k="20" />
<hkern u1="&#xe2;" u2="&#x2019;" k="20" />
<hkern u1="&#xe2;" u2="&#x27;" k="20" />
<hkern u1="&#xe2;" u2="&#x22;" k="20" />
<hkern u1="&#xe3;" u2="&#x201d;" k="20" />
<hkern u1="&#xe3;" u2="&#x2019;" k="20" />
<hkern u1="&#xe3;" u2="&#x27;" k="20" />
<hkern u1="&#xe3;" u2="&#x22;" k="20" />
<hkern u1="&#xe4;" u2="&#x201d;" k="20" />
<hkern u1="&#xe4;" u2="&#x2019;" k="20" />
<hkern u1="&#xe4;" u2="&#x27;" k="20" />
<hkern u1="&#xe4;" u2="&#x22;" k="20" />
<hkern u1="&#xe5;" u2="&#x201d;" k="20" />
<hkern u1="&#xe5;" u2="&#x2019;" k="20" />
<hkern u1="&#xe5;" u2="&#x27;" k="20" />
<hkern u1="&#xe5;" u2="&#x22;" k="20" />
<hkern u1="&#xe8;" u2="&#x201d;" k="20" />
<hkern u1="&#xe8;" u2="&#x2019;" k="20" />
<hkern u1="&#xe8;" u2="&#xfd;" k="41" />
<hkern u1="&#xe8;" u2="z" k="20" />
<hkern u1="&#xe8;" u2="y" k="41" />
<hkern u1="&#xe8;" u2="x" k="41" />
<hkern u1="&#xe8;" u2="w" k="41" />
<hkern u1="&#xe8;" u2="v" k="41" />
<hkern u1="&#xe8;" u2="&#x27;" k="20" />
<hkern u1="&#xe8;" u2="&#x22;" k="20" />
<hkern u1="&#xe9;" u2="&#x201d;" k="20" />
<hkern u1="&#xe9;" u2="&#x2019;" k="20" />
<hkern u1="&#xe9;" u2="&#xfd;" k="41" />
<hkern u1="&#xe9;" u2="z" k="20" />
<hkern u1="&#xe9;" u2="y" k="41" />
<hkern u1="&#xe9;" u2="x" k="41" />
<hkern u1="&#xe9;" u2="w" k="41" />
<hkern u1="&#xe9;" u2="v" k="41" />
<hkern u1="&#xe9;" u2="&#x27;" k="20" />
<hkern u1="&#xe9;" u2="&#x22;" k="20" />
<hkern u1="&#xea;" u2="&#x201d;" k="20" />
<hkern u1="&#xea;" u2="&#x2019;" k="20" />
<hkern u1="&#xea;" u2="&#xfd;" k="41" />
<hkern u1="&#xea;" u2="z" k="20" />
<hkern u1="&#xea;" u2="y" k="41" />
<hkern u1="&#xea;" u2="x" k="41" />
<hkern u1="&#xea;" u2="w" k="41" />
<hkern u1="&#xea;" u2="v" k="41" />
<hkern u1="&#xea;" u2="&#x27;" k="20" />
<hkern u1="&#xea;" u2="&#x22;" k="20" />
<hkern u1="&#xeb;" u2="&#x201d;" k="20" />
<hkern u1="&#xeb;" u2="&#x2019;" k="20" />
<hkern u1="&#xeb;" u2="&#xfd;" k="41" />
<hkern u1="&#xeb;" u2="z" k="20" />
<hkern u1="&#xeb;" u2="y" k="41" />
<hkern u1="&#xeb;" u2="x" k="41" />
<hkern u1="&#xeb;" u2="w" k="41" />
<hkern u1="&#xeb;" u2="v" k="41" />
<hkern u1="&#xeb;" u2="&#x27;" k="20" />
<hkern u1="&#xeb;" u2="&#x22;" k="20" />
<hkern u1="&#xf0;" u2="&#x201d;" k="20" />
<hkern u1="&#xf0;" u2="&#x2019;" k="20" />
<hkern u1="&#xf0;" u2="&#xfd;" k="41" />
<hkern u1="&#xf0;" u2="z" k="20" />
<hkern u1="&#xf0;" u2="y" k="41" />
<hkern u1="&#xf0;" u2="x" k="41" />
<hkern u1="&#xf0;" u2="w" k="41" />
<hkern u1="&#xf0;" u2="v" k="41" />
<hkern u1="&#xf0;" u2="&#x27;" k="20" />
<hkern u1="&#xf0;" u2="&#x22;" k="20" />
<hkern u1="&#xf2;" u2="&#x201d;" k="20" />
<hkern u1="&#xf2;" u2="&#x2019;" k="20" />
<hkern u1="&#xf2;" u2="&#xfd;" k="41" />
<hkern u1="&#xf2;" u2="z" k="20" />
<hkern u1="&#xf2;" u2="y" k="41" />
<hkern u1="&#xf2;" u2="x" k="41" />
<hkern u1="&#xf2;" u2="w" k="41" />
<hkern u1="&#xf2;" u2="v" k="41" />
<hkern u1="&#xf2;" u2="&#x27;" k="20" />
<hkern u1="&#xf2;" u2="&#x22;" k="20" />
<hkern u1="&#xf3;" u2="&#x201d;" k="20" />
<hkern u1="&#xf3;" u2="&#x2019;" k="20" />
<hkern u1="&#xf3;" u2="&#xfd;" k="41" />
<hkern u1="&#xf3;" u2="z" k="20" />
<hkern u1="&#xf3;" u2="y" k="41" />
<hkern u1="&#xf3;" u2="x" k="41" />
<hkern u1="&#xf3;" u2="w" k="41" />
<hkern u1="&#xf3;" u2="v" k="41" />
<hkern u1="&#xf3;" u2="&#x27;" k="20" />
<hkern u1="&#xf3;" u2="&#x22;" k="20" />
<hkern u1="&#xf4;" u2="&#x201d;" k="20" />
<hkern u1="&#xf4;" u2="&#x2019;" k="20" />
<hkern u1="&#xf4;" u2="&#xfd;" k="41" />
<hkern u1="&#xf4;" u2="z" k="20" />
<hkern u1="&#xf4;" u2="y" k="41" />
<hkern u1="&#xf4;" u2="x" k="41" />
<hkern u1="&#xf4;" u2="w" k="41" />
<hkern u1="&#xf4;" u2="v" k="41" />
<hkern u1="&#xf4;" u2="&#x27;" k="20" />
<hkern u1="&#xf4;" u2="&#x22;" k="20" />
<hkern u1="&#xf6;" u2="&#x201d;" k="41" />
<hkern u1="&#xf6;" u2="&#x2019;" k="41" />
<hkern u1="&#xf6;" u2="&#x27;" k="41" />
<hkern u1="&#xf6;" u2="&#x22;" k="41" />
<hkern u1="&#xf8;" u2="&#x201d;" k="20" />
<hkern u1="&#xf8;" u2="&#x2019;" k="20" />
<hkern u1="&#xf8;" u2="&#xfd;" k="41" />
<hkern u1="&#xf8;" u2="z" k="20" />
<hkern u1="&#xf8;" u2="y" k="41" />
<hkern u1="&#xf8;" u2="x" k="41" />
<hkern u1="&#xf8;" u2="w" k="41" />
<hkern u1="&#xf8;" u2="v" k="41" />
<hkern u1="&#xf8;" u2="&#x27;" k="20" />
<hkern u1="&#xf8;" u2="&#x22;" k="20" />
<hkern u1="&#xfd;" u2="&#x201e;" k="82" />
<hkern u1="&#xfd;" u2="&#x201d;" k="-82" />
<hkern u1="&#xfd;" u2="&#x201a;" k="82" />
<hkern u1="&#xfd;" u2="&#x2019;" k="-82" />
<hkern u1="&#xfd;" u2="&#x3f;" k="-41" />
<hkern u1="&#xfd;" u2="&#x2e;" k="82" />
<hkern u1="&#xfd;" u2="&#x2c;" k="82" />
<hkern u1="&#xfd;" u2="&#x27;" k="-82" />
<hkern u1="&#xfd;" u2="&#x22;" k="-82" />
<hkern u1="&#xfe;" u2="&#x201d;" k="20" />
<hkern u1="&#xfe;" u2="&#x2019;" k="20" />
<hkern u1="&#xfe;" u2="&#xfd;" k="41" />
<hkern u1="&#xfe;" u2="z" k="20" />
<hkern u1="&#xfe;" u2="y" k="41" />
<hkern u1="&#xfe;" u2="x" k="41" />
<hkern u1="&#xfe;" u2="w" k="41" />
<hkern u1="&#xfe;" u2="v" k="41" />
<hkern u1="&#xfe;" u2="&#x27;" k="20" />
<hkern u1="&#xfe;" u2="&#x22;" k="20" />
<hkern u1="&#xff;" u2="&#x201e;" k="82" />
<hkern u1="&#xff;" u2="&#x201d;" k="-82" />
<hkern u1="&#xff;" u2="&#x201a;" k="82" />
<hkern u1="&#xff;" u2="&#x2019;" k="-82" />
<hkern u1="&#xff;" u2="&#x3f;" k="-41" />
<hkern u1="&#xff;" u2="&#x2e;" k="82" />
<hkern u1="&#xff;" u2="&#x2c;" k="82" />
<hkern u1="&#xff;" u2="&#x27;" k="-82" />
<hkern u1="&#xff;" u2="&#x22;" k="-82" />
<hkern u1="&#x152;" u2="J" k="-123" />
<hkern u1="&#x178;" u2="&#x201e;" k="123" />
<hkern u1="&#x178;" u2="&#x201a;" k="123" />
<hkern u1="&#x178;" u2="&#x153;" k="102" />
<hkern u1="&#x178;" u2="&#x152;" k="41" />
<hkern u1="&#x178;" u2="&#xfc;" k="61" />
<hkern u1="&#x178;" u2="&#xfb;" k="61" />
<hkern u1="&#x178;" u2="&#xfa;" k="61" />
<hkern u1="&#x178;" u2="&#xf9;" k="61" />
<hkern u1="&#x178;" u2="&#xf8;" k="102" />
<hkern u1="&#x178;" u2="&#xf6;" k="102" />
<hkern u1="&#x178;" u2="&#xf5;" k="102" />
<hkern u1="&#x178;" u2="&#xf4;" k="102" />
<hkern u1="&#x178;" u2="&#xf3;" k="102" />
<hkern u1="&#x178;" u2="&#xf2;" k="102" />
<hkern u1="&#x178;" u2="&#xeb;" k="102" />
<hkern u1="&#x178;" u2="&#xea;" k="102" />
<hkern u1="&#x178;" u2="&#xe9;" k="102" />
<hkern u1="&#x178;" u2="&#xe8;" k="102" />
<hkern u1="&#x178;" u2="&#xe7;" k="102" />
<hkern u1="&#x178;" u2="&#xe6;" k="102" />
<hkern u1="&#x178;" u2="&#xe5;" k="102" />
<hkern u1="&#x178;" u2="&#xe4;" k="102" />
<hkern u1="&#x178;" u2="&#xe3;" k="102" />
<hkern u1="&#x178;" u2="&#xe2;" k="102" />
<hkern u1="&#x178;" u2="&#xe1;" k="102" />
<hkern u1="&#x178;" u2="&#xe0;" k="102" />
<hkern u1="&#x178;" u2="&#xd8;" k="41" />
<hkern u1="&#x178;" u2="&#xd6;" k="41" />
<hkern u1="&#x178;" u2="&#xd5;" k="41" />
<hkern u1="&#x178;" u2="&#xd4;" k="41" />
<hkern u1="&#x178;" u2="&#xd3;" k="41" />
<hkern u1="&#x178;" u2="&#xd2;" k="41" />
<hkern u1="&#x178;" u2="&#xc7;" k="41" />
<hkern u1="&#x178;" u2="&#xc5;" k="123" />
<hkern u1="&#x178;" u2="&#xc4;" k="123" />
<hkern u1="&#x178;" u2="&#xc3;" k="123" />
<hkern u1="&#x178;" u2="&#xc2;" k="123" />
<hkern u1="&#x178;" u2="&#xc1;" k="123" />
<hkern u1="&#x178;" u2="&#xc0;" k="123" />
<hkern u1="&#x178;" u2="z" k="41" />
<hkern u1="&#x178;" u2="u" k="61" />
<hkern u1="&#x178;" u2="s" k="82" />
<hkern u1="&#x178;" u2="r" k="61" />
<hkern u1="&#x178;" u2="q" k="102" />
<hkern u1="&#x178;" u2="p" k="61" />
<hkern u1="&#x178;" u2="o" k="102" />
<hkern u1="&#x178;" u2="n" k="61" />
<hkern u1="&#x178;" u2="m" k="61" />
<hkern u1="&#x178;" u2="g" k="41" />
<hkern u1="&#x178;" u2="e" k="102" />
<hkern u1="&#x178;" u2="d" k="102" />
<hkern u1="&#x178;" u2="c" k="102" />
<hkern u1="&#x178;" u2="a" k="102" />
<hkern u1="&#x178;" u2="Q" k="41" />
<hkern u1="&#x178;" u2="O" k="41" />
<hkern u1="&#x178;" u2="G" k="41" />
<hkern u1="&#x178;" u2="C" k="41" />
<hkern u1="&#x178;" u2="A" k="123" />
<hkern u1="&#x178;" u2="&#x3f;" k="-41" />
<hkern u1="&#x178;" u2="&#x2e;" k="123" />
<hkern u1="&#x178;" u2="&#x2c;" k="123" />
<hkern u1="&#x2013;" u2="T" k="82" />
<hkern u1="&#x2014;" u2="T" k="82" />
<hkern u1="&#x2018;" u2="&#x178;" k="-20" />
<hkern u1="&#x2018;" u2="&#x153;" k="123" />
<hkern u1="&#x2018;" u2="&#xfc;" k="61" />
<hkern u1="&#x2018;" u2="&#xfb;" k="61" />
<hkern u1="&#x2018;" u2="&#xfa;" k="61" />
<hkern u1="&#x2018;" u2="&#xf9;" k="61" />
<hkern u1="&#x2018;" u2="&#xf8;" k="123" />
<hkern u1="&#x2018;" u2="&#xf6;" k="123" />
<hkern u1="&#x2018;" u2="&#xf5;" k="123" />
<hkern u1="&#x2018;" u2="&#xf4;" k="123" />
<hkern u1="&#x2018;" u2="&#xf3;" k="123" />
<hkern u1="&#x2018;" u2="&#xf2;" k="123" />
<hkern u1="&#x2018;" u2="&#xeb;" k="123" />
<hkern u1="&#x2018;" u2="&#xea;" k="123" />
<hkern u1="&#x2018;" u2="&#xe9;" k="123" />
<hkern u1="&#x2018;" u2="&#xe8;" k="123" />
<hkern u1="&#x2018;" u2="&#xe7;" k="123" />
<hkern u1="&#x2018;" u2="&#xe6;" k="82" />
<hkern u1="&#x2018;" u2="&#xe5;" k="82" />
<hkern u1="&#x2018;" u2="&#xe4;" k="82" />
<hkern u1="&#x2018;" u2="&#xe3;" k="82" />
<hkern u1="&#x2018;" u2="&#xe2;" k="82" />
<hkern u1="&#x2018;" u2="&#xe1;" k="82" />
<hkern u1="&#x2018;" u2="&#xe0;" k="123" />
<hkern u1="&#x2018;" u2="&#xdd;" k="-20" />
<hkern u1="&#x2018;" u2="&#xc5;" k="143" />
<hkern u1="&#x2018;" u2="&#xc4;" k="143" />
<hkern u1="&#x2018;" u2="&#xc3;" k="143" />
<hkern u1="&#x2018;" u2="&#xc2;" k="143" />
<hkern u1="&#x2018;" u2="&#xc1;" k="143" />
<hkern u1="&#x2018;" u2="&#xc0;" k="143" />
<hkern u1="&#x2018;" u2="u" k="61" />
<hkern u1="&#x2018;" u2="s" k="61" />
<hkern u1="&#x2018;" u2="r" k="61" />
<hkern u1="&#x2018;" u2="q" k="123" />
<hkern u1="&#x2018;" u2="p" k="61" />
<hkern u1="&#x2018;" u2="o" k="123" />
<hkern u1="&#x2018;" u2="n" k="61" />
<hkern u1="&#x2018;" u2="m" k="61" />
<hkern u1="&#x2018;" u2="g" k="61" />
<hkern u1="&#x2018;" u2="e" k="123" />
<hkern u1="&#x2018;" u2="d" k="123" />
<hkern u1="&#x2018;" u2="c" k="123" />
<hkern u1="&#x2018;" u2="a" k="82" />
<hkern u1="&#x2018;" u2="Y" k="-20" />
<hkern u1="&#x2018;" u2="W" k="-41" />
<hkern u1="&#x2018;" u2="V" k="-41" />
<hkern u1="&#x2018;" u2="T" k="-41" />
<hkern u1="&#x2018;" u2="A" k="143" />
<hkern u1="&#x2019;" u2="&#x178;" k="-20" />
<hkern u1="&#x2019;" u2="&#x153;" k="123" />
<hkern u1="&#x2019;" u2="&#xfc;" k="61" />
<hkern u1="&#x2019;" u2="&#xfb;" k="61" />
<hkern u1="&#x2019;" u2="&#xfa;" k="61" />
<hkern u1="&#x2019;" u2="&#xf9;" k="61" />
<hkern u1="&#x2019;" u2="&#xf8;" k="123" />
<hkern u1="&#x2019;" u2="&#xf6;" k="123" />
<hkern u1="&#x2019;" u2="&#xf5;" k="123" />
<hkern u1="&#x2019;" u2="&#xf4;" k="123" />
<hkern u1="&#x2019;" u2="&#xf3;" k="123" />
<hkern u1="&#x2019;" u2="&#xf2;" k="123" />
<hkern u1="&#x2019;" u2="&#xeb;" k="123" />
<hkern u1="&#x2019;" u2="&#xea;" k="123" />
<hkern u1="&#x2019;" u2="&#xe9;" k="123" />
<hkern u1="&#x2019;" u2="&#xe8;" k="123" />
<hkern u1="&#x2019;" u2="&#xe7;" k="123" />
<hkern u1="&#x2019;" u2="&#xe6;" k="82" />
<hkern u1="&#x2019;" u2="&#xe5;" k="82" />
<hkern u1="&#x2019;" u2="&#xe4;" k="82" />
<hkern u1="&#x2019;" u2="&#xe3;" k="82" />
<hkern u1="&#x2019;" u2="&#xe2;" k="82" />
<hkern u1="&#x2019;" u2="&#xe1;" k="82" />
<hkern u1="&#x2019;" u2="&#xe0;" k="123" />
<hkern u1="&#x2019;" u2="&#xdd;" k="-20" />
<hkern u1="&#x2019;" u2="&#xc5;" k="143" />
<hkern u1="&#x2019;" u2="&#xc4;" k="143" />
<hkern u1="&#x2019;" u2="&#xc3;" k="143" />
<hkern u1="&#x2019;" u2="&#xc2;" k="143" />
<hkern u1="&#x2019;" u2="&#xc1;" k="143" />
<hkern u1="&#x2019;" u2="&#xc0;" k="143" />
<hkern u1="&#x2019;" u2="u" k="61" />
<hkern u1="&#x2019;" u2="s" k="61" />
<hkern u1="&#x2019;" u2="r" k="61" />
<hkern u1="&#x2019;" u2="q" k="123" />
<hkern u1="&#x2019;" u2="p" k="61" />
<hkern u1="&#x2019;" u2="o" k="123" />
<hkern u1="&#x2019;" u2="n" k="61" />
<hkern u1="&#x2019;" u2="m" k="61" />
<hkern u1="&#x2019;" u2="g" k="61" />
<hkern u1="&#x2019;" u2="e" k="123" />
<hkern u1="&#x2019;" u2="d" k="123" />
<hkern u1="&#x2019;" u2="c" k="123" />
<hkern u1="&#x2019;" u2="a" k="82" />
<hkern u1="&#x2019;" u2="Y" k="-20" />
<hkern u1="&#x2019;" u2="W" k="-41" />
<hkern u1="&#x2019;" u2="V" k="-41" />
<hkern u1="&#x2019;" u2="T" k="-41" />
<hkern u1="&#x2019;" u2="A" k="143" />
<hkern u1="&#x201a;" u2="&#x178;" k="123" />
<hkern u1="&#x201a;" u2="&#x152;" k="102" />
<hkern u1="&#x201a;" u2="&#xdd;" k="123" />
<hkern u1="&#x201a;" u2="&#xdc;" k="41" />
<hkern u1="&#x201a;" u2="&#xdb;" k="41" />
<hkern u1="&#x201a;" u2="&#xda;" k="41" />
<hkern u1="&#x201a;" u2="&#xd9;" k="41" />
<hkern u1="&#x201a;" u2="&#xd8;" k="102" />
<hkern u1="&#x201a;" u2="&#xd6;" k="102" />
<hkern u1="&#x201a;" u2="&#xd5;" k="102" />
<hkern u1="&#x201a;" u2="&#xd4;" k="102" />
<hkern u1="&#x201a;" u2="&#xd3;" k="102" />
<hkern u1="&#x201a;" u2="&#xd2;" k="102" />
<hkern u1="&#x201a;" u2="&#xc7;" k="102" />
<hkern u1="&#x201a;" u2="Y" k="123" />
<hkern u1="&#x201a;" u2="W" k="123" />
<hkern u1="&#x201a;" u2="V" k="123" />
<hkern u1="&#x201a;" u2="U" k="41" />
<hkern u1="&#x201a;" u2="T" k="143" />
<hkern u1="&#x201a;" u2="Q" k="102" />
<hkern u1="&#x201a;" u2="O" k="102" />
<hkern u1="&#x201a;" u2="G" k="102" />
<hkern u1="&#x201a;" u2="C" k="102" />
<hkern u1="&#x201c;" u2="&#x178;" k="-20" />
<hkern u1="&#x201c;" u2="&#x153;" k="123" />
<hkern u1="&#x201c;" u2="&#xfc;" k="61" />
<hkern u1="&#x201c;" u2="&#xfb;" k="61" />
<hkern u1="&#x201c;" u2="&#xfa;" k="61" />
<hkern u1="&#x201c;" u2="&#xf9;" k="61" />
<hkern u1="&#x201c;" u2="&#xf8;" k="123" />
<hkern u1="&#x201c;" u2="&#xf6;" k="123" />
<hkern u1="&#x201c;" u2="&#xf5;" k="123" />
<hkern u1="&#x201c;" u2="&#xf4;" k="123" />
<hkern u1="&#x201c;" u2="&#xf3;" k="123" />
<hkern u1="&#x201c;" u2="&#xf2;" k="123" />
<hkern u1="&#x201c;" u2="&#xeb;" k="123" />
<hkern u1="&#x201c;" u2="&#xea;" k="123" />
<hkern u1="&#x201c;" u2="&#xe9;" k="123" />
<hkern u1="&#x201c;" u2="&#xe8;" k="123" />
<hkern u1="&#x201c;" u2="&#xe7;" k="123" />
<hkern u1="&#x201c;" u2="&#xe6;" k="82" />
<hkern u1="&#x201c;" u2="&#xe5;" k="82" />
<hkern u1="&#x201c;" u2="&#xe4;" k="82" />
<hkern u1="&#x201c;" u2="&#xe3;" k="82" />
<hkern u1="&#x201c;" u2="&#xe2;" k="82" />
<hkern u1="&#x201c;" u2="&#xe1;" k="82" />
<hkern u1="&#x201c;" u2="&#xe0;" k="123" />
<hkern u1="&#x201c;" u2="&#xdd;" k="-20" />
<hkern u1="&#x201c;" u2="&#xc5;" k="143" />
<hkern u1="&#x201c;" u2="&#xc4;" k="143" />
<hkern u1="&#x201c;" u2="&#xc3;" k="143" />
<hkern u1="&#x201c;" u2="&#xc2;" k="143" />
<hkern u1="&#x201c;" u2="&#xc1;" k="143" />
<hkern u1="&#x201c;" u2="&#xc0;" k="143" />
<hkern u1="&#x201c;" u2="u" k="61" />
<hkern u1="&#x201c;" u2="s" k="61" />
<hkern u1="&#x201c;" u2="r" k="61" />
<hkern u1="&#x201c;" u2="q" k="123" />
<hkern u1="&#x201c;" u2="p" k="61" />
<hkern u1="&#x201c;" u2="o" k="123" />
<hkern u1="&#x201c;" u2="n" k="61" />
<hkern u1="&#x201c;" u2="m" k="61" />
<hkern u1="&#x201c;" u2="g" k="61" />
<hkern u1="&#x201c;" u2="e" k="123" />
<hkern u1="&#x201c;" u2="d" k="123" />
<hkern u1="&#x201c;" u2="c" k="123" />
<hkern u1="&#x201c;" u2="a" k="82" />
<hkern u1="&#x201c;" u2="Y" k="-20" />
<hkern u1="&#x201c;" u2="W" k="-41" />
<hkern u1="&#x201c;" u2="V" k="-41" />
<hkern u1="&#x201c;" u2="T" k="-41" />
<hkern u1="&#x201c;" u2="A" k="143" />
<hkern u1="&#x201e;" u2="&#x178;" k="123" />
<hkern u1="&#x201e;" u2="&#x152;" k="102" />
<hkern u1="&#x201e;" u2="&#xdd;" k="123" />
<hkern u1="&#x201e;" u2="&#xdc;" k="41" />
<hkern u1="&#x201e;" u2="&#xdb;" k="41" />
<hkern u1="&#x201e;" u2="&#xda;" k="41" />
<hkern u1="&#x201e;" u2="&#xd9;" k="41" />
<hkern u1="&#x201e;" u2="&#xd8;" k="102" />
<hkern u1="&#x201e;" u2="&#xd6;" k="102" />
<hkern u1="&#x201e;" u2="&#xd5;" k="102" />
<hkern u1="&#x201e;" u2="&#xd4;" k="102" />
<hkern u1="&#x201e;" u2="&#xd3;" k="102" />
<hkern u1="&#x201e;" u2="&#xd2;" k="102" />
<hkern u1="&#x201e;" u2="&#xc7;" k="102" />
<hkern u1="&#x201e;" u2="Y" k="123" />
<hkern u1="&#x201e;" u2="W" k="123" />
<hkern u1="&#x201e;" u2="V" k="123" />
<hkern u1="&#x201e;" u2="U" k="41" />
<hkern u1="&#x201e;" u2="T" k="143" />
<hkern u1="&#x201e;" u2="Q" k="102" />
<hkern u1="&#x201e;" u2="O" k="102" />
<hkern u1="&#x201e;" u2="G" k="102" />
<hkern u1="&#x201e;" u2="C" k="102" />
<hkern g1="uniFB00" u2="&#x201d;" k="-123" />
<hkern g1="uniFB00" u2="&#x2019;" k="-123" />
<hkern g1="uniFB00" u2="&#x27;" k="-123" />
<hkern g1="uniFB00" u2="&#x22;" k="-123" />
</font>
</defs></svg> 