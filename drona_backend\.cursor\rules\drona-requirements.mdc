---
description: 
globs: 
alwaysApply: true
---
Give me code for this requiremets and also analysis current codebase against this requirement, do not go out of scope
Authentication & Authorization (Firebase Integration)
Firebase Auth Integration

Middleware to verify Firebase ID tokens for every API request.

Firebase Admin SDK setup for backend communication (e.g., fetching user info, enforcing auth rules).

Role Management

Maintain user roles (superAdmin, collegeAdmin, teacher) in MongoDB.

Middleware/guards to authorize routes based on user roles.

📚 Question Management Module
✅ Admin Capabilities
CRUD for Questions

Routes to create, read, update, and delete questions.

Schema: questionText, options, answer, subject, topic, difficulty, type, createdBy, createdAt.

Bulk Upload

File parsing (CSV/Excel) using multer or similar library.

Validate and sanitize uploaded questions.

Duplicate detection logic (based on text and metadata).

Tagging & Filtering

Endpoints to filter questions by subject, topic, type, difficulty.

Duplicate Prevention

Pre-generation scan to ensure uniqueness.

📄 Question Paper Generation
Paper Creation Endpoint

Input: total marks, difficulty split, section info.

Logic to auto-generate questions based on criteria.

Export

Generate PDF/Word using puppeteer/docx libraries.

Store generated documents in AWS S3.

Return downloadable link.

🏫 College Management
✅ Super Admin Controls
Add/edit/delete college records.

Unique collegeId generation.

College schema: name, address, contact, logoURL.

✅ College Admin Controls
Manage teachers (add/edit/remove).

Link teachers to collegeId.

College-level question generation and download stats.

👥 User Roles & Permissions
Role Enforcement

Role-based guards using NestJS @Roles() decorators.

Permissions middleware to restrict access.

User Model

Firebase UID, email, role, associated collegeId (for admins/teachers), lastLogin.

📊 Analytics & Reporting
✅ Super Admin Dashboard
Aggregate analytics endpoints:

Total questions, most used, top colleges.

Usage timelines (daily/weekly/monthly).

Activity logging (e.g., question generation events).

✅ College Admin Dashboard
Endpoints for:

Teacher activity logs.

Question paper generation and download frequency.

Subject distribution in papers.

🗂️ File Handling & AWS S3
Upload ZIP/Multimedia

Use @nestjs/platform-express with multer-s3 for direct uploads.

Presigned URLs for secure upload/download.

Metadata Tracking

Store S3 URLs with metadata (e.g., user, file type, createdAt).

Clean-up utilities or lifecycle rules for old files (optional).

🧱 MongoDB Models
User: uid, email, role, collegeId, createdAt

College: collegeId, name, address, contact, logoURL

Question: text, options, answer, tags, subject, topic, type, difficulty, createdBy

QuestionPaper: generatedBy, collegeId, questionIds[], downloadLink, createdAt, config

ActivityLog: userId, action, timestamp, details

File: fileType, url, uploadedBy, collegeId, createdAt