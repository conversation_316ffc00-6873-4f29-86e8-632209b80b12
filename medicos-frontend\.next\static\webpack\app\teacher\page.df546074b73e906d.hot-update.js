"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/page",{

/***/ "(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx":
/*!**********************************************************!*\
  !*** ./src/components/teacher/question-paper-wizard.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuestionPaperWizard: () => (/* binding */ QuestionPaperWizard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./steps/question-type-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-type-step.tsx\");\n/* harmony import */ var _steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./steps/course-subject-step */ \"(app-pages-browser)/./src/components/teacher/steps/course-subject-step.tsx\");\n/* harmony import */ var _steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./steps/difficulty-level-step */ \"(app-pages-browser)/./src/components/teacher/steps/difficulty-level-step.tsx\");\n/* harmony import */ var _steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./steps/question-selection-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-selection-step.tsx\");\n/* harmony import */ var _steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./steps/paper-customization-step */ \"(app-pages-browser)/./src/components/teacher/steps/paper-customization-step.tsx\");\n/* harmony import */ var _steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./steps/include-answers-step */ \"(app-pages-browser)/./src/components/teacher/steps/include-answers-step.tsx\");\n/* harmony import */ var _steps_actions_step__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./steps/actions-step */ \"(app-pages-browser)/./src/components/teacher/steps/actions-step.tsx\");\n/* harmony import */ var _ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/step-indicator */ \"(app-pages-browser)/./src/components/teacher/ui/step-indicator.tsx\");\n/* harmony import */ var _steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./steps/question-title-description-step */ \"(app-pages-browser)/./src/components/teacher/steps/question-title-description-step.tsx\");\n/* __next_internal_client_entry_do_not_use__ QuestionPaperWizard auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    questionType: \"\",\n    title: \"\",\n    description: \"\",\n    course: \"\",\n    subject: \"\",\n    difficultyMode: \"auto\",\n    difficultyLevels: {\n        easy: 30,\n        medium: 50,\n        hard: 20\n    },\n    numberOfQuestions: 1,\n    totalMarks: 100,\n    includeAnswers: false,\n    duration: 60,\n    instructions: \"\",\n    topicId: undefined\n};\nfunction QuestionPaperWizard() {\n    _s();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data\n            }));\n    };\n    const nextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const prevStep = ()=>{\n        setCurrentStep((prev)=>Math.max(prev - 1, 0));\n    };\n    const skipStep = ()=>{\n        nextStep();\n    };\n    const goToFirstStep = ()=>{\n        setCurrentStep(0);\n    };\n    const handleSubmit = async ()=>{\n        if (isGenerating) return; // Prevent multiple submissions\n        try {\n            var _formData_title;\n            setIsGenerating(true);\n            console.log(\"Submitting data:\", formData);\n            // Debug: Check available tokens\n            const backendToken = localStorage.getItem(\"backendToken\");\n            const firebaseToken = localStorage.getItem(\"firebaseToken\");\n            const token = localStorage.getItem(\"token\");\n            console.log(\"Available tokens:\", {\n                backendToken: backendToken ? \"\".concat(backendToken.substring(0, 20), \"...\") : 'None',\n                firebaseToken: firebaseToken ? \"\".concat(firebaseToken.substring(0, 20), \"...\") : 'None',\n                token: token ? \"\".concat(token.substring(0, 20), \"...\") : 'None'\n            });\n            // Validate required fields\n            if (!((_formData_title = formData.title) === null || _formData_title === void 0 ? void 0 : _formData_title.trim())) {\n                setIsGenerating(false);\n                alert(\"Please enter a title for the question paper\");\n                return;\n            }\n            if (!formData.questionType) {\n                setIsGenerating(false);\n                alert(\"Please select an exam type\");\n                return;\n            }\n            if (!formData.subject) {\n                setIsGenerating(false);\n                alert(\"Please select a subject\");\n                return;\n            }\n            // Prepare the API payload\n            const apiPayload = {\n                title: formData.title,\n                description: formData.description,\n                subject: formData.subject,\n                totalMarks: formData.totalMarks,\n                duration: formData.duration,\n                examType: formData.questionType,\n                instructions: formData.instructions,\n                topicId: formData.topicId,\n                maxQuestions: formData.numberOfQuestions\n            };\n            // Add customization if not auto mode\n            if (formData.difficultyMode === \"custom\") {\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: formData.difficultyLevels.easy,\n                        mediumPercentage: formData.difficultyLevels.medium,\n                        hardPercentage: formData.difficultyLevels.hard\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            } else {\n                // For auto mode, still include customization with default values\n                apiPayload.customise = {\n                    customDifficulty: {\n                        easyPercentage: 30,\n                        mediumPercentage: 50,\n                        hardPercentage: 20\n                    },\n                    numberOfQuestions: formData.numberOfQuestions,\n                    totalMarks: formData.totalMarks,\n                    duration: formData.duration,\n                    includeAnswers: formData.includeAnswers\n                };\n            }\n            // Create the question paper\n            console.log(\"Calling createQuestionPaper API...\");\n            const result = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.createQuestionPaper)(apiPayload);\n            console.log(\"API result:\", result);\n            // Check if the request was successful\n            if (!result.success) {\n                console.log(\"API returned error:\", result.error);\n                setIsGenerating(false);\n                let errorMessage = result.error;\n                // Handle specific error types with better messages\n                if (errorMessage.includes(\"Authentication required\") || errorMessage.includes(\"Unauthorized\")) {\n                    errorMessage = \"Please log in again to continue. Your session may have expired.\";\n                } else if (errorMessage.includes(\"Network\") || errorMessage.includes(\"fetch\")) {\n                    errorMessage = \"Please check your internet connection and try again.\";\n                } else if (errorMessage.includes(\"unused questions available\")) {\n                    // Extract numbers from the error message for a clearer explanation\n                    const match = errorMessage.match(/Only (\\d+) unused questions available\\. Requested: (\\d+)/);\n                    if (match) {\n                        const available = match[1];\n                        const requested = match[2];\n                        errorMessage = \"Only \".concat(available, \" questions are available for this subject/topic, but you requested \").concat(requested, \" questions. Please reduce the number of questions or add more questions to the database.\");\n                    }\n                }\n                // Show error message in alert\n                alert(\"Error: \".concat(errorMessage));\n                console.log(\"Staying on current step due to error\");\n                return; // Exit early on error\n            }\n            // Success - proceed with download\n            console.log(\"API success! Proceeding with download...\");\n            const questionPaper = result.data;\n            // Download the PDF\n            const pdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_2__.downloadQuestionPaper)(questionPaper._id, 'pdf');\n            // Create download link\n            const url = window.URL.createObjectURL(pdfBlob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"\".concat(formData.title.replace(/\\s+/g, '_'), \"_\").concat(Date.now(), \".pdf\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            alert(\"Question paper generated and downloaded successfully!\");\n            console.log(\"Success! About to redirect to first step in 1 second...\");\n            // Reset to first step and clear form data after a short delay (only on success)\n            setTimeout(()=>{\n                console.log(\"Redirecting to first step now...\");\n                setCurrentStep(0);\n                setFormData(initialFormData);\n                setIsGenerating(false);\n                console.log(\"Redirect completed. Current step should be 0\");\n            }, 1000) // 1 second delay to ensure alert is visible\n            ;\n        } catch (error) {\n            setIsGenerating(false);\n            // Handle any unexpected errors (like network issues)\n            alert(\"Error: An unexpected error occurred. Please try again.\");\n        }\n    };\n    const steps = [\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_type_step__WEBPACK_IMPORTED_MODULE_3__.QuestionTypeStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Type\",\n            icon: \"HelpCircle\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_title_description_step__WEBPACK_IMPORTED_MODULE_11__.QuestionTitleAndDescriptionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 263,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Course & Subject Selection\",\n            icon: \"BookOpen\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_course_subject_step__WEBPACK_IMPORTED_MODULE_4__.CourseSubjectStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Select Difficulty Level\",\n            icon: \"BarChart2\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_difficulty_level_step__WEBPACK_IMPORTED_MODULE_5__.DifficultyLevelStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Question Selection Criteria\",\n            icon: \"FileText\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_question_selection_step__WEBPACK_IMPORTED_MODULE_6__.QuestionSelectionStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 305,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Paper Customization\",\n            icon: \"FileEdit\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_paper_customization_step__WEBPACK_IMPORTED_MODULE_7__.PaperCustomizationStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 319,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Include Answers?\",\n            icon: \"CheckSquare\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_include_answers_step__WEBPACK_IMPORTED_MODULE_8__.IncludeAnswersStep, {\n                formData: formData,\n                updateFormData: updateFormData,\n                onNext: nextStep,\n                onSkip: skipStep,\n                onBack: prevStep,\n                backDisabled: currentStep === 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 333,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            title: \"Actions\",\n            icon: \"FileOutput\",\n            component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_steps_actions_step__WEBPACK_IMPORTED_MODULE_9__.ActionsStep, {\n                formData: formData,\n                onSubmit: handleSubmit,\n                isLoading: isGenerating,\n                onBack: goToFirstStep\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 346,\n                columnNumber: 18\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_step_indicator__WEBPACK_IMPORTED_MODULE_10__.StepIndicator, {\n                currentStep: currentStep,\n                steps: steps.map((step)=>({\n                        title: step.title,\n                        icon: step.icon\n                    }))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this),\n            steps[currentStep].component\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\teacher\\\\question-paper-wizard.tsx\",\n        lineNumber: 353,\n        columnNumber: 5\n    }, this);\n}\n_s(QuestionPaperWizard, \"nel0TECL1Zv7CzoM26K8Hca8GSI=\");\n_c = QuestionPaperWizard;\nvar _c;\n$RefreshReg$(_c, \"QuestionPaperWizard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/teacher/question-paper-wizard.tsx\n"));

/***/ })

});