import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { MistralAiService } from './mistral-ai.service';
import { Question, QuestionSchema } from '../schema/question.schema';
import { ImageCompressionService } from '../common/services/image-compression.service';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: Question.name, schema: QuestionSchema },
    ]),
  ],
  providers: [MistralAiService, ImageCompressionService],
  exports: [MistralAiService],
})
export class MistralAiModule {}
