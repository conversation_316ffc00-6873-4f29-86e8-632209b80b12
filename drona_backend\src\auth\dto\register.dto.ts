// auth/dto/register.dto.ts
import {
  IsNotEmpty,
  IsString,
  IsEmail,
  MinLength,
  IsOptional,
  IsEnum,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Available user roles in the system
 */
export enum UserRole {
  SUPER_ADMIN = 'superAdmin',
  COLLEGE_ADMIN = 'collegeAdmin',
  TEACHER = 'teacher',
}

/**
 * DTO for user registration
 */
export class RegisterDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User display name',
    example: '<PERSON>',
  })
  @IsNotEmpty()
  @IsString()
  displayName: string;

  @ApiPropertyOptional({
    description: 'User first name',
    example: 'John',
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({
    description: 'User last name',
    example: '<PERSON>e',
  })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({
    description: 'User password',
    example: 'password123',
    minLength: 6,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: 'User role',
    example: 'teacher',
    enum: UserRole,
    enumName: 'UserRole',
  })
  @IsNotEmpty()
  @IsString()
  @IsEnum(UserRole)
  role: string;

  @ApiPropertyOptional({
    description: 'College ID (required for collegeAdmin and teacher roles)',
    example: '60d21b4667d0d8992e610c85',
  })
  @IsOptional()
  @IsString()
  collegeId?: string;
}
