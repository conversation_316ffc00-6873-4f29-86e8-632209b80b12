// auth/dto/login.dto.ts
import { IsNotEmpty, IsString, IsOptional, IsEmail } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for user login
 * Either email/password combination or Firebase token must be provided
 */
export class LoginDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsEmail()
  email?: string;

  @ApiProperty({
    description: 'User password',
    example: 'password123',
    required: false,
    minLength: 6,
  })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    description: 'Firebase authentication token',
    example: 'eyJhbGciOiJSUzI1NiIsImtpZCI6IjFlOWdkazcifQ...',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  firebaseToken?: string;
}
