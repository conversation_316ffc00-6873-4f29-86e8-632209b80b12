import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Question } from '../schema/question.schema';
import { ImageCompressionService } from '../common/services/image-compression.service';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

interface MistralOCRResponse {
  id: string;
  object: string;
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
  content: string;
  images?: Array<{
    bbox: number[];
    base64: string;
  }>;
}

interface ParsedQuestion {
  content: string;
  options: string[];
  answer: string;
  imageUrls?: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  type: string;
}

interface BulkUploadResult {
  questionsAdded: number;
  questionsFailed: number;
  questions: Question[];
  errors: string[];
}

@Injectable()
export class MistralAiService {
  private readonly logger = new Logger(MistralAiService.name);

  constructor(
    private readonly configService: ConfigService,
    @InjectModel(Question.name) private questionModel: Model<Question>,
    private readonly imageCompressionService: ImageCompressionService,
  ) {}

  async processPdfWithOCR(
    file: Express.Multer.File,
  ): Promise<MistralOCRResponse> {
    try {
      const mistralApiKey = this.configService.get<string>('MISTRAL_API_KEY');
      if (!mistralApiKey) {
        throw new BadRequestException('Mistral API key not configured');
      }

      // Convert file buffer to base64
      const base64Pdf = file.buffer.toString('base64');

      // Call Mistral OCR API
      const response = await fetch('https://api.mistral.ai/v1/ocr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${mistralApiKey}`,
        },
        body: JSON.stringify({
          model: 'mistral-ocr-latest',
          document: {
            type: 'document_url',
            document_url: `data:application/pdf;base64,${base64Pdf}`,
          },
          include_image_base64: true,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error(
          `Mistral OCR API error: ${response.status} - ${errorText}`,
        );
        throw new BadRequestException(
          `OCR processing failed: ${response.statusText}`,
        );
      }

      const ocrResult: MistralOCRResponse = await response.json();
      this.logger.log(
        `OCR processing completed. Content length: ${ocrResult.content?.length || 0}`,
      );

      return ocrResult;
    } catch (error) {
      this.logger.error(
        `Error processing PDF with OCR: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async parseQuestionsFromOCR(
    ocrResult: MistralOCRResponse,
  ): Promise<ParsedQuestion[]> {
    try {
      const content = ocrResult.content;
      const images = ocrResult.images || [];

      // Parse questions from OCR content using regex patterns
      const questions = this.extractQuestionsFromText(content);

      // Compress and save images locally
      const imageUrls = await this.compressAndSaveImages(images);

      // Associate images with questions (simple approach - distribute images among questions)
      const questionsWithImages = this.associateImagesWithQuestions(
        questions,
        imageUrls,
      );

      return questionsWithImages;
    } catch (error) {
      this.logger.error(
        `Error parsing questions from OCR: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private extractQuestionsFromText(content: string): ParsedQuestion[] {
    const questions: ParsedQuestion[] = [];

    // Split content by question patterns (e.g., "Q1.", "Question 1:", "1.", etc.)
    const questionBlocks = content.split(
      /(?:^|\n)(?:Q\.?\s*\d+\.?|Question\s+\d+\.?|\d+\.)\s*/i,
    );

    for (let i = 1; i < questionBlocks.length; i++) {
      // Skip first empty block
      const block = questionBlocks[i].trim();
      if (!block) continue;

      try {
        const parsedQuestion = this.parseQuestionBlock(block);
        if (parsedQuestion) {
          questions.push(parsedQuestion);
        }
      } catch (error) {
        this.logger.warn(`Failed to parse question block: ${error.message}`);
      }
    }

    return questions;
  }

  private parseQuestionBlock(block: string): ParsedQuestion | null {
    // Extract question content (everything before options)
    const optionPattern = /(?:^|\n)\s*(?:[A-D]\.?|\([A-D]\))\s+/i;
    const optionMatch = block.search(optionPattern);

    if (optionMatch === -1) {
      return null; // No options found
    }

    const questionContent = block.substring(0, optionMatch).trim();
    const optionsText = block.substring(optionMatch);

    // Extract options
    const options = this.extractOptions(optionsText);
    if (options.length < 2) {
      return null; // Need at least 2 options
    }

    // Extract answer (look for patterns like "Answer: A" or "Ans: A")
    const answerPattern = /(?:Answer|Ans)\.?\s*:?\s*([A-D])/i;
    const answerMatch = block.match(answerPattern);
    const answerLetter = answerMatch ? answerMatch[1].toUpperCase() : 'A';
    const answerIndex = answerLetter.charCodeAt(0) - 'A'.charCodeAt(0);
    const answer = options[answerIndex] || options[0];

    return {
      content: questionContent,
      options,
      answer,
      difficulty: 'medium', // Default difficulty
      type: 'multiple-choice',
    };
  }

  private extractOptions(optionsText: string): string[] {
    const options: string[] = [];
    const optionLines = optionsText.split(/\n/);

    for (const line of optionLines) {
      const match = line.match(/^\s*(?:[A-D]\.?|\([A-D]\))\s+(.+)/i);
      if (match) {
        options.push(match[1].trim());
      }
    }

    return options;
  }

  private async compressAndSaveImages(
    images: Array<{ bbox: number[]; base64: string }>,
  ): Promise<string[]> {
    const imageUrls: string[] = [];

    for (const [index, image] of images.entries()) {
      try {
        // Compress and save the image using the image compression service
        const result = await this.imageCompressionService.compressBase64Image(
          image.base64,
          {
            maxSizeBytes: 2 * 1024 * 1024, // 2MB max compression target
            quality: 85,
            format: 'jpeg',
          },
        );

        imageUrls.push(result.url);
        this.logger.log(
          `Compressed and saved image: ${result.filename} (${result.compressionRatio}% reduction)`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to compress and save image ${index}: ${error.message}`,
        );
      }
    }

    return imageUrls;
  }

  private associateImagesWithQuestions(
    questions: ParsedQuestion[],
    imageUrls: string[],
  ): ParsedQuestion[] {
    // Simple distribution: assign images to questions in order
    const questionsWithImages = questions.map((question, index) => {
      const assignedImages = imageUrls.slice(
        Math.floor((index * imageUrls.length) / questions.length),
        Math.floor(((index + 1) * imageUrls.length) / questions.length),
      );

      return {
        ...question,
        imageUrls: assignedImages.length > 0 ? assignedImages : undefined,
      };
    });

    return questionsWithImages;
  }

  async bulkCreateQuestions(
    parsedQuestions: ParsedQuestion[],
    subjectId: string,
    userId: string,
  ): Promise<BulkUploadResult> {
    const result: BulkUploadResult = {
      questionsAdded: 0,
      questionsFailed: 0,
      questions: [],
      errors: [],
    };

    for (const parsedQuestion of parsedQuestions) {
      try {
        const questionData = {
          content: parsedQuestion.content,
          options: parsedQuestion.options,
          answer: parsedQuestion.answer,
          imageUrls: parsedQuestion.imageUrls || [],
          subjectId,
          difficulty: parsedQuestion.difficulty,
          type: parsedQuestion.type,
          createdBy: userId,
          reviewStatus: 'pending', // Mark as in-review
          status: 'inactive',
          source: 'pdf-ocr',
        };

        const createdQuestion = new this.questionModel(questionData);
        const savedQuestion = await createdQuestion.save();

        result.questions.push(savedQuestion);
        result.questionsAdded++;

        this.logger.log(`Created question: ${savedQuestion._id}`);
      } catch (error) {
        result.questionsFailed++;
        result.errors.push(`Failed to create question: ${error.message}`);
        this.logger.error(`Failed to create question: ${error.message}`);
      }
    }

    return result;
  }
}
