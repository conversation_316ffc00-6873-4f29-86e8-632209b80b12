import { Controller, Get, UseGuards, Query } from '@nestjs/common';
import { AnalyticsService } from './analytics.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/guards/roles.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiQuery,
} from '@nestjs/swagger';

/**
 * Controller for platform-wide analytics
 * Provides endpoints for super admins to view analytics data across the entire platform
 */
@ApiTags('Analytics - Super Admin')
@ApiBearerAuth('JWT-auth')
@Controller('analytics')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('superAdmin')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  /**
   * Get a summary of platform-wide statistics
   * @returns Platform summary data
   */
  @Get('platform-summary')
  @ApiOperation({
    summary: 'Get platform summary',
    description: 'Returns a summary of platform statistics',
  })
  @ApiOkResponse({
    description: 'Returns platform summary data',
    schema: {
      type: 'object',
      properties: {
        totalColleges: { type: 'number', example: 25 },
        activeColleges: { type: 'number', example: 23 },
        totalTeachers: { type: 'number', example: 450 },
        activeTeachers: { type: 'number', example: 420 },
        totalQuestionPapers: { type: 'number', example: 1560 },
        totalQuestions: { type: 'number', example: 23400 },
        totalDownloads: { type: 'number', example: 3200 },
        activeUsersLast30Days: { type: 'number', example: 380 },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  getPlatformSummary() {
    return this.analyticsService.getPlatformSummary();
  }

  /**
   * Get statistics for top performing colleges
   * @returns Top colleges data
   */
  @Get('top-colleges')
  @ApiOperation({
    summary: 'Get top colleges',
    description: 'Returns statistics for the top performing colleges',
  })
  @ApiOkResponse({
    description: 'Returns top colleges data',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
          collegeName: { type: 'string', example: 'Harvard University' },
          teacherCount: { type: 'number', example: 45 },
          questionPaperCount: { type: 'number', example: 156 },
          questionCount: { type: 'number', example: 2340 },
          downloadCount: { type: 'number', example: 320 },
          activeTeacherPercentage: { type: 'number', example: 93.3 },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  getTopColleges() {
    return this.analyticsService.getTopColleges();
  }

  /**
   * Get question usage statistics
   * Returns question usage statistics including:
   * - Questions that have been used in papers (each question is counted only once)
   * - Questions that have never been used in papers
   * - Distribution of questions by difficulty
   * - Distribution of questions by type
   * @returns Question usage statistics
   */
  @Get('questions/usage')
  @ApiOperation({
    summary: 'Get question usage statistics',
    description:
      'Returns question usage statistics including questions used in papers, unused questions, and distribution by difficulty and type',
  })
  @ApiOkResponse({
    description: 'Returns question usage statistics',
    schema: {
      type: 'object',
      properties: {
        totalQuestions: { type: 'number', example: 23400 },
        usedQuestions: { type: 'number', example: 18560 },
        unusedQuestions: { type: 'number', example: 4840 },
        difficultyDistribution: {
          type: 'object',
          properties: {
            easy: { type: 'number', example: 7800 },
            medium: { type: 'number', example: 9360 },
            hard: { type: 'number', example: 6240 },
          },
        },
        typeDistribution: {
          type: 'object',
          properties: {
            'multiple-choice': { type: 'number', example: 15210 },
            'true-false': { type: 'number', example: 4680 },
            'fill-in-the-blank': { type: 'number', example: 3510 },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  getQuestionUsage() {
    return this.analyticsService.getQuestionUsage();
  }

  /**
   * Get detailed statistics about questions
   * @returns Question statistics
   */
  @Get('questions/stats')
  @ApiOperation({
    summary: 'Get question statistics',
    description: 'Returns detailed statistics about questions',
  })
  @ApiOkResponse({
    description: 'Returns question statistics',
    schema: {
      type: 'object',
      properties: {
        totalQuestions: { type: 'number', example: 23400 },
        approvedQuestions: { type: 'number', example: 21060 },
        pendingQuestions: { type: 'number', example: 1170 },
        rejectedQuestions: { type: 'number', example: 1170 },
        questionsByCollege: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              collegeId: {
                type: 'string',
                example: '60d21b4667d0d8992e610c85',
              },
              collegeName: { type: 'string', example: 'Harvard University' },
              questionCount: { type: 'number', example: 2340 },
            },
          },
        },
        questionsBySubject: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              subjectId: {
                type: 'string',
                example: '60d21b4667d0d8992e610c85',
              },
              subjectName: { type: 'string', example: 'Mathematics' },
              questionCount: { type: 'number', example: 5850 },
            },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  getQuestionStats() {
    return this.analyticsService.getQuestionStats();
  }

  /**
   * Get comprehensive college-wise analytics including questions, usage, and activity
   * @param startDate Start date for filtering (optional)
   * @param endDate End date for filtering (optional)
   * @param limit Number of colleges to return (optional, default: 50)
   * @param sortBy Sort criteria (optional, default: 'totalActivity')
   * @returns College-wise analytics data
   */
  @Get('colleges/analytics')
  @ApiOperation({
    summary: 'Get college-wise analytics',
    description:
      'Returns comprehensive analytics for each college including questions, usage, downloads, and activity metrics',
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date for filtering (ISO string)',
    required: false,
    example: '2024-01-01T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date for filtering (ISO string)',
    required: false,
    example: '2024-12-31T23:59:59.999Z',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Number of colleges to return',
    required: false,
    example: 50,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Sort criteria',
    required: false,
    enum: [
      'totalActivity',
      'questionCount',
      'downloadCount',
      'teacherCount',
      'paperCount',
    ],
    example: 'totalActivity',
  })
  @ApiOkResponse({
    description: 'Returns college-wise analytics data',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            totalColleges: { type: 'number', example: 25 },
            totalTeachers: { type: 'number', example: 450 },
            totalQuestions: { type: 'number', example: 23400 },
            totalPapers: { type: 'number', example: 1560 },
            totalDownloads: { type: 'number', example: 3200 },
          },
        },
        colleges: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              collegeId: {
                type: 'string',
                example: '60d21b4667d0d8992e610c85',
              },
              collegeName: { type: 'string', example: 'Harvard University' },
              status: { type: 'string', example: 'active' },
              metrics: {
                type: 'object',
                properties: {
                  teacherCount: { type: 'number', example: 45 },
                  activeTeachers: { type: 'number', example: 42 },
                  questionCount: { type: 'number', example: 2340 },
                  approvedQuestions: { type: 'number', example: 2106 },
                  pendingQuestions: { type: 'number', example: 117 },
                  rejectedQuestions: { type: 'number', example: 117 },
                  paperCount: { type: 'number', example: 156 },
                  downloadCount: { type: 'number', example: 320 },
                  totalActivity: { type: 'number', example: 2822 },
                },
              },
              subjectBreakdown: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    subjectId: {
                      type: 'string',
                      example: '60d21b4667d0d8992e610c86',
                    },
                    subjectName: { type: 'string', example: 'Mathematics' },
                    questionCount: { type: 'number', example: 585 },
                    paperCount: { type: 'number', example: 39 },
                    downloadCount: { type: 'number', example: 80 },
                  },
                },
              },
              recentActivity: {
                type: 'object',
                properties: {
                  last30Days: {
                    type: 'object',
                    properties: {
                      questionsCreated: { type: 'number', example: 45 },
                      papersGenerated: { type: 'number', example: 12 },
                      downloads: { type: 'number', example: 67 },
                      activeTeachers: { type: 'number', example: 38 },
                    },
                  },
                },
              },
            },
          },
        },
        filters: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time', nullable: true },
            endDate: { type: 'string', format: 'date-time', nullable: true },
            limit: { type: 'number', example: 50 },
            sortBy: { type: 'string', example: 'totalActivity' },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  getCollegeAnalytics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('limit') limit?: string,
    @Query('sortBy') sortBy?: string,
  ) {
    const filters: any = {};

    if (startDate) {
      filters.startDate = new Date(startDate);
    }
    if (endDate) {
      filters.endDate = new Date(endDate);
    }
    if (limit) {
      filters.limit = parseInt(limit, 10);
    }
    if (sortBy) {
      filters.sortBy = sortBy;
    }

    return this.analyticsService.getCollegeAnalytics(filters);
  }

  /**
   * Get monthly usage trends for dashboard bar chart
   * @param year Year to filter by (optional)
   * @param startDate Start date for custom range (optional)
   * @param endDate End date for custom range (optional)
   * @returns Monthly usage trends data
   */
  @Get('usage-trends')
  @ApiOperation({
    summary: 'Get usage trends',
    description:
      'Returns monthly usage data for questions created and papers generated (bar chart data)',
  })
  @ApiQuery({
    name: 'year',
    description: 'Year to filter by (e.g., 2024)',
    required: false,
    example: 2024,
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date for custom range (ISO string)',
    required: false,
    example: '2024-02-05T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date for custom range (ISO string)',
    required: false,
    example: '2024-03-06T23:59:59.999Z',
  })
  @ApiOkResponse({
    description: 'Returns monthly usage trends data',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              month: { type: 'string', example: '2024-01' },
              monthName: { type: 'string', example: 'January 2024' },
              questionsCreated: { type: 'number', example: 1250 },
              papersGenerated: { type: 'number', example: 340 },
              totalUsage: { type: 'number', example: 1590 },
            },
          },
        },
        summary: {
          type: 'object',
          properties: {
            totalMonths: { type: 'number', example: 12 },
            totalQuestionsCreated: { type: 'number', example: 15000 },
            totalPapersGenerated: { type: 'number', example: 4080 },
            averageMonthlyQuestions: { type: 'number', example: 1250 },
            averageMonthlyPapers: { type: 'number', example: 340 },
          },
        },
        dateRange: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  getUsageTrends(
    @Query('year') year?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const filters: any = {};

    if (year) {
      filters.year = parseInt(year, 10);
    }
    if (startDate) {
      filters.startDate = new Date(startDate);
    }
    if (endDate) {
      filters.endDate = new Date(endDate);
    }

    return this.analyticsService.getUsageTrends(filters);
  }

  /**
   * Get monthly college growth data for dashboard line chart
   * @param year Year to filter by (optional)
   * @param startDate Start date for custom range (optional)
   * @param endDate End date for custom range (optional)
   * @param view Data view type (optional, default: 'overview')
   * @returns Monthly college growth data
   */
  @Get('college-growth')
  @ApiOperation({
    summary: 'Get college growth trends',
    description:
      'Returns monthly college growth data with target comparisons (line chart data)',
  })
  @ApiQuery({
    name: 'year',
    description: 'Year to filter by (e.g., 2024)',
    required: false,
    example: 2024,
  })
  @ApiQuery({
    name: 'startDate',
    description: 'Start date for custom range (ISO string)',
    required: false,
    example: '2024-02-05T00:00:00.000Z',
  })
  @ApiQuery({
    name: 'endDate',
    description: 'End date for custom range (ISO string)',
    required: false,
    example: '2024-03-06T23:59:59.999Z',
  })
  @ApiQuery({
    name: 'view',
    description: 'Data view type',
    required: false,
    enum: ['overview', 'sales', 'revenue'],
    example: 'overview',
  })
  @ApiOkResponse({
    description: 'Returns monthly college growth data',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              month: { type: 'string', example: '2024-01' },
              monthName: { type: 'string', example: 'January 2024' },
              collegesAdded: { type: 'number', example: 5 },
              cumulativeColleges: { type: 'number', example: 25 },
              monthlyTarget: { type: 'number', example: 6 },
              targetAchievement: { type: 'number', example: 83.33 },
              revenue: { type: 'number', example: 25000 },
              salesMetrics: {
                type: 'object',
                properties: {
                  conversions: { type: 'number', example: 5 },
                  leads: { type: 'number', example: 12 },
                  conversionRate: { type: 'number', example: 41.67 },
                },
              },
            },
          },
        },
        summary: {
          type: 'object',
          properties: {
            totalMonths: { type: 'number', example: 12 },
            totalCollegesAdded: { type: 'number', example: 60 },
            averageMonthlyGrowth: { type: 'number', example: 5 },
            totalTargetAchievement: { type: 'number', example: 85.5 },
            totalRevenue: { type: 'number', example: 300000 },
          },
        },
        targets: {
          type: 'object',
          properties: {
            monthlyTarget: { type: 'number', example: 6 },
            yearlyTarget: { type: 'number', example: 72 },
            currentProgress: { type: 'number', example: 83.33 },
          },
        },
        dateRange: {
          type: 'object',
          properties: {
            startDate: { type: 'string', format: 'date-time' },
            endDate: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
  })
  @ApiUnauthorizedResponse({ description: 'Unauthorized - Invalid token' })
  @ApiForbiddenResponse({ description: 'Forbidden - Insufficient permissions' })
  getCollegeGrowth(
    @Query('year') year?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('view') view?: string,
  ) {
    const filters: any = {};

    if (year) {
      filters.year = parseInt(year, 10);
    }
    if (startDate) {
      filters.startDate = new Date(startDate);
    }
    if (endDate) {
      filters.endDate = new Date(endDate);
    }
    if (view) {
      filters.view = view;
    }

    return this.analyticsService.getCollegeGrowth(filters);
  }
}
