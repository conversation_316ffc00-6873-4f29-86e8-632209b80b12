// question.schema.ts
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { Subject } from './subject.schema';
import { Topic } from './topic.schema';
import { User } from './user.schema';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export type QuestionDocument = Question & Document;

@Schema({ timestamps: true })
export class Question {
  @ApiProperty({
    description: 'Question content/text',
    example: 'What is the capital of France?',
    required: true,
  })
  @Prop({ required: true })
  content: string;

  @ApiProperty({
    description: 'Answer options for the question',
    example: ['Paris', 'London', 'Berlin', 'Madrid'],
    type: [String],
    required: true,
  })
  @Prop({ type: [String], required: true })
  options: string[];

  @ApiProperty({
    description: 'Correct answer to the question',
    example: 'Paris',
    required: true,
  })
  @Prop({ required: true })
  answer: string;

  @ApiPropertyOptional({
    description: 'URLs to images associated with this question',
    example: [
      'https://s3.amazonaws.com/bucket-name/questions/images/image1.jpg',
    ],
    type: [String],
  })
  @Prop({ type: [String], default: [] })
  imageUrls?: string[];

  @ApiProperty({
    description: 'Subject ID this question belongs to',
    example: '60d21b4667d0d8992e610c85',
    type: String,
    required: true,
  })
  @Prop({ type: MongooseSchema.Types.ObjectId, required: true, ref: 'Subject' })
  subjectId: MongooseSchema.Types.ObjectId;

  @ApiPropertyOptional({
    description: 'Topic ID this question belongs to',
    example: '60d21b4667d0d8992e610c86',
    type: String,
  })
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Topic' })
  topicId?: MongooseSchema.Types.ObjectId;

  @ApiProperty({
    description: 'Question difficulty level',
    example: 'medium',
    enum: ['easy', 'medium', 'hard'],
    required: true,
  })
  @Prop({ required: true, enum: ['easy', 'medium', 'hard'] })
  difficulty: string;

  @ApiProperty({
    description: 'Question type (e.g., multiple-choice, true/false)',
    example: 'multiple-choice',
    required: true,
  })
  @Prop({ required: true })
  type: string;

  @ApiProperty({
    description: 'User ID who created this question',
    example: '60d21b4667d0d8992e610c87',
    type: String,
    required: true,
  })
  @Prop({ type: MongooseSchema.Types.ObjectId, required: true, ref: 'User' })
  createdBy: MongooseSchema.Types.ObjectId;

  @ApiProperty({
    description: 'Question status',
    example: 'active',
    enum: ['active', 'inactive'],
    default: 'inactive',
  })
  @Prop({ default: 'inactive', enum: ['active', 'inactive'] })
  status: string;

  @ApiProperty({
    description: 'Question review status',
    example: 'pending',
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
  })
  @Prop({ default: 'pending', enum: ['pending', 'approved', 'rejected'] })
  reviewStatus: string;

  @ApiPropertyOptional({
    description: 'User ID who reviewed this question',
    example: '60d21b4667d0d8992e610c89',
    type: String,
  })
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'User' })
  reviewedBy: MongooseSchema.Types.ObjectId;

  @ApiPropertyOptional({
    description: 'Date when the question was reviewed',
    example: '2023-07-21T15:30:00.000Z',
  })
  @Prop()
  reviewDate: Date;

  @ApiPropertyOptional({
    description: 'Notes from the reviewer',
    example: 'Approved with minor edits to option B',
  })
  @Prop()
  reviewNotes: string;

  @ApiPropertyOptional({
    description: 'Source of the question (e.g., "pdf-upload", "manual-entry")',
    example: 'pdf-upload',
  })
  @Prop()
  source?: string;
}

export const QuestionSchema = SchemaFactory.createForClass(Question);
