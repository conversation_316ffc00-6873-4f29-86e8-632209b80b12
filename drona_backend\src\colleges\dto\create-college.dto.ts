import { IsString, <PERSON>NotEmpty, <PERSON>Optional, IsEmail } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO for creating a new college
 */
export class CreateCollegeDto {
  @ApiProperty({
    description: 'College name',
    example: 'Harvard University',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'College address',
    example: '123 Main St, Cambridge, MA 02138',
  })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiPropertyOptional({
    description: 'City',
    example: 'Cambridge',
  })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiPropertyOptional({
    description: 'State/Province',
    example: 'Massachusetts',
  })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'USA',
  })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiPropertyOptional({
    description: 'Postal/ZIP code',
    example: '02138',
  })
  @IsString()
  @IsOptional()
  postalCode?: string;

  @ApiProperty({
    description: 'College contact phone number',
    example: '+****************',
  })
  @IsString()
  @IsNotEmpty()
  contactPhone: string;

  @ApiPropertyOptional({
    description: 'College email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsOptional()
  contactEmail?: string;

  @ApiPropertyOptional({
    description: 'College website URL',
    example: 'https://www.harvard.edu',
  })
  @IsString()
  @IsOptional()
  website?: string;

  @ApiPropertyOptional({
    description: 'College logo URL',
    example: 'https://example.com/logo.png',
  })
  @IsString()
  @IsOptional()
  logoUrl?: string;

  @ApiPropertyOptional({
    description: 'Array of college admin email addresses (optional - contactEmail will be automatically added)',
    example: ['<EMAIL>', '<EMAIL>'],
    type: [String],
  })
  @IsOptional()
  admins?: string[];
}
