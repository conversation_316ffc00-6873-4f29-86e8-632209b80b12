import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { QuestionUsageService } from './question-usage.service';
import {
  QuestionUsage,
  QuestionUsageSchema,
} from '../schema/question-usage.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: QuestionUsage.name, schema: QuestionUsageSchema },
    ]),
  ],
  providers: [QuestionUsageService],
  exports: [QuestionUsageService],
})
export class QuestionUsageModule {}
