"use client";

import React, { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import StatCard from "@/components/StatCard";
import { HelpCircle, Users, UserCheck, FileText, FileQuestion, Download } from "lucide-react";
import SubjectQuestionsChart from "@/components/SubjectQuestionsChart";
import TopTeachersList from "@/components/TopTeachersList";
import { getCollegeAnalytics } from "@/lib/api/college";

export default function DashboardPage() {
  const [collegeId, setCollegeId] = useState<string | null>(null);
  // Sample chart data
  const chartData = [
    { name: "Jan", Math: 1200, Physics: 1800, Chemistry: 600 },
    { name: "Feb", Math: 200, Physics: 800, Chemistry: 1400 },
    { name: "Mar", Math: 750, Physics: 300, Chemistry: 650 },
    { name: "Apr", Math: 400, Physics: 50, Chemistry: 800 },
    { name: "May", Math: 600, Physics: 1100, Chemistry: 300 },
    { name: "<PERSON>", Math: 800, Physics: 1500, Chemistry: 600 },
    { name: "Jul", Math: 200, Physics: 400, Chemistry: 70 }
  ];

  // Sample teachers data
  const teachers = [
    { 
      id: "1", 
      name: "David Hello", 
      avatar: "https://i.pravatar.cc/150?img=1", 
      status: "online" as "online" | "offline" | "away" | undefined
    },
    { 
      id: "2", 
      name: "Henry Fisher", 
      avatar: "https://i.pravatar.cc/150?img=2",
      status: "offline" as "online" | "offline" | "away" | undefined
    },
    { 
      id: "3", 
      name: "William Smith", 
      avatar: "https://i.pravatar.cc/150?img=3",
      status: "online" as const
    },
    { 
      id: "4", 
      name: "Willsasaiam Smith", 
      avatar: "https://i.pravatar.cc/150?img=3",
      status: "online" as const
    }
  ];

  // Subject colors
  const subjectColors = [
    { subject: "Math", color: "#4F46E5" },
    { subject: "Physics", color: "#10B981" },
    { subject: "Chemistry", color: "#F59E0B" },
    { subject: "Biology", color: "#EC4899" }
  ];

    // Add this useEffect to get collegeId from multiple sources
    useEffect(() => {
      // Try to get collegeId from localStorage
      const storedCollegeId = localStorage.getItem('collegeId');
      
      if (storedCollegeId) {
        console.log("Found collegeId in localStorage:", storedCollegeId);
        setCollegeId(storedCollegeId);
        return;
      }
      
      // If not found in localStorage, try to extract from JWT token
      try {
        const possibleTokenKeys = ['token', 'backendToken', 'authToken', 'jwtToken'];
        
        for (const key of possibleTokenKeys) {
          const token = localStorage.getItem(key);
          if (token) {
            try {
              const parts = token.split('.');
              if (parts.length === 3) {
                const payload = JSON.parse(atob(parts[1]));
                console.log("JWT payload:", payload);
                
                if (payload.collegeId) {
                  console.log(`Found collegeId in ${key}:`, payload.collegeId);
                  setCollegeId(payload.collegeId);
                  // Store it in localStorage for future use
                  localStorage.setItem('collegeId', payload.collegeId);
                  return;
                }
              }
            } catch (e) {
              console.error(`Error parsing token from ${key}:`, e);
            }
          }
        }
        
        // Log all localStorage keys for debugging
        console.log("All localStorage keys:", Object.keys(localStorage));
        console.error("Could not find collegeId in any token or localStorage");
      } catch (error) {
        console.error('Error getting collegeId:', error);
      }
    }, []);

  const handleTeacherClick = (teacher: { id: string; name: string; avatar: string; status?: "online" | "offline" | "away" }) => {
    if (teacher.status === "online" || teacher.status === "offline") {
      console.log("Clicked on teacher:", teacher.name);
    } else {
      console.warn("Teacher status is not clickable:", teacher.status ?? "unknown");
    }
  };

  // TODO: Replace with actual collegeId source if needed
  // const collegeId = "YOUR_COLLEGE_ID_HERE";
  const [summary, setSummary] = useState<any>(null);
  const [summaryLoading, setSummaryLoading] = useState(false);
  const [summaryError, setSummaryError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSummary = async () => {
      if (!collegeId) return;
      setSummaryLoading(true);
      setSummaryError(null);
      try {
        const data = await getCollegeAnalytics(collegeId);
        setSummary(data);
      } catch (err: any) {
        setSummaryError(err.message || 'Failed to load summary');
      } finally {
        setSummaryLoading(false);
      }
    };
    fetchSummary();
  }, [collegeId]);

  return (
    <div className="space-y-6">
       <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5">
          <StatCard 
            icon={<Users className="h-5 w-5 text-muted-foreground" />} 
            label="Total Teachers" 
            value={summary?.totalTeachers ?? 0} 
            loading={summaryLoading} 
            error={!!summaryError} 
          />
          <StatCard 
            icon={<UserCheck className="h-5 w-5 text-muted-foreground" />} 
            label="Active Teachers" 
            value={summary?.activeTeachers ?? 0} 
            loading={summaryLoading} 
            error={!!summaryError} 
            iconClassName="bg-blue-100" 
            valueClassName="text-blue-600" 
          />
          <StatCard 
            icon={<FileText className="h-5 w-5 text-muted-foreground" />} 
            label="Total Question Papers" 
            value={summary?.totalQuestionPapers ?? 0} 
            loading={summaryLoading} 
            error={!!summaryError} 
            iconClassName="bg-green-100" 
            valueClassName="text-green-600" 
          />
          <StatCard 
            icon={<FileQuestion className="h-5 w-5 text-muted-foreground" />} 
            label="Total Questions" 
            value={summary?.totalQuestions ?? 0} 
            loading={summaryLoading} 
            error={!!summaryError} 
            iconClassName="bg-amber-100" 
            valueClassName="text-amber-600" 
          />
          <StatCard 
            icon={<Download className="h-5 w-5 text-muted-foreground" />} 
            label="Total Downloads" 
            value={summary?.totalDownloads ?? 0} 
            loading={summaryLoading} 
            error={!!summaryError} 
            iconClassName="bg-purple-100" 
            valueClassName="text-purple-600" 
          />
        </div>
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3 mb-8">
          {/* Important: Added overflow-hidden to prevent chart overflow */}
          <div className="lg:col-span-2 overflow-hidden">
            <SubjectQuestionsChart 
          title="Questions created per subject"
          collegeId={collegeId || ""}
          subjectColors={subjectColors}
          defaultTimeRange="Monthly"
          className="w-full h-full"
            />
          </div>
          <div className="lg:col-span-1">
            <TopTeachersList 
              title="Top Teachers Generating Papers"
              teachers={teachers}
              onTeacherClick={handleTeacherClick}
            />
          </div>
        </div>
    </div>
  );
}