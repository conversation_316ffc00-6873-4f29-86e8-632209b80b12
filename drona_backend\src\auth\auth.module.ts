// auth/auth.module.ts
import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { FirebaseAuthService } from './firebase-auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { User, UserSchema } from '../schema/user.schema';
import { College, CollegeSchema } from '../schema/college.schema';

@Module({
  imports: [
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: College.name, schema: CollegeSchema },
    ]),
  ],
  providers: [AuthService, FirebaseAuthService, JwtStrategy],
  controllers: [AuthController],
  exports: [AuthService, FirebaseAuthService],
})
export class AuthModule {}
