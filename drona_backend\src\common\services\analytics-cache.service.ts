import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * A simple in-memory cache service for analytics data.
 * This helps reduce the number of expensive MongoDB aggregation queries.
 */
@Injectable()
export class AnalyticsCacheService {
  private readonly logger = new Logger(AnalyticsCacheService.name);
  private readonly cache: Map<string, { data: any; timestamp: number }> =
    new Map();
  private readonly ttl: number; // Time to live in milliseconds

  constructor(private configService: ConfigService) {
    // Get TTL from config or use default (1 hour)
    const configTtl = this.configService.get<number>('CACHE_TTL');
    this.ttl = configTtl ? configTtl * 1000 : 3600000;
    this.logger.log(`Analytics cache initialized with TTL: ${this.ttl}ms`);
  }

  /**
   * Get data from cache if it exists and is not expired
   * @param key The cache key
   * @returns The cached data or null if not found or expired
   */
  get<T>(key: string): T | null {
    const cached = this.cache.get(key);

    if (!cached) {
      return null;
    }

    const now = Date.now();
    if (now - cached.timestamp > this.ttl) {
      // Cache expired, remove it
      this.cache.delete(key);
      return null;
    }

    return cached.data as T;
  }

  /**
   * Set data in the cache
   * @param key The cache key
   * @param data The data to cache
   */
  set(key: string, data: any): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  /**
   * Remove data from the cache
   * @param key The cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all cached data
   */
  clear(): void {
    this.cache.clear();
    this.logger.log('Analytics cache cleared');
  }

  /**
   * Get a value from the cache if it exists, or compute it and store it in the cache
   * @param key The cache key
   * @param fn The function to compute the value if not in cache
   * @returns The cached or computed value
   */
  async getOrCompute<T>(key: string, fn: () => Promise<T>): Promise<T> {
    const cached = this.get<T>(key);

    if (cached !== null) {
      return cached;
    }

    const computed = await fn();
    this.set(key, computed);
    return computed;
  }
}
