import { IsString, IsOptional, IsEnum } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO for college admins to update teacher information
 * Restricted to administrative fields only
 */
export class UpdateTeacherAdminDto {
  @ApiPropertyOptional({
    description: 'Teacher phone number',
    example: '+****************',
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Department the teacher belongs to',
    example: 'Computer Science',
  })
  @IsString()
  @IsOptional()
  department?: string;

  @ApiPropertyOptional({
    description: 'Teacher designation/title',
    example: 'Associate Professor',
  })
  @IsString()
  @IsOptional()
  designation?: string;

  @ApiPropertyOptional({
    description: 'User account status',
    example: 'active',
    enum: ['active', 'inactive'],
  })
  @IsEnum(['active', 'inactive'])
  @IsOptional()
  status?: string;

  @ApiPropertyOptional({
    description: 'User role',
    example: 'teacher',
    enum: ['teacher', 'collegeAdmin'],
  })
  @IsEnum(['teacher', 'collegeAdmin'])
  @IsOptional()
  role?: string;
}
