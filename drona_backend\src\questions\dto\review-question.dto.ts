import { IsEnum, IsOptional, IsString } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ReviewQuestionDto {
  @ApiProperty({
    description: 'Review status (approved or rejected)',
    example: 'approved',
    enum: ['approved', 'rejected'],
  })
  @IsEnum(['approved', 'rejected'], {
    message: 'Status must be either approved or rejected',
  })
  status: 'approved' | 'rejected';

  @ApiPropertyOptional({
    description: 'Optional notes for the review',
    example: 'Question meets quality standards',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
