import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';
import { Question } from '../schema/question.schema';
interface ParsedQuestion {
    content: string;
    options: string[];
    answer: string;
    imageUrls?: string[];
    difficulty: 'easy' | 'medium' | 'hard';
    type: string;
}
interface BulkUploadResult {
    questionsAdded: number;
    questionsFailed: number;
    questions: Question[];
    errors: string[];
}
export declare class MistralAiService {
    private readonly configService;
    private questionModel;
    private readonly logger;
    private readonly mistralClient;
    constructor(configService: ConfigService, questionModel: Model<Question>);
    processPdfWithOCR(file: Express.Multer.File): Promise<import("@mistralai/mistralai/models/components").OCRResponse>;
    parseQuestionsFromOCR(ocrResult: any): Promise<ParsedQuestion[]>;
    private getCombinedMarkdown;
    private replaceImagesInMarkdown;
    private parseQuestionBlockAdvanced;
    private detectOptionPattern;
    private extractOptionsAdvanced;
    private extractAnswerAdvanced;
    private parseQuestionsFromMarkdown;
    private splitQuestionsAdvanced;
    private isQuestionStart;
    private extractImagesFromMarkdown;
    bulkCreateQuestions(parsedQuestions: ParsedQuestion[], subjectId: string, userId: string): Promise<BulkUploadResult>;
    private normalizeQuestionContent;
    private checkForDuplicateQuestion;
}
export {};
