import { <PERSON><PERSON><PERSON>, OneTo<PERSON>any, PrimaryGeneratedColumn, Column } from 'typeorm';
import { QuestionPaper } from '../../question-papers/entities/question-paper.entity';

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  email: string;

  @OneToMany(() => QuestionPaper, (questionPaper) => questionPaper.generatedBy)
  questionPapers: QuestionPaper[];
}
