import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { QuestionPapersService } from './question-papers.service';
import { QuestionPapersController } from './question-papers.controller';
import {
  QuestionPaper,
  QuestionPaperSchema,
} from '../schema/question-paper.schema';
import { Question, QuestionSchema } from '../schema/question.schema';
import { Subject, SubjectSchema } from '../schema/subject.schema';
import { QuestionUsageModule } from '../question-usage/question-usage.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: QuestionPaper.name, schema: QuestionPaperSchema },
      { name: Question.name, schema: QuestionSchema },
      { name: Subject.name, schema: SubjectSchema },
    ]),
    QuestionUsageModule,
  ],
  controllers: [QuestionPapersController],
  providers: [QuestionPapersService],
  exports: [QuestionPapersService],
})
export class QuestionPapersModule {}
