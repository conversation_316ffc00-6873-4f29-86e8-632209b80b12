# Image Upload and Compression System

This document describes the integrated image upload and compression system within the Questions API that replaces AWS S3 integration.

## Overview

The system supports large image uploads (up to 100MB) with automatic compression to a maximum of 2MB while maintaining good quality. Images are uploaded directly through the Questions API endpoints and can be stored locally on the server or directly in the database as base64.

## Features

- ✅ **Integrated with Questions API** - Upload images directly when creating/updating questions
- ✅ **Large image support** - up to 100MB upload, automatically compressed to 2MB output
- ✅ **Base64 support** - Large images can be uploaded as base64 and stored directly in database
- ✅ **Quality preservation** using smart compression algorithms
- ✅ **Multiple formats** supported (JPEG, PNG, WebP)
- ✅ **Flexible storage** - local storage or direct database storage
- ✅ **Role-based access** control (Super Admin only)
- ✅ **Batch upload** - up to 5 images per question

## API Endpoints

### Create Question with Images
```http
POST /questions
Content-Type: multipart/form-data
Authorization: Bearer <jwt-token>
```

**Body (Form Data):**
- `content` (string): Question content/text
- `options` (array): Answer options for the question
- `answer` (string): Correct answer
- `subjectId` (string): Subject ID this question belongs to
- `topicId` (string, optional): Topic ID this question belongs to
- `difficulty` (string): Question difficulty (easy/medium/hard)
- `type` (string): Question type (multiple-choice/true-false/descriptive)
- `tags` (array, optional): Tags for categorizing the question
- `images` (files, optional): Up to 5 image files (JPEG/PNG/WebP, max 100MB each, compressed to 2MB and stored in database)

**Response:**
```json
{
  "_id": "60d21b4667d0d8992e610c85",
  "content": "What is the capital of France?",
  "options": ["Paris", "London", "Berlin", "Madrid"],
  "answer": "Paris",
  "imageUrls": [
    "http://localhost:3000/uploads/images/abc123-def456.jpg",
    "http://localhost:3000/uploads/images/xyz789-ghi012.jpg"
  ],
  "subjectId": "60d21b4667d0d8992e610c86",
  "difficulty": "medium",
  "type": "multiple-choice",
  "reviewStatus": "pending",
  "createdAt": "2024-01-15T10:30:00.000Z"
}
```

### Update Question with Images
```http
PATCH /questions/{id}
Content-Type: multipart/form-data
Authorization: Bearer <jwt-token>
```

**Body (Form Data):**
- Same fields as create endpoint
- `images` (files, optional): New image files to add (existing images are preserved)

## Usage Examples

### Frontend Upload (JavaScript)
```javascript
// Create question with images
const formData = new FormData();
formData.append('content', 'What is the capital of France?');
formData.append('options', JSON.stringify(['Paris', 'London', 'Berlin', 'Madrid']));
formData.append('answer', 'Paris');
formData.append('subjectId', '60d21b4667d0d8992e610c85');
formData.append('difficulty', 'medium');
formData.append('type', 'multiple-choice');

// Add image files
const imageFiles = document.getElementById('imageInput').files;
for (let i = 0; i < imageFiles.length; i++) {
  formData.append('images', imageFiles[i]);
}

const response = await fetch('/questions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

const result = await response.json();
console.log('Created question with images:', result.imageUrls);
```

```javascript
// Update question with new images
const formData = new FormData();
formData.append('content', 'Updated question content');

// Add new image files (existing images will be preserved)
const newImageFiles = document.getElementById('newImageInput').files;
for (let i = 0; i < newImageFiles.length; i++) {
  formData.append('images', newImageFiles[i]);
}

const response = await fetch(`/questions/${questionId}`, {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});
```

### Backend Integration
```typescript
// The image compression is automatically handled in the Questions service
// When creating questions with images:
const question = await this.questionsService.create(createQuestionDto, user, imageFiles);

// When updating questions with new images:
const updatedQuestion = await this.questionsService.update(id, updateQuestionDto, imageFiles);

// Images are automatically compressed to 2MB and URLs are added to the question
console.log('Question image URLs:', question.imageUrls);
```

## Configuration

Add these environment variables to your `.env` file:

```env
# Base URL for generating image URLs
BASE_URL=http://localhost:3000

# Request Size Limits (for large base64 images)
MAX_JSON_SIZE=50mb
MAX_URL_ENCODED_SIZE=50mb

# Optional: Custom upload directory (default: uploads/images)
UPLOADS_DIR=uploads/images
```

## File Structure

```
uploads/
└── images/
    ├── abc123-def456.jpg
    ├── xyz789-ghi012.jpg
    └── ...
```

## Compression Algorithm

The system uses the following approach:

1. **Initial resize** if image exceeds maximum dimensions
2. **Iterative compression** with quality reduction until target size is reached
3. **Smart quality adjustment** (minimum 20% quality to prevent over-compression)
4. **Format optimization** (JPEG for photos, PNG for graphics)

## Security

- ✅ **File type validation** - Only image files allowed
- ✅ **Size limits** - 100MB max upload, 2MB max output for optimal storage
- ✅ **Role-based access** - Only authenticated users can upload
- ✅ **Filename sanitization** - UUIDs prevent conflicts
- ✅ **No executable files** - Only image processing
- ✅ **Base64 support** - Large images can be uploaded as base64 and stored directly in database

## Migration from AWS S3

The system automatically handles the transition:

1. **Mistral AI OCR** now uses local compression instead of S3
2. **Existing S3 URLs** continue to work (if still configured)
3. **New uploads** use local storage
4. **No data loss** during migration

## Performance

- **Compression speed**: ~100-500ms per image
- **Storage efficiency**: 60-80% size reduction typical
- **Memory usage**: Optimized streaming for large files
- **Concurrent uploads**: Supported with proper scaling

## Troubleshooting

### Common Issues

1. **"No image file provided"**
   - Ensure `image` field is included in form data
   - Check file input has a selected file

2. **"Invalid image type"**
   - Only JPEG, PNG, and WebP are supported
   - Check file MIME type

3. **"Image compression failed"**
   - File may be corrupted
   - Try with a different image

### Logs

Check application logs for detailed compression information:
```
[ImageCompressionService] Processing image: photo.jpg (5.2 MB)
[ImageCompressionService] Attempt 1: Size 3.1 MB, reducing quality to 75
[ImageCompressionService] Image compressed successfully: photo.jpg -> abc123.jpg (5.2 MB -> 1.8 MB, 65.4% reduction)
```
