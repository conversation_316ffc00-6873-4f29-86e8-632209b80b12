import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { QuestionsService } from './questions.service';
import { QuestionsController } from './questions.controller';
import { Question, QuestionSchema } from '../schema/question.schema';
import { MistralAiModule } from '../mistral-ai/mistral-ai.module';
import { ImageCompressionService } from '../common/services/image-compression.service';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Question.name, schema: QuestionSchema },
    ]),
    MistralAiModule,
  ],
  controllers: [QuestionsController],
  providers: [QuestionsService, ImageCompressionService],
  exports: [QuestionsService],
})
export class QuestionsModule {}
