import React from 'react';
import { extractImagesFromText } from '@/utils/imageUtils';
import { Base64Image } from './base64-image';
import { cn } from '@/lib/utils';

interface TextWithImagesProps {
  text: string;
  className?: string;
  imageClassName?: string;
  maxImageWidth?: number;
  maxImageHeight?: number;
}

export function TextWithImages({
  text,
  className,
  imageClassName,
  maxImageWidth = 300,
  maxImageHeight = 200
}: TextWithImagesProps) {
  const { cleanText, images } = extractImagesFromText(text);

  return (
    <div className={cn("space-y-3", className)}>
      {/* Render the cleaned text */}
      {cleanText && (
        <div className="text-base">
          {cleanText}
        </div>
      )}
      
      {/* Render extracted images */}
      {images.length > 0 && (
        <div className="space-y-2">
          {images.map((image) => (
            <div key={image.id} className="flex flex-col space-y-1">
              <Base64Image
                src={image.src}
                alt={image.alt}
                className={imageClassName}
                maxWidth={maxImageWidth}
                maxHeight={maxImageHeight}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
