// auth/firebase-auth.service.ts
import {
  Injectable,
  Logger,
  OnModuleInit,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';
import * as path from 'path';

@Injectable()
export class FirebaseAuthService implements OnModuleInit {
  private readonly logger = new Logger(FirebaseAuthService.name);
  private initialized = false;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    await this.initializeFirebaseAdmin();
  }

  private async initializeFirebaseAdmin(): Promise<void> {
    try {
      // Initialize Firebase Admin SDK if not already initialized
      if (!admin.apps.length) {
        // Check if service account file path is provided
        const serviceAccountPath = this.configService.get<string>(
          'FIREBASE_SERVICE_ACCOUNT_PATH',
        );

        if (serviceAccountPath) {
          // Use service account file
          const serviceAccountFilePath = serviceAccountPath.startsWith('/')
            ? serviceAccountPath
            : path.join(process.cwd(), serviceAccountPath);

          this.logger.log(
            `Initializing Firebase Admin SDK with service account file: ${serviceAccountFilePath}`,
          );

          admin.initializeApp({
            credential: admin.credential.cert(serviceAccountFilePath),
          });
        } else {
          // Fallback to environment variables
          const projectId = this.configService.get<string>(
            'FIREBASE_PROJECT_ID',
          );
          const clientEmail = this.configService.get<string>(
            'FIREBASE_CLIENT_EMAIL',
          );
          const privateKey = this.configService.get<string>(
            'FIREBASE_PRIVATE_KEY',
          );

          // Validate environment variables
          if (!projectId || !clientEmail || !privateKey) {
            this.logger.error(
              'Missing Firebase credentials in environment variables',
            );
            throw new Error('Firebase credentials not properly configured');
          }

          admin.initializeApp({
            credential: admin.credential.cert({
              projectId,
              clientEmail,
              // Handle the private key replacement safely
              privateKey: privateKey.replace(/\\n/g, '\n'),
            }),
          });
        }

        this.initialized = true;
        this.logger.log('Firebase Admin SDK initialized successfully');
      } else {
        this.initialized = true;
      }
    } catch (error) {
      this.logger.error(
        `Failed to initialize Firebase Admin SDK: ${error.message}`,
      );
      // We'll continue without Firebase, but operations will fail
      this.initialized = false;
    }
  }

  /**
   * Verify a Firebase ID token
   * @param token The Firebase ID token to verify
   * @returns The decoded token
   * @throws UnauthorizedException if the token is invalid or Firebase is not initialized
   */
  async verifyToken(token: string): Promise<admin.auth.DecodedIdToken> {
    if (!this.initialized) {
      await this.initializeFirebaseAdmin();
      if (!this.initialized) {
        throw new UnauthorizedException(
          'Firebase authentication is not available',
        );
      }
    }

    try {
      const decodedToken = await admin.auth().verifyIdToken(token, true);
      return decodedToken;
    } catch (error) {
      this.logger.error(`Failed to verify Firebase token: ${error.message}`);
      throw new UnauthorizedException('Invalid or expired Firebase token');
    }
  }

  /**
   * Get a user by their Firebase UID
   * @param uid The Firebase UID
   * @returns The user record
   * @throws UnauthorizedException if the user is not found or Firebase is not initialized
   */
  async getUserByUid(uid: string): Promise<admin.auth.UserRecord> {
    if (!this.initialized) {
      await this.initializeFirebaseAdmin();
      if (!this.initialized) {
        throw new UnauthorizedException(
          'Firebase authentication is not available',
        );
      }
    }

    try {
      // Ensure uid is a string
      const stringUid = String(uid);
      return await admin.auth().getUser(stringUid);
    } catch (error) {
      this.logger.error(`Failed to get user by UID: ${error.message}`);
      throw new UnauthorizedException('User not found in Firebase');
    }
  }
}
