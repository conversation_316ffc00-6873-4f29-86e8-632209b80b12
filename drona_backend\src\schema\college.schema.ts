// college.schema.ts
import { Prop, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export type CollegeDocument = College & Document;

@Schema({ timestamps: true })
export class College {
  @ApiProperty({
    description: 'College name',
    example: 'Harvard University',
    required: true,
  })
  @Prop({ required: true })
  name: string;

  @ApiPropertyOptional({
    description: 'College address',
    example: '123 Main St, Cambridge, MA 02138',
  })
  @Prop()
  address: string;

  @ApiPropertyOptional({ description: 'City', example: 'Cambridge' })
  @Prop()
  city: string;

  @ApiPropertyOptional({
    description: 'State/Province',
    example: 'Massachusetts',
  })
  @Prop()
  state: string;

  @ApiPropertyOptional({ description: 'Country', example: 'USA' })
  @Prop()
  country: string;

  @ApiPropertyOptional({ description: 'Postal/ZIP code', example: '02138' })
  @Prop()
  postalCode: string;

  @ApiPropertyOptional({
    description: 'Contact email address',
    example: '<EMAIL>',
  })
  @Prop()
  contactEmail: string;

  @ApiPropertyOptional({
    description: 'Contact phone number',
    example: '+****************',
  })
  @Prop()
  contactPhone: string;

  @ApiPropertyOptional({
    description: 'College website URL',
    example: 'https://www.harvard.edu',
  })
  @Prop()
  website: string;

  @ApiPropertyOptional({
    description: 'College logo URL',
    example: 'https://example.com/logo.png',
  })
  @Prop()
  logoUrl: string;

  @ApiProperty({
    description: 'College status',
    example: 'active',
    enum: ['active', 'inactive'],
    default: 'active',
  })
  @Prop({ default: 'active', enum: ['active', 'inactive'] })
  status: string;

  @ApiPropertyOptional({
    description: 'Array of teacher email addresses authorized for this college',
    example: ['<EMAIL>', '<EMAIL>'],
    type: [String],
  })
  @Prop({ type: [String], default: [] })
  teachers: string[];

  @ApiPropertyOptional({
    description: 'Array of college admin email addresses authorized for this college',
    example: ['<EMAIL>', '<EMAIL>'],
    type: [String],
  })
  @Prop({ type: [String], default: [] })
  admins: string[];
}

export const CollegeSchema = SchemaFactory.createForClass(College);
